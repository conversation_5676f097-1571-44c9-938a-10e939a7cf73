const mongoose = require('mongoose')

const CompanyData = require('./../dtos/CompanyData')

const schema = new mongoose.Schema({
  name: String,
  socket_code: String,
  is_active: {
    type: Boolean,
    default: true
  },
  phone_number: String,
  company_code: String,
  shopify_info_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ShopifyInfo',
    alias: 'shopify_info'
  },
  reseller_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Reseller',
    alias: 'reseller'
  },
  data: mongoose.Schema.Types.Mixed,
  waba_id: String,
  shopify: String,
  certificate: String,
  arvia_project_id: String,
  tsoft_app_id: String,
  tsoft_domain: String,
  billtekrom_account_id: Number,
  is_demo: {
    type: Boolean,
    default: true
  }
}, {
  collection: 'companies',
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
})

schema.virtual('vSocketCode').get(function () {
  return 'company-' + this.socket_code
})

schema.virtual('vPhoneNumber').get(function () {
  return this.phone_number || '************' // Duygu şirket hattı
})

schema.virtual('vData').get(function () {
  return new CompanyData(this.data)
})

schema.index({ shopify_info_id: 1 })
schema.index({ billtekrom_account_id: 1 })

module.exports = mongoose.model('Company', schema)
