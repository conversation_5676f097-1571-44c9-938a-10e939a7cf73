const mongoose = require('mongoose')

const schema = new mongoose.Schema({

	issued_fb_user_id: {
		type: String,
		required: true
	},
	user_access_token: {
		type: String,
		required: true
	},
	is_active: {
		type: Boolean,
		default: true
	},
	scopes: {
		type: Array,
		required: true
	},
	company_id: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'Company',
		alias: 'company'
	},
	deleted_at: mongoose.Schema.Types.Date
}, {
	collection: 'meta_ads_users',
	timestamps: {
		createdAt: 'created_at',
		updatedAt: 'updated_at'
	}
})


module.exports = mongoose.model('MetaAdsUsers', schema)
