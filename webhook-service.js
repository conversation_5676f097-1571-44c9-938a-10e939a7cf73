require('dotenv').config()

const pino = require('pino')()
const cors = require('cors')
const nocache = require('nocache')
const express = require('express')
const mongoose = require('mongoose')

const i18next = require('./i18next')

const utils = require('./libs/utils')
const helpers = require('./libs/helpers')

const WebhookAppController = require('./controllers/Webhook')

Promise.resolve()
  .then(() => {

    pino.info({
      message: 'i18next inited',
      timestamp: new Date()
    })

    return helpers.retryProcessOnError(() => mongoose.connect(process.env.MONGODB_CONN_STRING))

  })
  .then(() => {
    pino.info({
      message: 'mongodb connected',
      timestamp: new Date()
    })

    const app = express()

    app.models = {
      AclRole: require('./models/AclRole'),
      AclAction: require('./models/AclAction'),
      AclResource: require('./models/AclResource'),
      AclRoleResource: require('./models/AclRoleResource'),
      AclResourceAction: require('./models/AclResourceAction'),

      Job: require('./models/Job'),
      Log: require('./models/Log'),
      File: require('./models/File'),
      User: require('./models/User'),
      Chat: require('./models/Chat'),
      Token: require('./models/Token'),
      Config: require('./models/Config'),
      Message: require('./models/Message'),
      Channel: require('./models/Channel'),
      Company: require('./models/Company'),
      Container: require('./models/Container'),
      LoginWith: require('./models/LoginWith'),
      WebpImage: require('./models/WebpImage'),
      Integration: require('./models/Integration'),
      FormMessage: require('./models/FormMessage'),
      WebPushToken: require('./models/WebPushToken'),
      WebhookBaseUrl: require('./models/WebhookBaseUrl'),
      UserPermission: require('./models/UserPermission'),
      ChatIntegration: require('./models/ChatIntegration'),
      OnboardingWizard: require('./models/OnboardingWizard')
    }

    app.services = {
      JobService: require('./services/JobService'),
      UserService: require('./services/UserService'),
      ChatService: require('./services/ChatService'),
      QueueService: require('./services/QueueService'),
      IntegrationService: require('./modules/AgentApp/IntegrationService'),
      LogService: require('./services/LogService'),
      TsoftAgentAppService: require('./integrations/Tsoft/AgentApp/TsoftService'),
      TsoftIntegrationService: require('./integrations/Tsoft/TsoftIntegrationService'),
      ShopifyIntegrationService: require('./integrations/Shopify/ShopifyIntegrationService')
    }

    app.set('etag', false)
    app.set('trust proxy', 'loopback')

    app.use(cors())
    app.use(express.urlencoded({
      extended: true
    }))
    app.use(express.json())
    app.use(nocache())

    app.use((req, res, next) => {
      req.trace_id = utils.generateHash(30)

      pino.info({
        message: `trace_id generated`,
        trace_id: req.trace_id,
        method: req.method,
        url: `${req.protocol}://${req.hostname}:${process.env.APP_PORT}${req.originalUrl}`,
        timestamp: new Date(),
        request_body: JSON.stringify(req.body),
        request_params: JSON.stringify(req.params),
        request_query: JSON.stringify(req.query),
        request_headers: JSON.stringify(req.headers)
      })
      res.modifiedResponse = function (statusCode, response) {

        pino.info({
          trace_id: req.trace_id,
          response: JSON.stringify(response),
          url: `${req.protocol}://${req.hostname}:${process.env.APP_PORT}${req.originalUrl}`,
          timestamp: new Date()
        })

        return res.status(statusCode).json({
          trace_id: req.trace_id,
          ...response
        })
      }
      next()
    })

    app.use((req, res, next) => {

      req.i18n = {
        language: req.headers['x-app-lang-code'],
        changeLanguage: (value) => {
          req.i18n.language = value
        },
        store: {
          data: i18next.store.data
        }
      }

      req.t = (value, data = {}) => i18next.t(value, { lng: req.i18n.language, ...data })

      next()
    })

    app.get('/health', (req, res, next) => {
      return res.modifiedResponse(200, { success: true })
    })

    // WebHook controller
    app.all('/webhook/facebook', WebhookAppController.Facebook)
    app.all('/webhook/whatsapp', WebhookAppController.Whatsapp)
    app.post('/webhook/whatsapp/:code', WebhookAppController.WhatsappCode) // container'lar doğrudan istek atıyor
    app.all('/webhook/instagram', WebhookAppController.Instagram)
    app.all('/webhook/tsoft', WebhookAppController.Tsoft)
    app.post('/webhook/thinker', WebhookAppController.Thinker)
    app.post('/webhook/telegram', WebhookAppController.Telegram)
    app.all('/webhook/shopify', WebhookAppController.Shopify)
    app.post('/webhook/mail', WebhookAppController.Mail)
    app.post('/webhook/billtekrom', WebhookAppController.BillTekrom)
    app.all('/webhook/template-message', WebhookAppController.TemplateMessage)
    app.post('/webhook/arvia-meet-ended', WebhookAppController.ArviaMeetEnded)
    app.post('/shopify/data-request', WebhookAppController.ShopifyDataRequest)
    app.post('/shopify/customer-delete', WebhookAppController.ShopifyCustomerDelete)
    app.post('/shopify/app-uninstall', WebhookAppController.ShopifyAppUninstall)
    app.get('/google/drive/callback', WebhookAppController.GoogleDriveCallback)

    app.use((err, req, res, next) => {
      helpers.handleError(req, err, res)
    })

    return app

  }).then(async app => {

    const RabbitMQConnection = require('./modules/RabbitMQConnection')

    await RabbitMQConnection.connection()

    const port = 3041

    app.listen(port, () => pino.info({
      message: `Webhook-Service listening on port ${port}!`,
      timestamp: new Date()
    }))

  })
  .catch(error => {

    console.log(error)

  })