FROM node:18.18.2 as builder

ARG SERVICE_NAME=local
ARG SERVICE_PORT=local
ENV SERVICE_NAME ${SERVICE_NAME}
ENV SERVICE_PORT ${SERVICE_PORT}

WORKDIR /home/<USER>

COPY . .

RUN rm -rf node_modules && rm -rf .env
RUN npm install -g @vercel/ncc
RUN npm install
RUN ncc build ${SERVICE_NAME}.js -o dist

# App
FROM node:18.18.2-alpine3.15 as release

ARG SERVICE_NAME=local
ARG SERVICE_PORT=local
ENV SERVICE_NAME ${SERVICE_NAME}
ENV SERVICE_PORT ${SERVICE_PORT}

WORKDIR /home/<USER>

COPY --from=builder /home/<USER>/dist .

EXPOSE ${SERVICE_PORT}

CMD [ "node", "index.js" ]

# docker build --tag helorobo-backend --build-arg SERVICE_NAME=backend --build-arg SERVICE_PORT=3000 .
# docker build --tag helorobo-worker-service --build-arg SERVICE_NAME=worker-service --build-arg SERVICE_PORT=3021 .
# docker build --tag helorobo-webhook-service --build-arg SERVICE_NAME=webhook-service --build-arg SERVICE_PORT=3041 .