const moment = require('moment')
const slugify = require('slugify')
const { default: axios } = require('axios')
const { RandomHash } = require('random-hash')
const pino = require('pino')()
const mongoose = require('mongoose')

const enums = require('../libs/enums')
const utils = require('../libs/utils')
const helpers = require('../libs/helpers')

const Job = require('../models/Job')
const Message = require('../models/Message')
const UserIntegration = require('../models/UserIntegration')
const CustomerEvaluationReport = require('../models/CustomerEvaluationReport')
const HelobotHasChat = require('../models/HelobotHasChat')
const ThinkerHasChat = require('../models/ThinkerHasChat')
const User = require('../models/User')
const AgentSession = require('../models/AgentSession')
const TeamHasAgent = require('../models/TeamHasAgent')
const Team = require('../models/Team')
const CompletedOrder = require('../models/CompletedOrder')
const Company = require('../models/Company')

const BotActionJob = require('./AgentApp/BotActionJob')
const UserIntegrationJob = require('./AgentApp/UserIntegrationJob')

const SendOowhMessage = require('../modules/SendOowhMessage')
const SendFirstMessage = require('../modules/AgentApp/BotAction/SendFirstMessage')
const RedisConnection = require('../modules/RedisConnection')
const SendOrderStatusMessage = require('../modules/AgentApp/BotAction/SendOrderStatusMessage')
const GetInteractiveData = require('../modules/AgentApp/BotAction/GetInteractiveData')
const GetBotData = require('../modules/AgentApp/BotAction/GetBotData')

const AgentReport = require('../services/Report/AgentReports')
const ChatService = require('../services/ChatService')
const QueueService = require('../services/QueueService')
const ThinkerService = require('../services/ThinkerService')
const ChatActions = require('../services/ChatActions')
const HeloBotService = require('../services/HelobotService')
const ChatReplyReportService = require('../services/ChatReplyReportService')

const WhatsappMessageDto = require('../integrations/Whatsapp/SDK/WhatsappMessageDto')
const WhatsappCloudMessageDto = require('../integrations/Whatsapp/CloudSDK/WhatsappCloudMessageDto')
const FacebookMessageDto = require('../integrations/Facebook/SDK/FacebookMessageDto')
const InstagramMessageDto = require('../integrations/Instagram/SDK/InstagramMessageDto')
const TelegramMessageDto = require('../integrations/Telegram/SDK/TelegramMessageDto')
const MobileService = require('../integrations/Firebase/MobileService')
const TsoftIntegrationService = require('../integrations/Tsoft/TsoftIntegrationService')

const runWhatsappIncomingMessageJob = require('../integrations/Whatsapp/runWhatsappIncomingMessageJob')
const runWhatsappCloudIncomingMessageJob = require('../integrations/Whatsapp/runWhatsappCloudIncomingMessageJob')
const runLiveChatIncomingMessageJob = require('../integrations/LiveChat/runLiveChatIncomingMessageJob')
const runFacebookIncomingMessageJob = require('../integrations/Facebook/runFacebookIncomingMessageJob')
const runInstagramIncomingMessageJob = require('../integrations/Instagram/runInstagramIncomingMessageJob')
const runTelegramIncomingMessageJob = require('../integrations/Telegram/runTelegramIncomingMessageJob')

const DashPresenter = require('../presenters/Dash')


/**
 * @param req
 * @param {CreatedChatMessage} createdChatMessage
 *
 * @return {Promise<void>}
 */
const __onCreatedMessage = async (req, createdChatMessage, botData) => {

  pino.info({
    trace_id: req.trace_id,
    message: 'Frontend e Mesaj Bildirimi Gönderilecek',
    timestamp: new Date()
  })

  let isSendUnArchivedMessage = false

  // chat eğer pinlenmiş ise bekleyene düşme işlemi yapılmayacak
  if (!createdChatMessage.getChat().pinned_at) {
    ChatService.queueCheckNotAnswered(req, createdChatMessage.getMessage(), createdChatMessage.getChat())
  }

  //kullanıcının üye olup olmadığı bilgisi

  const hasUserIntegration = await createdChatMessage.hasUserIntegration()

  const hasThinker = await ThinkerHasChat.findOne({ chat_id: createdChatMessage.conversation._id, status: true })

  let chatItem = DashPresenter.getChatItem(createdChatMessage.conversation, createdChatMessage.message, createdChatMessage.channel, undefined, createdChatMessage.getChat().thinker_status, createdChatMessage.getChat().helobot_status)

  const agentMessage = await User.findById(createdChatMessage.message.user_id)

  const messageItem = await DashPresenter.getRepliedMessage(createdChatMessage.message, agentMessage)

  chatItem.is_paired = hasUserIntegration

  const agent = await User.findById(createdChatMessage.conversation.owner_user_id)

  const socketRooms = createdChatMessage.getCompany().vData.getTeamStatus() && createdChatMessage.getChat().team_id ? [createdChatMessage.getChat().team_id.toString()] : [createdChatMessage.getChannel().id]

  // mesaja ait conversation arşiv durumunda ise öncelikle conversation arşivden çıkartılmalı
  if (createdChatMessage.conversation.vIsArchived) {

    // akış devam etmemeli.
    if (hasThinker) {
      return isSendUnArchivedMessage
    }

    // eğer müşteri değerlendirme raporu ise unarvice işlemleri çalışmayacak
    if (typeof botData === 'object') {
      if (botData.agent_from_type === enums.message_from_types.SYSTEM) {
        if (botData.agent_message.content.system_next_action === enums.SYSTEM_BOT_MESSAGE_ACTION.CUSTOMER_EVALUATION_MESSAGE) {
          return isSendUnArchivedMessage
        }
      }
    }

    // mesaj arşivlendi ve arşiv sonrası mesaj gönderme ayarı aktifse unarchive mesajı gönder
    if (createdChatMessage.getChannel().vSettings.getIsActiveUnarchiveMessaging() && !createdChatMessage.isReferralMessage()) {
      isSendUnArchivedMessage = true

      const status = await __handleWorkingHours(req, createdChatMessage)
      if (!status) {
        await ChatService.unarchiveMessage(req, createdChatMessage, chatItem)
      }
    }

    if (!createdChatMessage.getCompany().vData.getAssignChatToAgent()) {
      createdChatMessage.conversation.owner_user_id = undefined
      createdChatMessage.conversation.owner_user = false
    }

    createdChatMessage.conversation.archived_at = undefined
    createdChatMessage.conversation.archived = false
    await createdChatMessage.conversation.save()

    createdChatMessage.getChat().channel = createdChatMessage.getChannel()

    if (!createdChatMessage.conversation.thinker_status && !createdChatMessage.conversation.helobot_status) {
      // chat arşivden çıktı bilgisi herkese gidiyor.
      await QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.CHAT_UNARCHIVED_TO_AGENT,
        socket_rooms: socketRooms,
        data: {
          chat_id: createdChatMessage.getChat().id
        }
      }, req.i18n.language)

      if (createdChatMessage.conversation.owner_user_id) {
        // Chati alacak agenta chat hakkında bilgiler gönderilir
        await QueueService.publishToAppSocket({
          event: enums.agent_app_socket_events.CHAT_ASSIGNED_TO_AGENT,
          socket_rooms: [agent.vSocketCode],
          data: {
            chat_item: chatItem
          }
        }, req.i18n.language)
      } else {
        // agent için bir event gönderelim
        await QueueService.publishToAppSocket({
          event: enums.agent_app_socket_events.CHAT_UNARCHIVED,
          socket_rooms: socketRooms,
          data: {
            chat_item: chatItem,
            message_item: messageItem,
          }
        }, req.i18n.language)
      }
    }
  }

  // conversation zaten bir agent'a ait ve arşivde değilse agent'ın private'ında demektir. Bu durumda agent'a
  // bilgi gönderilir ve agentın müşteri ile ilgilenmesi sağlanır.

  const lastOrder = await CompletedOrder.findOne({ chat_ext_id: createdChatMessage.getChat().ext_id }).sort({ _id: -1 })

  if (createdChatMessage.conversation.owner_user_id) {
    QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.MESSAGE_RECEIVED_TO_OWNED_CHAT,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_item: chatItem,
        message_item: messageItem,
        notification_sound_status: createdChatMessage.channel.vSettings.getNotificationSoundStatus()
      }
    }, req.i18n.language)


    if (agent) {
      if (agent.vData.getIsMobileNotificationsActive() && agent.vData.getMobileToken()) {
        MobileService.SendMessage(enums.agent_app_socket_events.MESSAGE_RECEIVED_TO_OWNED_CHAT, createdChatMessage.channel.id, createdChatMessage.conversation, createdChatMessage.message, true, agent.vData.getMobileToken()).catch(err => {
          pino.error({
            trace_id: req.trace_id,
            data: JSON.stringify({
              event: enums.agent_app_socket_events.MESSAGE_RECEIVED_TO_OWNED_CHAT,
              socket_rooms: [createdChatMessage.channel.id],
              chat_id: createdChatMessage.getChat().id
            }),
            message: 'Mobile Bildirim Gönderileceği Zaman Hata Verdi. -- ' + JSON.stringify(err),
            timestamp: new Date()
          })
        })
      }

      ChatService.sendOnlineWatcherEvent(createdChatMessage.getCompany()._id, createdChatMessage.conversation.id, agent.id, enums.agent_app_socket_events.ONLINE_WATCHER_MESSAGE_RECEIVED_TO_OWNED_CHAT, { message_item: messageItem })
    }

    if (moment().diff(createdChatMessage.getMessage().created_at, 'hours') >= 24 || moment().diff(lastOrder?.created_at, 'hours') >= 24) {
      if (createdChatMessage.company.vData.getTeamStatus() && createdChatMessage.channel.vSettings.getTeamMessageBeforeOrder()) {
        await ChatService.teamMessage(req, createdChatMessage.getChat(), createdChatMessage.getChannel(), createdChatMessage.getCompany(), helpers.getFirstWelcomeMessage(req, createdChatMessage.getChannel().vSettings))
      }
    }

    pino.info({
      trace_id: req.trace_id,
      message: 'Frontend e Mesaj Bildirimi Gönderildi',
      timestamp: new Date()
    })

    return isSendUnArchivedMessage
  }

  if (createdChatMessage.conversation.thinker_status || createdChatMessage.conversation.helobot_status) {
    const event = createdChatMessage.conversation.thinker_status ? enums.agent_app_socket_events.NEW_THINKER_PUBLIC_MESSAGE_RECEVIED : enums.agent_app_socket_events.NEW_HELOBOT_PUBLIC_MESSAGE_RECEVIED

    await QueueService.publishToAppSocket({
      event: event,
      socket_rooms: socketRooms,
      data: {
        chat_item: chatItem,
        message_item: messageItem,
        notification_sound_status: createdChatMessage.channel.vSettings.getNotificationSoundStatus()
      }
    }, req.i18n.language)

    return isSendUnArchivedMessage
  }

  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.NEW_PUBLIC_MESSAGE_RECEVIED,
    socket_rooms: socketRooms,
    data: {
      chat_item: chatItem,
      message_item: messageItem,
      notification_sound_status: createdChatMessage.channel.vSettings.getNotificationSoundStatus()
    }
  }, req.i18n.language)

  if (moment().diff(createdChatMessage.getMessage().created_at, 'hours') >= 24 || moment().diff(lastOrder?.created_at, 'hours') >= 24) {
    if (createdChatMessage.company.vData.getTeamStatus() && createdChatMessage.channel.vSettings.getTeamMessageBeforeOrder()) {
      await ChatService.teamMessage(req, createdChatMessage.getChat(), createdChatMessage.getChannel(), createdChatMessage.getCompany(), helpers.getFirstWelcomeMessage(req, createdChatMessage.getChannel().vSettings))
    }
  }

  MobileService.SendMessage(enums.agent_app_socket_events.NEW_PUBLIC_MESSAGE_RECEVIED, socketRooms[0], createdChatMessage.conversation, createdChatMessage.message, false).catch(err => {
    pino.error({
      trace_id: req.trace_id,
      data: JSON.stringify({
        event: enums.agent_app_socket_events.NEW_PUBLIC_MESSAGE_RECEVIED,
        socket_rooms: socketRooms,
        chat_id: createdChatMessage.getChat().id
      }),
      message: 'Mobile Bildirim Gönderileceği Zaman Hata Verdi. -- ' + JSON.stringify(err),
      timestamp: new Date()
    })
  })

  pino.info({
    trace_id: req.trace_id,
    message: 'Frontend e Mesaj Bildirimi Gönderildi',
    timestamp: new Date()
  })

  return isSendUnArchivedMessage
}

/**
 * @param req
 * @param {CreatedChatMessage} createdChatMessage
 *
 * @private
 */
const __selectChatLanguage = async (req, createdChatMessage) => {

  const chatData = createdChatMessage.getChat().vData

  // Chat içersinde dil bilgisi yoksa default olarak tr kaydediliyor
  if (!chatData.getChatLangCode()) {

    chatData.setChatLangCode('tr')

    createdChatMessage.getChat().data = chatData.getData()
    createdChatMessage.getChat().markModified('data')

    await createdChatMessage.getChat().save()

    req.i18n.changeLanguage('tr')

    return req.i18n.language

  }

  req.i18n.changeLanguage(chatData.getChatLangCode())

  return req.i18n.language

}

const __createCustomerClientId = async (createdChatMessage) => {

  if (createdChatMessage.getIntegration()?.type === enums.INTEGRATION_TYPES.HELOSCOPE) {

    const chatIntegrationvData = createdChatMessage.getChatIntegration().vData
    const generateHash = new RandomHash({
      length: 25,
      charset: 'abcdefghijklmnopqrstuvwxyzABCDEF'
    })

    if (!chatIntegrationvData.getClientId()) {
      chatIntegrationvData.setClientId(generateHash())
      createdChatMessage.getChatIntegration().data = chatIntegrationvData.getData()
      createdChatMessage.chatIntegration.markModified('data')
      await createdChatMessage.getChatIntegration().save()
    }

  }

}

/**
 * @param req
 * @param {CreatedChatMessage} createdChatMessage
 *
 * @return {Promise<boolean>}
 *
 * @private
 */
const __handleOrderStatus = async (req, createdChatMessage, integration) => {

  const messageText = slugify(createdChatMessage.getMessage().vContentText, {
    replacement: '_',
    lower: true
  }).toString()

  if (messageText.includes('siparis_durum') || messageText.includes('order_status')) {

    // Şipariş durumu mesajı gönderilmesi için gerekli bilgiler alınıyor.
    await SendOrderStatusMessage(req, createdChatMessage.getChat(), integration)

    pino.info({
      trace_id: req.trace_id,
      message: '__handleOrderStatus fonksiyonu çalıştı',
      timestamp: new Date(),
      integration_id: createdChatMessage.getIntegration()?.id || '',
      channel_id: createdChatMessage.getChannel().id,
      chat_id: createdChatMessage.getChat().id
    })

    return true
  }

  pino.info({
    trace_id: req.trace_id,
    message: '__handleOrderStatus fonksiyonu çalışmadı -> ' + messageText,
    timestamp: new Date(),
    integration_id: createdChatMessage.getIntegration()?.id || '',
    channel_id: createdChatMessage.getChannel().id,
    chat_id: createdChatMessage.getChat().id
  })
  return false
}

/**
 * @param req
 * @param {CreatedChatMessage} createdChatMessage
 *
 * @return {Promise<boolean>}
 *
 * @private
 */
const __handleWorkingHours = async (req, createdChatMessage) => {

  if (!createdChatMessage.getChannel()) {
    return false
  }

  const channelSettings = createdChatMessage.getChannel().vSettings

  if (channelSettings.getWorkingHours().length === 0) {
    return false
  }

  // pazar 0'dan başlıyor, [0, 6]
  const activeDay = createdChatMessage.getMessage().created_at.getDay()

  // sistemde kayıtlı çalışma saatlerini elde edelim
  const workingHours = channelSettings.getWorkingHours()

  // eğer çalışma saati sayısı 7 değilse işleme devam etmiyoruz, bir hafa 7 gün olduğu için
  if (workingHours.length !== 7) {
    return false
  }
  // ilgili ayarı alalım
  let workingHour = {}
  let workingStartHour = ""
  let workingEndHour = ""

  if (channelSettings.getIsActiveSpecificWorkingHours(activeDay)) {
    const hours = channelSettings.getSpecificWorkingHours(activeDay)
    for (const hour of hours) {
      if (moment(createdChatMessage.getMessage().created_at, 'hh:mm:ss').isBetween(moment(hour.start, 'hh:mm'), moment(hour.end, 'hh:mm'))) {
        workingHour.is_active = true
        workingStartHour = (hour.start || '').trim()
        workingEndHour = (hour.end || '').trim()
        break
      }
    }
  } else {
    workingHour = workingHours[activeDay] || {}
    workingStartHour = (workingHour.start || '').trim()
    workingEndHour = (workingHour.end || '').trim()
  }

  // is_active bilgisi sonradan eklendi, mevcut ayarlarda olmayabilir, bu durumda is_active bilgisi yoksa
  // is_active bilgisi true olarak kabul edeceğiz, böylece mesai saat kontrolünü yapabileceğiz
  const isWorkingHourActive = typeof workingHour.is_active === 'undefined' ? false : workingHour.is_active

  // eğer is_active false ise mesai saati dışı mesajı gönderilmeli
  if (!isWorkingHourActive) {

    const isOowhMessage = await SendOowhMessage(req, createdChatMessage, channelSettings)

    if (isOowhMessage) {
      pino.info({
        trace_id: req.trace_id,
        message: '__handleWorkingHours::SendOowhMessage fonksiyonu çalıştı -1',
        timestamp: new Date(),
        integration_id: createdChatMessage.getIntegration()?.id || '',
        channel_id: createdChatMessage.getChannel().id,
        chat_id: createdChatMessage.getChat().id
      })
      return true
    }
    return false
  }

  // eğer is_active true ve en az bir adet start ya da end bilgisi boş ise tüm gün mesai var olarak kabul edeceğiz
  if (workingStartHour.length === 0 || workingEndHour.length === 0) {
    return false
  }

  const messageCreatedAt = moment(createdChatMessage.getMessage().created_at, 'hh:mm:ss')

  const startWorkHour = moment(workingStartHour, 'hh:mm')
  const endWorkHour = moment(workingEndHour, 'hh:mm')

  // mesai saatleri içindeyse geriye dönebiliriz
  if (messageCreatedAt.isBetween(startWorkHour, endWorkHour)) {
    return false
  }

  // mesai saatleri dışındaymışız
  const isSend = await SendOowhMessage(req, createdChatMessage, channelSettings)

  pino.info({
    trace_id: req.trace_id,
    message: '__handleWorkingHours::SendOowhMessage fonksiyonu çalıştı -2',
    timestamp: new Date(),
    integration_id: createdChatMessage.getIntegration()?.id || '',
    channel_id: createdChatMessage.getChannel().id,
    chat_id: createdChatMessage.getChat().id
  })

  return isSend
}

const __runAdsAction = async (req, createdChatMessage) => {

  const chatDoc = createdChatMessage.getChat()
  // aktif thinker durduruluyor
  if (chatDoc.thinker_status === true) {
    chatDoc.thinker_status = false
    await chatDoc.save()

    await ThinkerHasChat.updateOne({ chat_id: chat._id, status: true }, {
      $set: {
        status: false
      }
    })

    await QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.THINKER_BOT_STOP,
      socket_rooms: [createdChatMessage.getChannel().id],
      data: {
        chat_id: chatDoc.id
      }
    }, req.language)
  }

  // aktif helobot durduruluyor
  if (chatDoc.helobot_status === true) {
    chatDoc.helobot_status = false
    await chatDoc.save()

    await HelobotHasChat.updateOne({ chat_id: chatDoc._id, status: true }, {
      $set: {
        status: false
      }
    })

    await QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.HELOBOT_STOP,
      socket_rooms: [createdChatMessage.getChannel().id],
      data: {
        chat_id: chatDoc.id
      }
    }, req.language)
  }

  if (!createdChatMessage.getChannel().is_active) {
    return
  }

  const channelSettings = createdChatMessage.getChannel().vSettings
  if (!channelSettings.getIsActiveAdsActionMessaging()) {
    return
  }

  const adsActionOptions = channelSettings.getAdsActionOptions()
  if (adsActionOptions.length === 0) {
    return
  }
  await chatDoc.populate('chat_referral_id')

  let referralAdId = null;
  if (createdChatMessage.getChannel().type === enums.channel_types.WHATSAPP_NUMBER) {
    referralAdId = chatDoc.chat_referral.source_id
  }

  if (createdChatMessage.getChannel().type === enums.channel_types.FACEBOOK_PAGE || createdChatMessage.getChannel().type === enums.channel_types.INSTAGRAM_ACCOUNT) {
    referralAdId = chatDoc.chat_referral.ad_id
  }

  if (referralAdId === null) {
    pino.info({
      trace_id: req.trace_id,
      message: '__runAdsAction: Referral ad id bulunamadı',
      timestamp: new Date(),
    })
    return
  }

  const foundAdOption = adsActionOptions.find(option => option.ad.id === referralAdId)
  if (!foundAdOption) {
    pino.info({
      trace_id: req.trace_id,
      message: '__runAdsAction: Uygun AdAction bulunamadı',
      timestamp: new Date(),
    })
    return
  }

  if (foundAdOption.thinker.status) {
    if (foundAdOption.thinker.flow_id) {
      await ThinkerService.StartProcedure(chatDoc, createdChatMessage.getCompany(), createdChatMessage.getChannel().id, foundAdOption.thinker.flow_id, req.trace_id, createdChatMessage.getMessage().vContentText)

      await QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.THINKER_BOT_STARTED,
        socket_rooms: [createdChatMessage.getChannel().id],
        data: {
          chat_id: chatDoc.id
        }
      }, 'tr')

      const lastMessage = await Message.findById(chatDoc.last_message_id).populate('user_id').populate('conversation_id')

      return QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.THINKER_CHAT,
        socket_rooms: [createdChatMessage.getChannel().id],
        data: {
          chat_item: DashPresenter.getChatItem(chatDoc, lastMessage, createdChatMessage.getChannel(), undefined, chatDoc.thinker_status, chatDoc.helobot_status),
          message_item: await DashPresenter.getRepliedMessage(lastMessage),
        }
      }, 'tr')
    }
  }
  if (foundAdOption.helobot.status) {
    if (foundAdOption.helobot.knowledge_base_id) {
      const company = await Company.findById(createdChatMessage.getChannel().company_id)

      const helobotMessage = await HeloBotService.BotReplyMessage(req, company, createdChatMessage.getMessage().vContentText, chatDoc, createdChatMessage.getChannel(), true, foundAdOption.helobot.knowledge_base_id, foundAdOption.helobot.timeout)
      if (!helobotMessage) {
        return false
      }

      await QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.HELOBOT_STARTED,
        socket_rooms: [createdChatMessage.getChannel().id],
        data: {
          chat_id: chatDoc.id
        }
      }, 'tr')

      return ChatService.SendHeloBotProductMessages(req, helobotMessage, chatDoc)

    }
  }

  if (foundAdOption.message.status) {
    let chatText = foundAdOption.message.text
    if (chatText.length === 0) {
      return
    }

    return ChatService.addSystemMessage(req, chatDoc, enums.message_types.TEXT, {
      text: chatText
    }, { mark_as_seen_event: false })
  }

  pino.info({
    message: '__runAdsAction: Uygun AdAction mantığı uygulanamadw.',
    timestamp: new Date(),
  })


  // Süreç Thinker a aktarılıyor
  // if (channelSettings.getAdsActionThinkerStatus() && channelSettings.getAdsActionThinkerFlowId()) {
  //   await ThinkerService.StartProcedure(createdChatMessage.getChat(), createdChatMessage.getCompany(), createdChatMessage.getChannel().id, channelSettings.getAdsActionThinkerFlowId(), req.trace_id)

  //   await QueueService.publishToAppSocket({
  //     event: enums.agent_app_socket_events.THINKER_BOT_STARTED,
  //     socket_rooms: [createdChatMessage.getChannel().id],
  //     data: {
  //       chat_id: createdChatMessage.getChat().id
  //     }
  //   }, 'tr')

  //   const lastMessage = await Message.findById(createdChatMessage.getChat().last_message_id).populate('user_id').populate('conversation_id')

  //   return QueueService.publishToAppSocket({
  //     event: enums.agent_app_socket_events.THINKER_CHAT,
  //     socket_rooms: [createdChatMessage.getChannel().id],
  //     data: {
  //       chat_item: DashPresenter.getChatItem(createdChatMessage.getChat(), lastMessage, createdChatMessage.getChannel(), undefined, createdChatMessage.getChat().thinker_status, createdChatMessage.getChat().helobot_status),
  //       message_item: await DashPresenter.getRepliedMessage(lastMessage),
  //     }
  //   }, 'tr')
  // } else if (channelSettings.getAdsActionHelobotStatus() && channelSettings.getAdsActionHelobotKnowledgeBaseId() && createdChatMessage.getChat().thinker_status === false) {
  //   const company = await Company.findById(createdChatMessage.getChannel().company_id)

  //   const helobotMessage = await HeloBotService.BotReplyMessage(req, company, createdChatMessage.getMessage().vContentText, createdChatMessage.getChat(), createdChatMessage.getChannel(), true, channelSettings.getAdsActionHelobotKnowledgeBaseId(), channelSettings.getAdsActionHelobotTimeout())
  //   if (!helobotMessage) {
  //     return false
  //   }

  //   await QueueService.publishToAppSocket({
  //     event: enums.agent_app_socket_events.HELOBOT_STARTED,
  //     socket_rooms: [createdChatMessage.getChannel().id],
  //     data: {
  //       chat_id: createdChatMessage.getChat().id
  //     }
  //   }, 'tr')

  //   return ChatService.SendHeloBotProductMessages(req, helobotMessage, createdChatMessage.getChat())
  // }

  // let chatText = helpers.getAdsActionMessage(req, channelSettings)
  // if (chatText.length === 0) {
  //   return
  // }

  // return ChatService.addSystemMessage(req, createdChatMessage.getChat(), enums.message_types.TEXT, {
  //   text: chatText
  // }, { mark_as_seen_event: false })
}

/**
 * @param req
 * @param {CreatedChatMessage} createdChatMessage
 * @param {boolean} botData
 *
 * @return {Promise<boolean>}
 *
 * @private
 */
const __handleFirstMessage = async (req, createdChatMessage, botData, isSendUnArchivedMessage) => {

  // reklam mesajı gönderilmiş demektir.
  if (createdChatMessage.isReferralMessage()) {
    return __runAdsAction(req, createdChatMessage)
  }

  // Müşteriye ait Mesaj sayısı alınıyor
  const count = await Message.countDocuments({ conversation_id: createdChatMessage.getChatId() })

  if (count === 1) {

    const status = await __handleWorkingHours(req, createdChatMessage)

    if (!status) {
      await SendFirstMessage(req, createdChatMessage, createdChatMessage.getCompany())
      return true
    }

    return status
  } else {

    // agenta veya takıma atama işlemi yapar
    if (!createdChatMessage.getChat().thinker_status && createdChatMessage.getChannel().vSettings.getIsActiveWelcomeMessaging() && !createdChatMessage.getChannel().vSettings.getWelcomeThinkerStatus()) {
      await __setAgentToCustomer(req, createdChatMessage)
    }
  }

  // botmesajı var ise mesai durumu göz ardı edilecek
  if (!botData && isSendUnArchivedMessage === false) {
    return await __handleWorkingHours(req, createdChatMessage)
  }

  return false
}

const __checkMessageForChannelType = async (createdMessage, traceId) => {

  let data = null
  switch (createdMessage.getChannelType()) {

    case enums.channel_types.WHATSAPP_NUMBER:
      data = await GetInteractiveData(createdMessage.getChat(), createdMessage.getMessage())
      break

    default:
      data = await GetBotData(createdMessage.getChat(), 2)

      if (!data) {
        return false
      }

      if (data.messages.length !== 2) {
        return false
      }
  }

  pino.info({
    trace_id: traceId,
    message: '__checkMessageForChannelType fonksiyonu çalıştı',
    timestamp: new Date(),
    integration_id: createdMessage.getIntegration()?.id || '',
    channel_id: createdMessage.getChannel().id,
    chat_id: createdMessage.getChat().id
  })

  return data
}

const __sendTemplateMessageReport = async (chat, url, channelPhoneNumber, customer__last_message, traceId) => {

  try {

    if (customer__last_message.type !== enums.message_types.WHATSAPP_REPLY_TO_MESSAGE)
      return false

    const agent_message = await Message.findOne({ ext_id: customer__last_message.vContent.reply_to })

    if (!agent_message)
      return false


    if (agent_message.type !== enums.message_types.WHATSAPP_TEMPLATE)
      return false

    const config = {
      url: url,
      method: 'POST',
      data: {
        to: chat.ext_id,
        from: channelPhoneNumber,
        message: customer__last_message.content?.button?.text,
        template_message: agent_message.content?.body
      }
    }

    pino.info({
      trace_id: traceId,
      message: '__sendTemplateMessageReport istek atılacak',
      timestamp: new Date(),
      config: JSON.stringify(config)
    })

    return axios.request(config)
  } catch (err) {
    pino.error({
      trace_id: traceId,
      message: '__sendTemplateMessageReport error',
      error: JSON.stringify(err.response?.data || { message: 'İstek Atılamadı' }),
      timestamp: new Date(),
    })
  }
}

const __handleIntegrationMember = async (req, createdChatMessage, integration) => {

  try {

    if (createdChatMessage.getChatIntegration().ext_id) {
      return
    }

    if (integration.type !== enums.INTEGRATION_TYPES.TSOFT) {
      return
    }

    const user = await TsoftIntegrationService.process(req, integration, undefined, enums.TSOFT_ACTIONS.GET_CUSTOMERS, {
      phone_number: createdChatMessage.getUser().phone_number
    })

    if (user && user.data.data && user.data.data.length === 1) {

      createdChatMessage.getChatIntegration().ext_id = user.data.data[0].CustomerId
      await createdChatMessage.getChatIntegration().save()

      // Soket üzerinden müşteri üye olduğuna dair bilgi gönderiliyor
      QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.CUSTOMER_PAIRED,
        socket_rooms: [createdChatMessage.getChannel().id],
        data: {
          chat_id: createdChatMessage.getChat().id
        }
      }, req.i18n.language)

      pino.info({
        trace_id: req.trace_id,
        message: '__handleIntegrationMember fonksiyonu çalıştı',
        timestamp: new Date(),
        ext_id: createdChatMessage.getChatIntegration().ext_id,
        integration_id: createdChatMessage.getIntegration()?.id || '',
        channel_id: createdChatMessage.getChannel().id,
        chat_id: createdChatMessage.getChat().id
      })
    }

  } catch (err) {
    pino.error({
      trace_id: req.trace_id,
      message: '__handleIntegrationMember error',
      error: JSON.stringify(err.response?.data || { message: 'İstek Atılamadı' }),
      timestamp: new Date()
    })
  }

}

const __assignedChat = async (req, createdChatMessage) => {

  const customer__last_message = await Message.findOne({
    conversation_id: createdChatMessage.getChatId(),
    from_type: enums.message_from_types.AGENT
  }).sort({ _id: -1 })

  if (!customer__last_message) {
    return
  }

  // agent ın meta üzerinden gelen mesajları chat atamaya dahil değildir.
  if (customer__last_message.platform === enums.platforms.META) {
    return
  }

  const agentSession = await AgentSession.findOne({ agent_id: customer__last_message.user_id }).populate('agent_id').sort({ _id: -1 })
  if (agentSession.agent_id.deleted_at) {
    return
  }

  if (agentSession && !agentSession.end_time) {

    if (createdChatMessage.getChat().owner_user_id) {
      return
    }

    createdChatMessage.getChat().owner_user_id = agentSession.agent_id
    createdChatMessage.getChat().owner_user = true
    await createdChatMessage.getChat().save()

    const agent = await User.findOne({ _id: agentSession.agent_id._id, deleted_at: { $exists: false } })
    if (!agent) {
      return
    }

    QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.CHAT_OWNED_BY_AGENT,
      socket_rooms: [createdChatMessage.getChannelId()],
      data: {
        chat_id: createdChatMessage.getChatId().toString(),
        agent_id: agent.id
      }
    })

    // Chati alacak agenta chat hakkında bilgiler gönderilir
    QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.CHAT_ASSIGNED_TO_AGENT,
      socket_rooms: [agent.vSocketCode], // buraya hangi chati seçti ise onun id si gelicek
      data: {
        chat_item: DashPresenter.getChatItem(
          createdChatMessage.getChat(),
          createdChatMessage.getMessage(),
          createdChatMessage.getChannel(),
          createdChatMessage.getChatIntegration(),
          createdChatMessage.getChat().thinker_status,
          createdChatMessage.getChat().helobot_status
        )
      }
    }, req.i18n.language)

    AgentReport.addAgentInterviewReport(agent._id, createdChatMessage.getChatId(), enums.AGENT_INTERVIEW_REPORTS.ASSIGNED_FROM_SYSTEM)

    pino.info({
      trace_id: req.trace_id,
      message: '__assignedChat fonksiyonu çalıştı',
      timestamp: new Date(),
      integration_id: createdChatMessage.getIntegration()?.id || '',
      channel_id: createdChatMessage.getChannel().id,
      chat_id: createdChatMessage.getChat().id
    })
  }

}

const __setAgentToCustomer = async (req, createdChatMessage) => {

  let selectedButton = null
  const messages = await Message.find({ conversation_id: createdChatMessage.getChatId() }).sort({ created_at: -1 }).limit(2).populate('user_id')

  if (messages.length === 2) {

    if (messages[1].from_type !== enums.message_from_types.SYSTEM) {
      return
    }

    switch (createdChatMessage.getChannelType()) {

      case enums.channel_types.WHATSAPP_NUMBER:
        if (createdChatMessage.getMessage().type === enums.message_types.WHATSAPP_REPLY_TO_MESSAGE && createdChatMessage.getMessage().content.type === enums.message_types.WHATSAPP_INTERACTIVE) {
          if (createdChatMessage.getMessage().content.interactive.type !== 'list_reply') {
            return
          }

          if (createdChatMessage.getCompany().vData.getTeamStatus()) {
            // takım aktif değilse işlem yapılmayacak
            selectedButton = createdChatMessage.getChannel().vSettings.getWelcomeMessageActionsSelectedButtonForTeam(messages[0].content.interactive.list_reply.id)
            if (!selectedButton) {
              return
            }

            const teamStatus = await Team.findById(selectedButton.team_ids[0])
            if (!teamStatus.status) {
              // ekip aktif değil mesajı gönderilebilir.
              return
            }
          } else {
            selectedButton = createdChatMessage.getChannel().vSettings.getWelcomeMessageActionsSelectedButtonForAgent(messages[0].content.interactive.list_reply.id)
          }
        }
        break

      default:
        if ('select_agent' in messages[1].content) {
          if (createdChatMessage.getCompany().vData.getTeamStatus()) {
            selectedButton = messages[1].content.select_agent[Number(messages[0].content.text) - 1]
            const teamStatus = await Team.findById(selectedButton.team_ids[0])
            // takım aktif değilse işlem yapılmayacak
            if (!teamStatus.status) {
              return
            }
          }
        }
        break
    }

    if (selectedButton) {

      let selectedAgent = ""

      if (createdChatMessage.getCompany().vData.getTeamStatus()) {
        const agents = await TeamHasAgent.find({
          team_id: new mongoose.Types.ObjectId(selectedButton.team_ids[0]),
          deleted_at: { $exists: false }
        })
        if (agents.length === 0) {
          return
        }

        // aktif olan agent bulunuluyor, end_time yok ise agent aktif demektir.
        const agentSession = await AgentSession.find({
          agent_id: {
            $in: agents.map(a => a.agent_id)
          },
          end_time: {
            $exists: false
          },
          _id: {
            $gte: helpers.createObjectId(moment().utc(true).startOf('days').toDate())
          }
        })

        // chat takıma atanıyor
        createdChatMessage.getChat().team_id = new mongoose.Types.ObjectId(selectedButton.team_ids[0])
        await createdChatMessage.getChat().save()

        await ChatActions(req, enums.chat_actions.customer_assign_to_team, { name: 'App.chat_action.customer' }, createdChatMessage.getChat()._id)

        // TODO: burada aslında public atama yapılması gerekiyor. daha sonra bakılacak
        if (agentSession.length === 0) {
          return
        } else {
          // agentlardan aktif olanların birisi random olarak seçiliyor
          selectedAgent = agentSession[utils.selectRandomNumber(agentSession.length)].agent_id.toString()
        }
      } else {
        selectedAgent = selectedButton.agent_ids[0]
      }

      createdChatMessage.getChat().is_active = true
      await createdChatMessage.getChat().save()

      const agent = await User.findOne({ _id: new mongoose.Types.ObjectId(selectedAgent), deleted_at: { $exists: false } })
      if (!agent) {
        return
      }

      await QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.CHAT_OWNED_BY_AGENT,
        socket_rooms: [createdChatMessage.getChannel().id],
        data: {
          chat_id: createdChatMessage.getChatId().toString(),
          agent_id: selectedAgent
        }
      })

      // Chati alacak agenta chat hakkında bilgiler gönderilir
      await QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.CHAT_ASSIGNED_TO_AGENT,
        socket_rooms: [agent.vSocketCode], // buraya hangi chati seçti ise onun id si gelicek
        data: {
          chat_item: DashPresenter.getChatItem(
            createdChatMessage.getChat(),
            createdChatMessage.getMessage(),
            createdChatMessage.getChannel(),
            createdChatMessage.getChatIntegration(),
            createdChatMessage.getChat().thinker_status,
            createdChatMessage.getChat().helobot_status
          )
        }
      }, req.i18n.language)

      createdChatMessage.getChat().owner_user_id = agent._id
      createdChatMessage.getChat().owner_user = true
      await createdChatMessage.getChat().save()

      pino.info({
        trace_id: req.trace_id,
        message: '__setAgentToCustomer fonksiyonu çalıştı',
        timestamp: new Date(),
        integration_id: createdChatMessage.getIntegration()?.id || '',
        channel_id: createdChatMessage.getChannel().id,
        chat_id: createdChatMessage.getChat().id
      })
    }
  }
}

const setDefaultCurrencyCode = async (integration, chatIntegration) => {

  if (integration.type === enums.INTEGRATION_TYPES.TSOFT) {
    const chatIntegrationData = chatIntegration.vData

    if (chatIntegrationData.isIntegrationCurrencyCode()) {
      return
    }

    const defaultCurrency = integration.vData.getCurrencyCodes().find(a => a.default === true)

    if (defaultCurrency) {
      chatIntegrationData.setIntegrationCurrencyCode({
        id: defaultCurrency.id,
        currency: defaultCurrency.currency
      })
    } else {
      chatIntegrationData.setIntegrationCurrencyCode({
        id: '0',
        currency: 'TL'
      })
    }

    chatIntegration.data = chatIntegrationData.getData()
    chatIntegration.markModified('data')
    return chatIntegration.save()
  }
}

const __handleCustomerEvaluationReport = async (createdChatMessage, botData) => {

  if (typeof botData === 'object') {
    if (botData.agent_from_type === enums.message_from_types.SYSTEM) {
      if (botData.agent_message.content.system_next_action === enums.SYSTEM_BOT_MESSAGE_ACTION.CUSTOMER_EVALUATION_MESSAGE) {

        const firstEvaluation = await CustomerEvaluationReport.findOne({
          chat_id: createdChatMessage.getChatId(),
          channel_id: createdChatMessage.getChannelId(),
          agent_id: botData.agent_message.content.agent_id,
          message_ext_id: botData.agent_message.ext_id
        })

        // bu daha önce zaten oylanmış. süreç devam etmeyecek
        if (firstEvaluation) {
          return
        }

        let score = 0
        if (botData.customer_message.type === enums.message_types.WHATSAPP_REPLY_TO_MESSAGE) {
          score = Number(botData.customer_message.content.interactive.list_reply.id)
        } else {

          // puanlama 5 e kadar olduğu için
          if (![1, 2, 3, 4, 5].includes(Number(botData.customer_message.content.text))) {
            return
          }

          score = Number(botData.customer_message.content.text)
        }

        await new CustomerEvaluationReport({
          agent_id: botData.agent_message.content.agent_id,
          score: score,
          channel_id: createdChatMessage.getChannelId(),
          chat_id: createdChatMessage.getChatId(),
          message_ext_id: botData.agent_message.ext_id
        }).save()
      }
    }
  }
}

const AutoIntegration = async (chat, channel, chatIntegration) => {
  if (!channel.integration_id) {
    return
  }

  if (!chat.customer_id) {
    return
  }

  const user = await User.findById(chat.customer_id)
  if (user) {
    let changed = false
    if (user.email && !chat.email) {
      chat.email = user.email
      changed = true
    }
    if (user.phone_number && !chat.phone_number) {
      chat.phone_number = user.phone_number
      changed = true
    }

    if (user.data.customer_note && !chat.note) {
      chat.note = user.data.customer_note
      changed = true
    }

    if (changed) {
      await chat.save()
    }
  }

  const userIntegration = await UserIntegration.findOne({ user_id: chat.customer_id })
  if (!userIntegration) {
    return
  }

  if (userIntegration.ext_id && !chatIntegration.ext_id) {
    chatIntegration.ext_id = userIntegration.ext_id
    await chatIntegration.save()

    let socketRoom = []
    if (chat.owner_user_id) {
      const agent = await User.findById(chat.owner_user_id)

      socketRoom.push(agent.vSocketCode)
    } else {
      socketRoom.push(channel.id)
    }

    await QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.CUSTOMER_PAIRED,
      socket_rooms: socketRoom,
      data: {
        chat_id: chat.id
      }
    }, 'tr')
  }

}

const isAgentMessage = async (req, incomingMessageJobResultDto) => {
  pino.info({
    trace_id: req.trace_id,
    message: 'bu bir agent mesajıdır'
  })

  if (incomingMessageJobResultDto.getCreatedChatMessage().getChat().owner_user_id) {
    const agent = await User.findById(incomingMessageJobResultDto.getCreatedChatMessage().getChat().owner_user_id)

    const messageUser = await User.findById(incomingMessageJobResultDto.getCreatedChatMessage().getMessage().user_id)

    await QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.MESSAGE_SENT,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_item: DashPresenter.getChatItem(
          incomingMessageJobResultDto.getCreatedChatMessage().getChat(),
          incomingMessageJobResultDto.getCreatedChatMessage().getMessage(),
          incomingMessageJobResultDto.getCreatedChatMessage().getChannel(),
          incomingMessageJobResultDto.getCreatedChatMessage().getChatIntegration(),
          incomingMessageJobResultDto.getCreatedChatMessage().getChat().thinker_status,
          incomingMessageJobResultDto.getCreatedChatMessage().getChat().helobot_status
        ),
        message_item: await DashPresenter.getMessageItemCustomerOrAgent(incomingMessageJobResultDto.getCreatedChatMessage().getMessage(), messageUser),
        temp_id: ''
      }
    }, req.language)
  }
}

const sendHelobotMessage = async (req, incomingMessageJobResultDto) => {
  const helobotMessage = await HeloBotService.BotReplyMessage(req,
    incomingMessageJobResultDto.getCreatedChatMessage().getCompany(),
    incomingMessageJobResultDto.getCreatedChatMessage().getMessage().vContentText,
    incomingMessageJobResultDto.getCreatedChatMessage().getChat(),
    incomingMessageJobResultDto.getCreatedChatMessage().getChannel(),
    false
  )

  if (helobotMessage) {
    await ChatService.SendHeloBotProductMessages(req, helobotMessage, incomingMessageJobResultDto.getCreatedChatMessage().getChat())

    if (helobotMessage.live_support) {
      await HeloBotService.StopHelobot(req, incomingMessageJobResultDto.getCreatedChatMessage().getCompany()._id, incomingMessageJobResultDto.getCreatedChatMessage().getChannel(), incomingMessageJobResultDto.getCreatedChatMessage().getChat())

      if (incomingMessageJobResultDto.getCreatedChatMessage().getChat().thinker_status) {
        ThinkerService.SendMessage(incomingMessageJobResultDto.getCreatedChatMessage().getChat(), incomingMessageJobResultDto.getCreatedChatMessage().getCompany(), incomingMessageJobResultDto.getCreatedChatMessage().getChannel(), incomingMessageJobResultDto.getCreatedChatMessage().getMessage(), req.trace_id)
      }
    }
    return true
  }
}

const IncomingMessageJob = {

  TYPE_FACEBOOK: 'FACEBOOK',
  TYPE_LIVE_CHAT: 'LIVE_CHAT',
  TYPE_WHATSAPP: 'WHATSAPP', // provider: tekrom
  TYPE_WHATSAPP_CLOUD: 'WHATSAPP_CLOUD',
  TYPE_INSTAGRAM: 'INSTAGRAM',
  TYPE_TELEGRAM: 'TELEGRAM',

  add: (type, data) => {

    let job = new Job()

    job.type = enums.job_types.INCOMING_MESSAGE
    job.status = enums.job_statuses.CREATED
    job.data = {
      type: type,
      data: data
    }

    return job.save()

  },

  run: async (req, data) => {

    let incomingMessageJobResultDto

    // Mesaj tipine göre mesaj kayıtları yapılır
    switch (data.type) {

      case IncomingMessageJob.TYPE_WHATSAPP:

        incomingMessageJobResultDto = await runWhatsappIncomingMessageJob(req, WhatsappMessageDto.createFromData(data.data))
        break

      case IncomingMessageJob.TYPE_WHATSAPP_CLOUD:

        incomingMessageJobResultDto = await runWhatsappCloudIncomingMessageJob(req, WhatsappCloudMessageDto.createFromData(data.data))
        break

      case IncomingMessageJob.TYPE_FACEBOOK:

        incomingMessageJobResultDto = await runFacebookIncomingMessageJob(req, FacebookMessageDto.createFromData(data.data))
        break

      case IncomingMessageJob.TYPE_LIVE_CHAT:

        incomingMessageJobResultDto = await runLiveChatIncomingMessageJob(req, data.data.message_id)
        break

      case IncomingMessageJob.TYPE_INSTAGRAM:

        incomingMessageJobResultDto = await runInstagramIncomingMessageJob(req, InstagramMessageDto.createFromData(data.data))
        break

      case IncomingMessageJob.TYPE_TELEGRAM:

        incomingMessageJobResultDto = await runTelegramIncomingMessageJob(req, TelegramMessageDto.createFromData(data.data.message_id, data.data))
        break

      default:
        throw 'type not handled: ' + data.type
    }

    // Gelen bir mesaj yok ise process burada sonlandırlıoyr
    if (!incomingMessageJobResultDto?.getCreatedChatMessage()) {
      return
    }

    process.nextTick(() => {
      try {
        ChatReplyReportService.addChatReplyReport(
          incomingMessageJobResultDto.getCreatedChatMessage().getChatId(),
          incomingMessageJobResultDto.getCreatedChatMessage().getChannelId(),
          incomingMessageJobResultDto.getCreatedChatMessage().getMessage()
        )
      } catch (error) {
        pino.error({
          trace_id: req.trace_id,
          message: error.message,
          timestamp: new Date()
        })
      }
    })

    await AutoIntegration(
      incomingMessageJobResultDto.getCreatedChatMessage().getChat(),
      incomingMessageJobResultDto.getCreatedChatMessage().getChannel(),
      incomingMessageJobResultDto.getCreatedChatMessage().getChatIntegration()
    )

    if (
      incomingMessageJobResultDto.getCreatedChatMessage().getIntegration()
      &&
      incomingMessageJobResultDto.getCreatedChatMessage().getIntegration().type === enums.INTEGRATION_TYPES.SHOPIFY
      &&
      incomingMessageJobResultDto.getCreatedChatMessage().getChannelType() === enums.channel_types.WHATSAPP_NUMBER
      &&
      !incomingMessageJobResultDto.getCreatedChatMessage().getChatIntegration().ext_id
    ) {
      UserIntegrationJob.add(incomingMessageJobResultDto.getCreatedChatMessage().getChatId().toString(), req.language)
    }

    ChatService.messageSetSortTime(incomingMessageJobResultDto.getCreatedChatMessage().getChatId())

    // mesajı atın agent ise işleme devam edilmeyecek
    if (!incomingMessageJobResultDto.getIsCustomerMessage()) {
      await isAgentMessage(req, incomingMessageJobResultDto)
      return
    }

    if (incomingMessageJobResultDto.getCreatedChatMessage().getCompany().vData.getAssignChatToAgent()) {
      await __assignedChat(req, incomingMessageJobResultDto.getCreatedChatMessage())
    }

    if (incomingMessageJobResultDto.getCreatedChatMessage().getChat().vData.getChatLangCode()) {
      await req.i18n.changeLanguage(incomingMessageJobResultDto.getCreatedChatMessage().getChat().vData.getChatLangCode())
    }

    // Kanal tipine göre Bot Mesaj kontrolü yapıldı.
    const botData = await __checkMessageForChannelType(incomingMessageJobResultDto.getCreatedChatMessage(), req.trace_id)

    // Soketten mesaj gönderimi yapılıyor
    const isSendUnArchivedMessage = await __onCreatedMessage(req, incomingMessageJobResultDto.getCreatedChatMessage(), botData)

    if (!incomingMessageJobResultDto.getCreatedChatMessage().getChat().owner_user_id) {
      await RedisConnection.deleteChannelCustomerCount(incomingMessageJobResultDto.getCreatedChatMessage().getChannel().id, enums.chat_lists.PASSIVED)
    }

    if (incomingMessageJobResultDto.getCreatedChatMessage().getIntegration()) {
      // set default currency code
      await setDefaultCurrencyCode(incomingMessageJobResultDto.getCreatedChatMessage().getIntegration(), incomingMessageJobResultDto.getCreatedChatMessage().getChatIntegration())
    }

    // heloscope için
    await __createCustomerClientId(incomingMessageJobResultDto.getCreatedChatMessage())

    //Template mesaj raporu göndermek için yapıldı
    if (incomingMessageJobResultDto.getCreatedChatMessage().getChannel().vSettings.getHasTemplateReport() &&
      incomingMessageJobResultDto.getCreatedChatMessage().getChannel().vSettings.getTemplateUrl()) {
      __sendTemplateMessageReport(incomingMessageJobResultDto.getCreatedChatMessage().getChat(),
        incomingMessageJobResultDto.getCreatedChatMessage().getChannel().vSettings.getTemplateUrl(),
        incomingMessageJobResultDto.getCreatedChatMessage().getChannel().ext_id,
        incomingMessageJobResultDto.getCreatedChatMessage().getMessage(),
        req.trace_id
      )
    }

    // Chat bilgisine göre dil ayarlaması yapıldı
    let language = await __selectChatLanguage(req, incomingMessageJobResultDto.getCreatedChatMessage())

    // Kullanıcının ilk mesajımı ve bu mesaj mesai saati mesjaına denk geliyor mu diye kontrol edildi.
    const handleFirstMessage = await __handleFirstMessage(req, incomingMessageJobResultDto.getCreatedChatMessage(), botData, isSendUnArchivedMessage)

    if (incomingMessageJobResultDto.getCreatedChatMessage().getChat().thinker_status && incomingMessageJobResultDto.getCreatedChatMessage().getChat().helobot_status === false) {
      // thinker mesaj gönderimi yapılıyor
      ThinkerService.SendMessage(incomingMessageJobResultDto.getCreatedChatMessage().getChat(), incomingMessageJobResultDto.getCreatedChatMessage().getCompany(), incomingMessageJobResultDto.getCreatedChatMessage().getChannel(), incomingMessageJobResultDto.getCreatedChatMessage().getMessage(), req.trace_id)
    }

    // Bu bilgi true ise process burada sonlanacaktır. bot mesajı yok demektir. mesai saati mesajı veya karşılama mesajı bu aşamadan önce gitmiş olabilir.
    if (handleFirstMessage) {
      return
    }

    // helobot mesajı gönderildi
    if (incomingMessageJobResultDto.getCreatedChatMessage().getChat().helobot_status) {
      const status = await sendHelobotMessage(req, incomingMessageJobResultDto)
      if (status) {
        return
      }
    }

    // entegrasyon yok ise devam etmeyecek
    if (!incomingMessageJobResultDto.getCreatedChatMessage().getIntegration()) {
      return false
    }

    // müşteri puanlama sistemi burada yapılıyor
    await __handleCustomerEvaluationReport(incomingMessageJobResultDto.getCreatedChatMessage(), botData)

    //Müşterinin entegrasyonda üyelik kaydının olup olmadıgına bakılıyor. Üyelik kaydı yoksa es geçiliyor varsa müşteriye hangi entegrasyonu seçmek istediği gönderiliyor.
    await __handleIntegrationMember(req, incomingMessageJobResultDto.getCreatedChatMessage(), incomingMessageJobResultDto.getCreatedChatMessage().getIntegration())

    if (incomingMessageJobResultDto.getCreatedChatMessage().getIntegration().type === enums.INTEGRATION_TYPES.TSOFT) {
      // Şipariş durumu mesajı kontorlü yapılıyor
      const worked = await __handleOrderStatus(req, incomingMessageJobResultDto.getCreatedChatMessage(), incomingMessageJobResultDto.getCreatedChatMessage().getIntegration())

      // True bilgisi gelirse sipariş durumu mesajı gönderildi demektir ve process burada sonlandırılıyor
      if (worked) {
        return
      }
    }

    // bot action yoksa burada sonlanacak
    if (!botData) {
      return
    }
    if (botData.agent_from_type === enums.message_from_types.SYSTEM) {
      return
    }

    // Bot mesajı için job kaydı oluşturuluyor
    const job = await BotActionJob.add(incomingMessageJobResultDto.getCreatedChatMessage().getChatId(), botData.agent_message.vContent.next_action, language, req.trace_id)

    // Rabbit kuyruğuna mesaj ekleniyor
    await QueueService.publishToWorker({
      job_id: job.id,
      trace_id: req.trace_id
    }, language)

    pino.info({
      trace_id: req.trace_id,
      chat_id: incomingMessageJobResultDto.getCreatedChatMessage().getChatId().toString(),
      job: enums.job_statuses.QUEUED,
      language: language,
      bot_action: botData.agent_message.vContent.next_action
    })

    // Job kuyruğa verildğine dair bilgi kaydediliyor
    job.status = enums.job_statuses.QUEUED
    return job.save()

  }

}

module.exports = IncomingMessageJob
