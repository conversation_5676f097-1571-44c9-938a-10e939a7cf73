# This is an example Starter pipeline configuration
# Use a skeleton to build, test and deploy using manual and parallel steps
# -----
# You can specify a custom docker image from Docker Hub as your build environment.
# v3
image: node:22

pipelines:
  branches:
    test:
      - step:
          name: 'Backend Updated!'
          script:
            - pipe: atlassian/ssh-run:0.4.1
              variables:
                SSH_USER: $TESTUSER
                SERVER: $TESTHOST
                PORT: $TESTPORT
                COMMAND: >
                  cd /data/web/node/backend/ &&
                  bash ./refresh.sh install
                MODE: command