{"name": "helorobo-backend", "version": "1.0.0", "description": "<PERSON><PERSON><PERSON><PERSON> backend a<PERSON> projesi", "main": "worker-service.js", "scripts": {"test": "nodemon --exec 'mocha --recursive -R min'", "htest": "node node_modules/mocha/bin/mocha --recursive", "run": "node backend.js", "debug": "node backend.js | pino-pretty"}, "repository": {"type": "git", "url": "git+https://bitbucket.org/helorobo/backend.git"}, "author": "", "license": "ISC", "homepage": "https://bitbucket.org/helorobo/backend/src/master/", "dependencies": {"@google-cloud/translate": "^8.2.0", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.3", "async": "^3.2.0", "axios": "^1.6.0", "bbcode-parser": "^1.0.10", "bcrypt": "^5.1.1", "cors": "^2.8.5", "crypto-js": "^4.1.1", "cryptr": "^6.0.1", "dayjs": "^1.10.7", "decimal.js": "^10.2.1", "deep-diff": "^1.0.2", "dotenv": "^8.2.0", "express": "^4.18.2", "faker": "^4.1.0", "file-type": "^16.5.0", "firebase": "^10.4.0", "form-data": "^4.0.0", "generate-password": "^1.6.1", "googleapis": "^128.0.0", "http-errors": "^2.0.0", "https-proxy-agent": "^7.0.4", "i18next": "^19.3.4", "ioredis": "^5.3.2", "jimp": "^0.22.10", "joi": "^17.6.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.20", "mjml": "^4.14.1", "moment": "^2.29.4", "mongoose": "^8.0.0", "multer": "^1.4.2", "nanoid": "^3.1.30", "nocache": "^4.0.0", "node-cron": "^3.0.2", "nodemailer": "^6.9.7", "pino": "^8.16.1", "qrcode": "^1.5.4", "random-hash": "^4.0.1", "slugify": "^1.4.6", "speakeasy": "^2.0.0", "ssh2-sftp-client": "^9.0.4", "system-sleep": "^1.3.6", "webp-converter": "^2.3.3", "xmlbuilder2": "^3.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^6.2.3", "nodemon": "^2.0.19"}}