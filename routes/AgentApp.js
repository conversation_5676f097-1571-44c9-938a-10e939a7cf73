const express = require('express')

const FileService = require('../services/FileService')

const CheckAuth = require('../middlewares/AgentApp/CheckAuth')
const CheckAuth2FA = require('../middlewares/AgentApp/CheckAuth2FA')
const CheckPermission = require('./../middlewares/AgentApp/CheckPermission')
const CheckOvertimeStatus = require('./../middlewares/AgentApp/CheckOvertimeStatus')
const CheckPackageStatus = require('./../middlewares/AgentApp/CheckPackageStatus')

const router = express.Router()

/**
 * @type {*|Router}
 */

//Auth ve Login İşlemleri
router.post('/auth/login', require('../controllers/AgentApp/Login'))
router.post('/auth/service-account/login', require('../controllers/AgentApp/ServiceAccountLogin'))
router.get('/auth/check-agent', CheckAuth, CheckPermission, require('../controllers/AgentApp/IsAgentActive'))
router.get('/auth/2fa-status', CheckAuth2FA, CheckPermission, require('../controllers/AgentApp/2FAStatus'))
router.post('/auth/agent/2fa', CheckAuth2FA, CheckPermission, require('../controllers/AgentApp/Agents/Set2FA'))
router.post('/auth/agent/2fa-verify', CheckAuth2FA, CheckPermission, require('../controllers/AgentApp/Agents/Verify2FA'))
router.get('/auth/agent/token', CheckAuth2FA, CheckPermission, require('../controllers/AgentApp/Agents/2FAToToken'))
router.get('/auth/2fa/qrcode', CheckAuth2FA, CheckPermission, require('../controllers/AgentApp/2FA/GetQRCode'))
router.post('/auth/sendheap-login', require('../controllers/AgentApp/Auth/SendHeapLogin'))
router.get('/auth/forgot-password', require('../controllers/AgentApp/Auth/ForgotPassword'))
router.post('/auth/forgot-password', require('../controllers/AgentApp/Auth/ForgotPassword'))
router.get('/auth/set-new-password/:id/:hash', require('../controllers/AgentApp/Auth/SetNewPassword'))
router.post('/auth/set-new-password/:id/:hash', require('../controllers/AgentApp/Auth/SetNewPassword'))
router.get('/auth/login-as/:token', require('../controllers/AgentApp/Auth/LoginAs'))
router.post('/auth/logout', CheckAuth, CheckPermission, require('../controllers/AgentApp/Auth/Logout'))

router.get('/agent-chat', CheckAuth, CheckPermission, require('../controllers/AgentApp/AgentChatPage'))
router.post('/chats', CheckAuth, CheckPermission, require('../controllers/AgentApp/Chat/GetChats'))
router.get('/chat', CheckAuth, CheckPermission, require('../controllers/AgentApp/Chat/GetChat'))
router.post('/chats/thinker', CheckAuth, CheckPermission, CheckPackageStatus(false, 'thinker'), require('../controllers/AgentApp/Chat/ThinkerChats'))
router.post('/chats/helobot', CheckAuth, CheckPermission, CheckPackageStatus(false, 'helobot'), require('../controllers/AgentApp/Chat/HelobotChats'))

router.post('/search-chats', CheckAuth, CheckPermission, require('./../controllers/AgentApp/Chat/SearchChats'))
router.post('/search-messages', CheckAuth, CheckPermission, require('./../controllers/AgentApp/Message/SearchMessage'))

// Permissions
router.get('/permissions', CheckAuth, CheckPermission, require('./../controllers/AgentApp/GetPermissions'))

//Cart İşlemleri
router.get('/chats/cart', CheckAuth, CheckPermission, require('../controllers/AgentApp/Cart/GetCart'))
router.get('/chats/orders', CheckAuth, CheckPermission, require('../controllers/AgentApp/Order/GetOrders'))
router.get('/chats/order', CheckAuth, CheckPermission, require('../controllers/AgentApp/Order/GetOrder'))

router.post('/chats/send-order-history', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Message/SendOrderHistory'))

router.post('/chats/add-to-cart', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Cart/AddToCart'))
router.post('/chats/cart/remove', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Cart/RemoveItemFromCart'))
router.post('/chats/cart/set-count', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Cart/UpdateItemFromCart'))
router.post('/chats/cart/share', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Cart/Share'))
router.post('/chats/orders', CheckAuth, CheckPermission, require('../controllers/AgentApp/Order/CreateOrder'))
router.post('/chats/coupon-code', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Integration/SetCouponCode'))
router.post('/chats/cart/empty-cart', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Cart/EmptyCart'))
router.post('/chats/create-order', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Order/CreateOrder'))

//Campaign Process
router.post('/chats/send-campaigns', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Cart/SendCampaigns'))
router.post('/chats/cart/campaigns', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Integration/UpdateCampaign'))
router.post('/chats/create-campaign', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Campaigns/CreateCampaign'))
router.post('/chats/set-campaign', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Campaigns/ApplyCampaign'))

// shopify discount
router.get('/list-discount', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Shopify/ListDiscount'))
router.post('/chats/set-discount', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Shopify/SetDiscount'))
router.post('/chats/remove-discount', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Shopify/RemoveDiscount'))

//Ürün İşlemleri
router.get('/chats/:chatId/products/:productId', CheckAuth, CheckPermission, require('../controllers/AgentApp/RedirectToProductUrl'))
router.post('/chats/catalog', CheckAuth, CheckPermission, require('../controllers/AgentApp/Integration/GetCatalog'))
router.post('/chats/cart/products/share', CheckAuth, CheckPermission, require('../controllers/AgentApp/Message/ProductShareFromCart'))
router.post('/chats/products/share', CheckAuth, CheckPermission, require('../controllers/AgentApp/Message/ProductShare'))
router.post('/chats/multi-share', CheckAuth, CheckPermission, require('../controllers/AgentApp/Message/MultiProductShare'))

//Chat İşlemleri
router.get('/chats/detail', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Chat/ChatDetail'))
router.get('/chats/mark-as-seen', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Chat/ChatMarkAsSeen'))
router.post('/chats/archived', CheckAuth, CheckPermission, require('../controllers/AgentApp/Chat/ArchivedChats'))
router.post('/chats/send-location-request-message', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Message/SendLocationRequestMessage'))
router.post('/chats/send-message', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Message/SendMessage'))
router.post('/chats/send-reply-message', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Message/SendReplyMessage'))
router.post('/chats/send-forward-message', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Message/SendForwardMessage'))
router.post('/chats/send-message-file', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/SendMessageFile'))
router.post('/chats/send-location-message', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/SendLocationMessage'))
router.post('/chats/send-team-message', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Message/SendTeamMessage'))
router.post('/chats/message-reaction', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Message/Reaction'))
router.post('/chats/message-unreaction', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Message/UnReaction'))
router.post('/file/upload', CheckAuth, CheckPermission, CheckOvertimeStatus, FileService.uploadFile, require('../controllers/AgentApp/FileUpload'))
router.post('/chats/send-order-summary-message', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Cart/SendOrderSummaryMessage'))
router.post('/chats/hide', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Chat/ChatHide'))
router.post('/chats/multi-hide', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Chat/MultiChatHide'))
router.post('/chats/active-multi-hide', CheckAuth, CheckPermission, require('../controllers/AgentApp/Chat/ActiveMultiChatHide'))
router.post('/chat/reports', CheckAuth, CheckPermission, require('../controllers/AgentApp/Report/ChatReport'))
router.post('/chats/set-agent', CheckAuth, CheckPermission, require('../controllers/AgentApp/Chat/SetChatToAgent'))
router.get('/chats/whatsapp-users', CheckAuth, CheckPermission, require('../controllers/AgentApp/Chat/GetWhatsappUsers'))
router.post('/chats/block', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Chat/BlockChat'))
router.post('/chats/unblock', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Chat/UnBlockChat'))
router.get('/chats/blocked', CheckAuth, CheckPermission, require('../controllers/AgentApp/Chat/GetBlockedChats'))
router.post('/chats/pinned', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Chat/PinnedChat'))
router.post('/chats/unpinned', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Chat/UnPinnedChat'))
router.get('/chats/pinned', CheckAuth, CheckPermission, require('../controllers/AgentApp/Chat/GetPinnedChats'))
router.get('/chat/actions', CheckAuth, CheckPermission, require('../controllers/AgentApp/Chat/ChatAction'))
router.get('/chats/mark-as-unread', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Chat/ChatMarkAsUnread'))
router.post('/chats/mark-as-read', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Chat/ChatMarkAsRead'))

//Adres ve Kargo İşlemleri
router.get('/chats/addresses', CheckAuth, CheckPermission, require('../controllers/AgentApp/Address/GetAddresses'))
router.get('/chats/add-new-address', CheckAuth, CheckPermission, require('../controllers/AgentApp/Address/GetCustomerAddress'))
router.post('/chats/add-new-address', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Address/CreateCustomerAddress'))
router.get('/chats/countries/cities', CheckAuth, CheckPermission, require('../controllers/AgentApp/Integration/GetCities'))
router.get('/chats/cities/towns', CheckAuth, CheckPermission, require('../controllers/AgentApp/Integration/GetTowns'))
router.get('/chats/towns/districts', CheckAuth, CheckPermission, require('../controllers/AgentApp/Integration/GetDistricts'))
router.post('/chats/update-new-order-data', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Order/UpdateNewOrderData'))
router.post('/chats/send-cargo-options', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Message/SendCargoOptions'))
router.post('/chats/addresses/share', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Message/SendAddressMessage'))
router.post('/chats/address/share', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Message/ShareAddress'))
router.post('/chats/address/edit', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Address/EditAddress'))
router.post('/chats/address/delete', CheckAuth, CheckPermission, require('../controllers/AgentApp/Address/DeleteAddress'))
router.get('/chats/address/detail', CheckAuth, CheckPermission, require('../controllers/AgentApp/Address/GetAddress'))

//Müşteri İşlemleri
router.post('/chats/update-currency-code', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Integration/UpdateCurrencyCode'))
router.get('/chats/move-to-public', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Chat/MoveToPublic'))
router.post('/chats/add-new-customer', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Integration/CreateCustomer'))
router.get('/chats/add-new-customer', CheckAuth, CheckPermission, require('../controllers/AgentApp/Integration/GetCreateCustomer'))
router.post('/chats/send-payment-options', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Message/SendPaymentOptions'))
router.post('/chats/ask-form-question', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/AskFormQuestion'))
router.post('/chats/send-create-customer-confirmation', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Message/SendCreateCustomerConfirmation'))
router.post('/chats/messages', CheckAuth, CheckPermission, require('../controllers/AgentApp/Chat/GetChatMessages'))

//Dil İşlemleri
router.post('/chats/change/language', CheckAuth, CheckPermission, require('../controllers/AgentApp/ChangeLanguage'))

//Integration İşlemleri
router.get('/settings/integrations', CheckAuth, CheckPermission, require('../controllers/AgentApp/Integration/GetIntegrations'))
router.get('/settings/integration/:integrationId/edit', CheckAuth, CheckPermission, require('../controllers/AgentApp/Integration/IntegrationEdit'))
router.post('/settings/integration/:integrationId/edit', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Integration/IntegrationEdit'))
router.get('/settings/channels', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/Channels'))
router.get('/settings/channels/:channelId/edit', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/GetChannel'))
router.get('/settings/channels/:channelId/edit-new', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/GetChannelNew'))
router.post('/settings/channels/:channelId/edit', CheckAuth, CheckPermission, CheckOvertimeStatus, FileService.uploadPicture, require('../controllers/AgentApp/Channel/ChannelEdit'))
router.post('/settings/channels/:channelId/whatsapp-upload-picture', CheckAuth, CheckPermission, FileService.uploadPicture, require('../controllers/AgentApp/Channel/WhatsappPictureUpload'))
router.post('/settings/channels/:channelId/edit-new', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/ChannelEditNew'))
router.post('/settings/channels/:channelId/profile-edit-cloud', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/ChannelCloudProfileEdit'))
router.get('/settings/channels/:channelId/profile', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/GetChannelCloudProfile'))
router.post('/settings/channels/:channelId/chat-archived', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/ChatArchived'))
router.post('/settings/channels/:channelId/data-set', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/AddDataSet'))
router.get('/settings/channels/:channelId/data-set-event-list', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/GetDataSetEventList'))
router.post('/settings/channels/:channelId/data-set-event-list', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/SetDataSetEventList'))
router.get('/chats/:chatId/update-user-profile-data', CheckAuth, CheckPermission, require('../controllers/AgentApp/GetUserProfile'))
router.post('/chats/:chatId/update-user-profile-data', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/UpdateUserProfile'))
router.post('/chats/integration-users', CheckAuth, CheckPermission, require('../controllers/AgentApp/UserMatch'))
router.post('/chats/match-customer', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/UserMatchAdd'))
router.post('/chats/remove-match', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/RemoveUserMatch'))
router.get('/settings/integration/:integrationId/message-image-urls', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Integration/GetMessageImageUrls'))
router.post('/settings/integration/:integrationId/message-image-urls', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Integration/SetMessageImageUrls'))
router.delete('/settings/integration/:integrationId/message-image-urls', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Integration/ResetMessageImageUrls'))
router.get('/settings/channels/:channelId/ads-actions', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/GetChannelAdsAction'))
router.post('/settings/channels/:channelId/ads-actions', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/SetChannelAdsAction'))

router.post('/settings/quick-replies', CheckAuth, CheckPermission, require('../controllers/AgentApp/QuickReplies'))
router.get('/settings/quick-replies', CheckAuth, CheckPermission, require('../controllers/AgentApp/QuickReplies'))
router.post('/settings/company', CheckAuth, CheckPermission, require('../controllers/AgentApp/Settings/EditCompany'))
router.get('/settings/company', CheckAuth, CheckPermission, require('../controllers/AgentApp/Settings/GetEditCompany'))
// OBO Ads
router.get('/settings/obo-user', CheckAuth, CheckPermission, require('../controllers/AgentApp/Settings/CheckOboUser'))
router.get('/settings/obo-adaccounts', CheckAuth, CheckPermission, require('../controllers/AgentApp/Settings/GetOboAdaccounts'))
router.get('/settings/obo-adaccounts/:adaccountId/ads', CheckAuth, CheckPermission, require('../controllers/AgentApp/Settings/GetOboAdaccountAds'))

router.post('/integration/abandoned-cart', CheckAuth, CheckPermission, require('../controllers/AgentApp/Integration/NewAbandonedCartJob'))
router.get('/integration/abandoned-cart', CheckAuth, CheckPermission, require('../controllers/AgentApp/Integration/GetAbandonedCartJobs'))

router.post('/message-exports', CheckAuth, CheckPermission, require('../controllers/AgentApp/Chat/MessageExport'))
router.post('/message-export-list', CheckAuth, CheckPermission, require('../controllers/AgentApp/Chat/MessageExportList'))

//Channel İşlemleri
router.post('/settings/channels/:id/:status', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/ChannelStatus'))
router.get('/facebook/channels', CheckAuth, CheckPermission, require('../controllers/AgentApp/Facebook/GetChannelIds'))
router.get('/channel/customer/count', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/GetChannelCustomerCount'))
router.get('/channel/:channelId/auto/tag/:id', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/GetAutoTag'))
router.get('/channel/:channelId/auto/tag', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/GetAutoTags'))
router.post('/channel/:channelId/auto/tag', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/NewAutoTag'))
router.put('/channel/:channelId/auto/tag/:id', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/StatusAutoTag'))
router.post('/channel/:channelId/auto/tag/:id/report', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/GetAutoTagReport'))


//Rapor İşlemleri
router.get('/message/reports', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/MessageReport'))
router.post('/message/reports', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/MessageReport'))
router.post('/report/seasonal-session', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/SeasonalSession'))
router.post('/report/seasonal-completed-order-session', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/SeasonalCompletedOrderSession'))
router.post('/report/seasonal-message', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/SeasonalMessage'))
router.post('/report/completed-order-of-channel', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/CompletedOrderOfChannel'))
router.get('/report/customer-evaluation', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/CustomerEvaluation'))
router.post('/report/customer-evaluation-details', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/CustomerEvaluationDetails'))
router.get('/report/customer-evaluation-all', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/CustomerEvaluationAll'))
router.get('/report/customer-evaluation-messages', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/CustomerEvaluationMessages'))
router.post('/report/chats/tag-and-note', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/CustomerTagAndNote'))
router.post('/agent/report/active-time', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/AgentActiveTime'))
router.post('/agent/report/chat-speech', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/AgentChatSpeech'))
router.post('/agent/report/interview', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/AgentInterview'))
router.post('/agent/report/chat-reply', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/ChatReplyReport'))
router.post('/agent/report/completed-order', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/AgentCompletedOrderReport'))
router.post('/agent/report/status', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/AgentStatusReport'))

// Agent İşlemleri
router.post('/agent-profile', CheckAuth, CheckPermission, require('../controllers/AgentApp/Agents/GetAgentProfil'))
router.post('/agent', CheckAuth, CheckPermission, CheckPackageStatus(true), require('../controllers/AgentApp/Agents/AgentAdd'))
router.put('/agent', CheckAuth, CheckPermission, require('../controllers/AgentApp/Agents/AgentEdit'))
router.delete('/agent', CheckAuth, CheckPermission, require('../controllers/AgentApp/Agents/AgentDelete'))
router.get('/agents/list', CheckAuth, CheckPermission, require('../controllers/AgentApp/Agents/GetAgents'))
router.get('/agent/last-chat', CheckAuth, CheckPermission, require('../controllers/AgentApp/Agents/GetAgentLastChat'))
router.get('/agents/all-list', CheckAuth, CheckPermission, require('../controllers/AgentApp/Agents/GetAllAgents'))
router.get('/agent/:agentId/permission', CheckAuth, CheckPermission, require('../controllers/AgentApp/UserPermission/UserPermission'))
router.get('/agent/:agentId/channel/permissions', CheckAuth, CheckPermission, require('../controllers/AgentApp/UserPermission/ChannelPermission/GetChannelPermissions'))
router.post('/agent/:agentId/channel/permissions', CheckAuth, CheckPermission, require('../controllers/AgentApp/UserPermission/ChannelPermission/AddChannelPermission'))
router.post('/agent/:agentId/permission', CheckAuth, CheckPermission, require('../controllers/AgentApp/UserPermission/UserPermission'))
router.post('/agent/update-profile', CheckAuth, CheckPermission, require('../controllers/AgentApp/Agents/UpdateProfile'))
router.get('/agent/update-profile', CheckAuth, CheckPermission, require('../controllers/AgentApp/Agents/UpdateProfile'))
router.post('/agent/edit-company', CheckAuth, CheckPermission, require('../controllers/AgentApp/EditCompany'))
router.get('/agent/channels', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/GetChannels'))
router.post('/agent/chats', CheckAuth, CheckPermission, require('../controllers/AgentApp/Agents/AgentChats'))
router.post('/agent/chats/count', CheckAuth, CheckPermission, require('../controllers/AgentApp/Agents/AgentChatsCount'))
router.post('/agent/chat/messages', CheckAuth, CheckPermission, require('../controllers/AgentApp/Agents/AgentChatMessages'))
router.post('/agent/set-overtime', CheckAuth, CheckPermission, require('../controllers/AgentApp/AgentOvertime/SetAgentOvertime'))
router.get('/agent/get-overtime', CheckAuth, CheckPermission, require('../controllers/AgentApp/AgentOvertime/GetAgentOvertime'))

router.get('/agent-profil-information', CheckAuth, CheckPermission, require('../controllers/AgentApp/AgentProfilInfo'))
router.put('/agent-profil-information', CheckAuth, CheckPermission, require('../controllers/AgentApp/AgentProfilUpdate'))
router.get('/agent/is-super', CheckAuth, CheckPermission, require('../controllers/AgentApp/AgentIsSuper'))
router.get('/agent/team', CheckAuth, CheckPermission, require('../controllers/AgentApp/Agents/AgentTeam'))
router.post('/agent/chat-move', CheckAuth, CheckPermission, require('../controllers/AgentApp/Agents/AgentChatMove'))

//Calendar İşlemleri
router.get('/google-calendar/auth-calendar', CheckAuth, CheckPermission, require('../controllers/AgentApp/GoogleCalendar/AuthCalendar'))
router.get('/google-calendar/oauth/success', require('../controllers/AgentApp/GoogleCalendar/Webhook'))
router.get('/google-calendar/calendars', CheckAuth, CheckPermission, require('../controllers/AgentApp/GoogleCalendar/Calendars'))
router.post('/google-calendar/create-notification', CheckAuth, CheckPermission, require('../controllers/AgentApp/GoogleCalendar/CreateNotification'))
router.delete('/google-calendar', CheckAuth, CheckPermission, require('../controllers/AgentApp/GoogleCalendar/DeleteCalendar'))

/**
 * Marketing
 */
router.post('/marketing/most-commenter', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Marketing/GetMostCommenter'))
router.post('/comment', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Marketing/GetComment'))
router.post('/comment/mark-as-seen', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Marketing/MarkAsSeen'))
router.post('/get-comments', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Marketing/GetComments'))
router.get('/channels', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Marketing/GetChannels'))
router.post('/get-post', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Marketing/GetPost'))
router.post('/user/comments', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Marketing/GetUserComments'))
router.post('/reviewer/comments', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Marketing/GetReviewerComments'))
router.post('/marketing/comment/reply', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Marketing/RepyCommentToComment'))

// Instagram Süreci
router.post('/media/instagram', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Instagram/GetInstagramMedias'))
router.post('/post/comment/instagram', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Instagram/GetInstagramMediaComments'))
router.post('/comment/instagram', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Instagram/SendCommentToPost'))
router.post('/comment/reply/instagram', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Instagram/SendReplyToComment'))
router.post('/replies/instagram', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Instagram/GetCommentReplies'))
router.post('/comment/instagram/hide', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Instagram/HideComment'))
router.delete('/comment/instagram', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Instagram/DeleteComment'))

router.post('/comment/send-message', CheckAuth, CheckPermission, require('../controllers/AgentApp/Message/CommentToSendMessage'))
router.post('/comment/user-status', CheckAuth, CheckPermission, require('../controllers/AgentApp/Chat/CommentUserStatus'))

//Persistent Menu
router.get('/instagram/persistent-menu', CheckAuth, CheckPermission, require('../controllers/AgentApp/PersistentMenu/PersistentMenuGet'))
router.post('/instagram/persistent-menu', CheckAuth, CheckPermission, require('../controllers/AgentApp/PersistentMenu/PersistentMenuAdd'))
router.post('/instagram/persistent-menu/status', CheckAuth, CheckPermission, require('../controllers/AgentApp/PersistentMenu/PersistentMenuStatus'))

// Facebook Süreci
router.post('/post/comment/facebook', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Facebook/GetFacebookPostComments'))
router.post('/post/facebook', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Facebook/GetFacebookPosts'))
router.post('/comment/facebook', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Facebook/SendCommentToPost'))
router.post('/comment/reply/facebook', CheckAuth, CheckPermission, CheckPackageStatus(true, 'social_marketing'), require('../controllers/AgentApp/Facebook/SendReplyToComment'))

//CBOT
router.post('/cbot/hello', CheckAuth, CheckPermission, require('../controllers/AgentApp/CBot/SetHello'))
router.get('/cbot/hello', CheckAuth, CheckPermission, require('../controllers/AgentApp/CBot/GetHello'))
router.get('/cbot/orders', CheckAuth, CheckPermission, require('../controllers/AgentApp/CBot/GetOrders'))
router.get('/cbot/order', CheckAuth, CheckPermission, require('../controllers/AgentApp/CBot/GetOrder'))
router.get('/cbot/payment-options', CheckAuth, CheckPermission, require('../controllers/AgentApp/CBot/GetPaymentOptions'))
router.post('/cbot/create-customer', CheckAuth, CheckPermission, require('../controllers/AgentApp/CBot/CreateCustomer'))

//Heloscope Process for Admin
router.post('/heloscope/:integrationId/login', CheckAuth, CheckPermission, require('../controllers/AgentApp/Heloscope/Login'))
router.get('/heloscope/:integrationId/quota', CheckAuth, CheckPermission, require('../controllers/AgentApp/Heloscope/GetQuota'))
router.get('/heloscope/:integrationId/panel', CheckAuth, CheckPermission, require('../controllers/AgentApp/Heloscope/GetGeneral'))
router.get('/heloscope/:integrationId/traffic', CheckAuth, CheckPermission, require('../controllers/AgentApp/Heloscope/GetTraffic'))
router.get('/heloscope/:integrationId/orders', CheckAuth, CheckPermission, require('../controllers/AgentApp/Heloscope/GetOrders'))
router.get('/heloscope/:integrationId/products', CheckAuth, CheckPermission, require('../controllers/AgentApp/Heloscope/Product').ListProducts)
router.get('/heloscope/:integrationId/categories', CheckAuth, CheckPermission, require('../controllers/AgentApp/Heloscope/Category').ListCategories)

//Heloscope Process for User
router.post('/heloscope/:chatId/set-order-note', CheckAuth, CheckPermission, require('../controllers/AgentApp/Heloscope/OrderNote'))
router.get('/heloscope/:chatId/get-order-not', CheckAuth, CheckPermission, require('../controllers/AgentApp/Heloscope/OrderNote'))
router.get('/heloscope/:chatId/customer-orders', CheckAuth, CheckPermission, require('../controllers/AgentApp/Heloscope/GetCustomerOrders'))
router.get('/chats/:chatId/countries/:countryCode/cities/:cityCode/districts', CheckAuth, CheckPermission, require('../controllers/AgentApp/Heloscope/GetDistricts'))

/**
 * Template Message
 */
router.post('/channels/set-template-url', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/SetTemplateUrl'))

router.get('/channels/limits', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/Limits'))

/**
 * Advermind
 */
router.get('/advermind/check-token', CheckAuth, CheckPermission, require('../controllers/AgentApp/Advermind/GetCheckAdvermindToken'))
router.post('/advermind/set-access-token', CheckAuth, CheckPermission, require('../controllers/AgentApp/Advermind/SetAccessToken'))
router.get('/advermind/products/catalog', CheckAuth, CheckPermission, require('../controllers/AgentApp/Advermind/GetCatalog'))
router.get('/advermind/available-features', CheckAuth, CheckPermission, require('../controllers/AgentApp/Advermind/GetAvaliableFeatures'))
router.get('/advermind/campaigns', CheckAuth, CheckPermission, require('../controllers/AgentApp/Advermind/GetCampaigns'))
router.post('/advermind/ad-preview', CheckAuth, CheckPermission, require('../controllers/AgentApp/Advermind/AdPreview'))
router.get('/advermind/targeting-filters', CheckAuth, CheckPermission, require('../controllers/AgentApp/Advermind/TargetFilters'))
router.post('/advermind/reach-estimate', CheckAuth, CheckPermission, require('../controllers/AgentApp/Advermind/ReachEstimate'))
router.get('/advermind/account-balance', CheckAuth, CheckPermission, require('../controllers/AgentApp/Advermind/AccountBalance'))
router.get('/advermind/transactions', CheckAuth, CheckPermission, require('../controllers/AgentApp/Advermind/GetTransactions'))
router.get('/advermind/products/sets', CheckAuth, CheckPermission, require('../controllers/AgentApp/Advermind/ProductsSet'))
router.post('/advermind/credit-card', CheckAuth, CheckPermission, require('../controllers/AgentApp/Advermind/CreditCard'))
router.post('/advermind/create-campaign', CheckAuth, CheckPermission, require('../controllers/AgentApp/Advermind/CreateCampaign'))
router.post('/advermind/set-status-campaign', CheckAuth, CheckPermission, require('../controllers/AgentApp/Advermind/SetStatusCampaign'))
router.post('/advermind/get-campaign-detail', CheckAuth, CheckPermission, require('../controllers/AgentApp/Advermind/GetCampaignDetail'))

//Agent-Follow Process
router.post('/agent-flow/chats', CheckAuth, CheckPermission, require('../controllers/AgentApp/AgentFlow/GetChats'))
router.get('/agent-flow/agents', CheckAuth, CheckPermission, require('../controllers/AgentApp/AgentFlow/AgentList'))
router.get('/agent-flow/channels', CheckAuth, CheckPermission, require('../controllers/AgentApp/AgentFlow/Channels'))
router.get('/agent-flow/:chatId/messages', CheckAuth, CheckPermission, require('../controllers/AgentApp/AgentFlow/Messages'))
router.get('/agent-flow/:agentId/customers', CheckAuth, CheckPermission, require('../controllers/AgentApp/AgentFlow/Customers'))
router.get('/agent-flow/chats/:chatId', CheckAuth, CheckPermission, require('../controllers/AgentApp/AgentFlow/ChatDetail'))

/**
 * EShops
 */

router.post('/eshops/products', CheckAuth, CheckPermission, require('../controllers/AgentApp/Eshops/Products'))
router.get('/eshops/models', CheckAuth, CheckPermission, require('../controllers/AgentApp/Eshops/Models'))
router.get('/eshops/brands', CheckAuth, CheckPermission, require('../controllers/AgentApp/Eshops/Brands'))
router.get('/eshops/categories', CheckAuth, CheckPermission, require('../controllers/AgentApp/Eshops/Categories'))

/*
*Mobile Routes
*/
router.post('/agent/set-mobile-token', CheckAuth, CheckPermission, require('../controllers/AgentApp/Agents/SetMobileToken'))


/**
 * Arvia
 */
router.post('/video-chat/create', CheckAuth, CheckPermission, require('../controllers/AgentApp/Arvia/CreateRoom'))
router.get('/video-chat/history', CheckAuth, CheckPermission, require('../controllers/AgentApp/Arvia/ListHistoryRoom'))


/**
 * Onboarding Channels
 */
// router.post('/onboarding-wizard/instagram-complete', CheckAuth, CheckPermission, require('../controllers/AgentApp/OnBoarding/InstagramOnboarding'))
// router.post('/onboarding-wizard/whatsapp-configure', CheckAuth, CheckPermission, require('../controllers/AgentApp/OnBoarding/WhatsappEmbeddedSignup'))
router.post('/onboarding-wizard/whatsapp-configure', CheckAuth, CheckPermission, require('../controllers/AgentApp/OnBoarding/WhatsappEmbeddedSignupNew'))
router.post('/onboarding-wizard/whatsapp-configure-cloud', CheckAuth, CheckPermission, require('../controllers/AgentApp/OnBoarding/WhatsappEmbeddedSignupCloud'))
router.post('/onboarding-wizard/instagram-accounts', CheckAuth, CheckPermission, require('../controllers/AgentApp/OnBoarding/GetInstagramAccounts'))
router.post('/onboarding-wizard/instagram-configure', CheckAuth, CheckPermission, require('../controllers/AgentApp/OnBoarding/InstagramEmbeddedSignup'))
router.post('/onboarding-wizard/whatsapp-phone-numbers', CheckAuth, CheckPermission, require('../controllers/AgentApp/OnBoarding/WhatsappPhoneNumbers'))
router.post('/onboarding-wizard/facebook-pages', CheckAuth, CheckPermission, require('../controllers/AgentApp/OnBoarding/GetFacebookPages'))
router.post('/onboarding-wizard/facebook-configure', CheckAuth, CheckPermission, require('../controllers/AgentApp/OnBoarding/FacebookEmbeddedSignup'))
router.post('/onboarding-wizard/telegram-configure', CheckAuth, CheckPermission, require('../controllers/AgentApp/OnBoarding/TelegramOnboarding'))
router.post('/create-livechat-channel', CheckAuth, CheckPermission, require('../controllers/AgentApp/CreateLivechatChannel'))
router.post('/onboarding-wizard/marketing-configure', CheckAuth, CheckPermission, require('../controllers/AgentApp/OnBoarding/MarketingOnboarding'))

// Nildesk Iframe
router.put('/nildesk', CheckAuth, CheckPermission, require('../controllers/AgentApp/Nildesk/NildeskUserEdit'))
router.post('/nildesk', CheckAuth, CheckPermission, require('../controllers/AgentApp/Nildesk/NildeskUserSave'))
router.get('/nildesk-user', CheckAuth, CheckPermission, require('../controllers/AgentApp/Nildesk/NildeskUserControl'))
router.get('/nildesk', CheckAuth, CheckPermission, require('../controllers/AgentApp/Nildesk/NildeskIframe'))

// Feedback
router.post('/agent/feedback', CheckAuth, CheckPermission, require('../controllers/AgentApp/Feedback'))

/**
 * Tags
 */
router.get('/company/tags', CheckAuth, CheckPermission, require('../controllers/AgentApp/Tags/GetTags'))
router.post('/company/tags-add', CheckAuth, CheckPermission, require('../controllers/AgentApp/Tags/AddTags'))
router.put('/company/tags', CheckAuth, CheckPermission, require('../controllers/AgentApp/Tags/EditTags'))
router.delete('/company/tags', CheckAuth, CheckPermission, require('../controllers/AgentApp/Tags/DeleteTags'))
router.get('/company/package', CheckAuth, CheckPermission, require('../controllers/AgentApp/Package/GetCompanyPackage'))

router.get('/chat/tags', CheckAuth, CheckPermission, require('../controllers/AgentApp/Tags/GetChatTags'))
router.post('/chat/tags', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Tags/AddChatTags'))
router.delete('/chat/tags', CheckAuth, CheckPermission, CheckOvertimeStatus, require('../controllers/AgentApp/Tags/DeleteChatTags'))

// Instagram için dillerin verildiği genel endpoint
router.get('/instagram/locale', CheckAuth, CheckPermission, require('../controllers/AgentApp/Instagram/InstagramLocale'))

/**
 * Instagram Quick Replies
 */
router.post('/instagram/quick-replies', CheckAuth, CheckPermission, require('../controllers/AgentApp/Instagram/SetQuickReplies'))
router.get('/instagram/quick-replies', CheckAuth, CheckPermission, require('../controllers/AgentApp/Instagram/GetQuickReplies'))
router.put('/instagram/quick-replies', CheckAuth, CheckPermission, require('../controllers/AgentApp/Instagram/EditQuickReplies'))
router.delete('/instagram/quick-replies', CheckAuth, CheckPermission, require('../controllers/AgentApp/Instagram/DeleteQuickReplies'))
router.post('/instagram/quick-replies/send-message', CheckAuth, CheckPermission, require('../controllers/AgentApp/Instagram/SendMessageQuickReplies'))

// Instagram Ice Breakers
router.get('/instagram/ice-breakers', CheckAuth, CheckPermission, require('../controllers/AgentApp/IceBreakers/IceBreakersGet'))
router.post('/instagram/ice-breakers', CheckAuth, CheckPermission, require('../controllers/AgentApp/IceBreakers/IceBreakersAdd'))
router.post('/instagram/ice-breakers/status', CheckAuth, CheckPermission, require('../controllers/AgentApp/IceBreakers/IceBreakersStatus'))

// Instagram Private Reply
router.get('/instagram/private-reply', CheckAuth, CheckPermission, require('../controllers/AgentApp/Instagram/PrivateReplyGet'))
router.post('/instagram/private-reply', CheckAuth, CheckPermission, require('../controllers/AgentApp/Instagram/PrivateReplyAdd'))

// Instagram ig.me
router.post('/instagram/ig-me-link', CheckAuth, CheckPermission, require('../controllers/AgentApp/Instagram/IgMeLinkAdd'))
router.get('/instagram/ig-me-link', CheckAuth, CheckPermission, require('../controllers/AgentApp/Instagram/IgMeLinkGet'))
router.put('/instagram/ig-me-link', CheckAuth, CheckPermission, require('../controllers/AgentApp/Instagram/IgMeLinkEdit'))
router.delete('/instagram/ig-me-link', CheckAuth, CheckPermission, require('../controllers/AgentApp/Instagram/IgMeLinkDelete'))

// Team Geliştirmesi
router.get('/team', CheckAuth, CheckPermission, require('../controllers/AgentApp/Team/GetTeam'))
router.get('/list-team', CheckAuth, CheckPermission, require('../controllers/AgentApp/Team/ListTeam'))
router.post('/add-team', CheckAuth, CheckPermission, require('../controllers/AgentApp/Team/AddTeam'))
router.post('/edit-team', CheckAuth, CheckPermission, require('../controllers/AgentApp/Team/EditTeam'))
router.post('/status-team', CheckAuth, CheckPermission, require('../controllers/AgentApp/Team/TeamStatus'))
router.delete('/delete-team', CheckAuth, CheckPermission, require('../controllers/AgentApp/Team/DeleteTeam'))
router.post('/set-team-or-agent', CheckAuth, CheckPermission, require('../controllers/AgentApp/Team/SetTeamOrAgent'))

router.get('/version/notes', CheckAuth, CheckPermission, require('../controllers/AgentApp/Version/VersionNotes'))
router.get('/version/readed', CheckAuth, CheckPermission, require('../controllers/AgentApp/Version/VersionStatus'))
router.post('/version/readed', CheckAuth, CheckPermission, require('../controllers/AgentApp/Version/VersionReaded'))

router.post('/message/retry', CheckAuth, CheckPermission, require('../controllers/AgentApp/Message/Retry'))
router.get('/conversation-limit', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/ConversationLimit'))
router.get('/message-limit', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/MessageLimit'))

// Mesaj yıldızlama
router.post('/message/add-star', CheckAuth, CheckPermission, require('../controllers/AgentApp/Message/AddStar'))
router.post('/message/remove-star', CheckAuth, CheckPermission, require('../controllers/AgentApp/Message/RemoveStar'))
router.post('/message/starred-list', CheckAuth, CheckPermission, require('../controllers/AgentApp/Message/StarredList'))
router.post('/message/translate', CheckAuth, CheckPermission, require('../controllers/AgentApp/Message/Translate'))

router.get('/payment-url', CheckAuth, CheckPermission, require('../controllers/AgentApp/PaymentUrl'))
router.get('/email-confirmation', CheckAuth, CheckPermission, require('../controllers/EmailConfirmation'))

router.post('/trendyol/create-account', CheckAuth, CheckPermission, CheckPackageStatus(true, 'trendyol'), require('../controllers/Trendyol/CreateTrendyolAccount'))
router.get('/trendyol/account', CheckAuth, CheckPermission, CheckPackageStatus(false, 'trendyol'), require('../controllers/Trendyol/GetTrendyolAccount'))
router.get('/trendyol/accounts', CheckAuth, CheckPermission, CheckPackageStatus(false, 'trendyol'), require('../controllers/Trendyol/GetTrendyolAccounts'))
router.put('/trendyol/edit-account', CheckAuth, CheckPermission, CheckPackageStatus(true, 'trendyol'), require('../controllers/Trendyol/EditTrendyolAccount'))
router.delete('/trendyol/delete-account', CheckAuth, CheckPermission, CheckPackageStatus(true, 'trendyol'), require('../controllers/Trendyol/DeleteTrendyolAccount'))
router.post('/trendyol/questions', CheckAuth, CheckPermission, CheckPackageStatus(true, 'trendyol'), require('../controllers/Trendyol/TrendyolQuestions'))
router.post('/trendyol/questions/:questionsId/answer', CheckAuth, CheckPermission, CheckPackageStatus(true, 'trendyol'), require('../controllers/Trendyol/AnswerQuestion'))

router.post('/hepsiburada/create-account', CheckAuth, CheckPermission, CheckPackageStatus(true, 'hepsiburada'), require('../controllers/Hepsiburada/CreateHepsiburadaAccount'))
router.get('/hepsiburada/account', CheckAuth, CheckPermission, CheckPackageStatus(false, 'hepsiburada'), require('../controllers/Hepsiburada/GetHepsiburadaAccount'))
router.get('/hepsiburada/accounts', CheckAuth, CheckPermission, CheckPackageStatus(false, 'hepsiburada'), require('../controllers/Hepsiburada/GetHepsiburadaAccounts'))
router.put('/hepsiburada/edit-account', CheckAuth, CheckPermission, CheckPackageStatus(true, 'hepsiburada'), require('../controllers/Hepsiburada/EditHepsiburadaAccount'))
router.delete('/hepsiburada/delete-account', CheckAuth, CheckPermission, CheckPackageStatus(true, 'hepsiburada'), require('../controllers/Hepsiburada/DeleteHepsiburadaAccount'))
router.post('/hepsiburada/questions', CheckAuth, CheckPermission, CheckPackageStatus(true, 'hepsiburada'), require('../controllers/Hepsiburada/HepsiburadaQuestions'))
router.post('/hepsiburada/questions/:issueNumber/answer', CheckAuth, CheckPermission, CheckPackageStatus(true, 'hepsiburada'), require('../controllers/Hepsiburada/AnswerQuestion'))

//HeloBot
router.get('/helobot/account', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/GetAccount'))
router.post('/helobot/create-account', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/CreateAccount'))
router.get('/helobot/activity', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/AccountActivity'))
router.get('/helobot/credit', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/CheckCredit'))
router.get('/helobot/knowledge-bases', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/KnowledgebaseList'))
router.post('/helobot/knowledge-base-status', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/KnowledgeBaseStatus'))
router.post('/helobot/knowledge-base', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/CreateKnowledgebase'))
router.get('/helobot/channels-knowledge-bases', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/ChannelsKnowledgeBaseList'))
router.put('/helobot/knowledge-base-prompt', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/UpdateKnowledgebasePrompt'))
router.delete('/helobot/knowledge-base', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/DeleteKnowledgebase'))
router.post('/helobot/create-conversation', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/CreateConversation'))
router.post('/helobot/stop', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), CheckOvertimeStatus, require('../controllers/Helobot/StopHelobot'))
router.post('/helobot/start', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), CheckOvertimeStatus, require('../controllers/Helobot/StartHelobot'))
router.get('/helobot/pay-credit', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/BuyCredit'))
router.get('/helobot/purchased-packages', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/PurchasedPackages'))
router.get('/helobot/credit-usage', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/CreditUsage'))
router.get('/helobot/total-credit-usage', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/TotalCreditUsage'))

// Media Upload Url
router.get('/media/url', CheckAuth, CheckPermission, require('../controllers/AgentApp/GetMediaUrl'))
router.get('/media/cloudflare/url', CheckAuth, CheckPermission, require('../controllers/AgentApp/GetMediaCloudflareUrl'))


//Locations
router.get('/locations', CheckAuth, CheckPermission, require('../controllers/AgentApp/Location/Locations'))
router.post('/location', CheckAuth, CheckPermission, require('../controllers/AgentApp/Location/AddLocation'))
router.put('/location', CheckAuth, CheckPermission, require('../controllers/AgentApp/Location/EditLocation'))
router.delete('/location', CheckAuth, CheckPermission, require('../controllers/AgentApp/Location/DeleteLocation'))

// New Reports
router.get('/dashboard/message-count', CheckAuth, CheckPermission, require('../controllers/AgentApp/Dashboard/MessageCount'))
router.get('/dashboard/chat-count', CheckAuth, CheckPermission, require('../controllers/AgentApp/Dashboard/ChatCount'))
router.get('/dashboard/chat-count-by-channel', CheckAuth, CheckPermission, require('../controllers/AgentApp/Dashboard/ChatCountByChannel'))
router.get('/dashboard/channel-message-count', CheckAuth, CheckPermission, require('../controllers/AgentApp/Dashboard/MessageCountByChannel'))
router.get('/dashboard/thinker-count', CheckAuth, CheckPermission, require('../controllers/AgentApp/Dashboard/ThinkerCount'))
router.get('/dashboard/helobot-count', CheckAuth, CheckPermission, require('../controllers/AgentApp/Dashboard/HelobotCount'))
router.get('/dashboard/agent-count', CheckAuth, CheckPermission, require('../controllers/AgentApp/Dashboard/AgentCount'))
router.get('/dashboard/overtime-agent-count', CheckAuth, CheckPermission, require('../controllers/AgentApp/Dashboard/OvertimeAgentCount'))

//entegrasyon raporları
router.post('/report/member-count', CheckAuth, CheckPermission, require('../controllers/AgentApp/Report/Integration/MemberCount'))
router.post('/report/order-count', CheckAuth, CheckPermission, require('../controllers/AgentApp/Report/Integration/OrderCount'))

// pazarama
router.post('/pazarama/create-account', CheckAuth, CheckPermission, CheckPackageStatus(true, 'pazarama'), require('../controllers/Pazarama/CreatePazaramaAccount'))
router.get('/pazarama/account', CheckAuth, CheckPermission, CheckPackageStatus(false, 'pazarama'), require('../controllers/Pazarama/GetPazaramaAccount'))
router.get('/pazarama/accounts', CheckAuth, CheckPermission, CheckPackageStatus(false, 'pazarama'), require('../controllers/Pazarama/GetPazaramaAccounts'))
router.put('/pazarama/edit-account', CheckAuth, CheckPermission, CheckPackageStatus(true, 'pazarama'), require('../controllers/Pazarama/EditPazaramaAccount'))
router.delete('/pazarama/delete-account', CheckAuth, CheckPermission, CheckPackageStatus(true, 'pazarama'), require('../controllers/Pazarama/DeletePazaramaAccount'))
router.get('/pazarama/getQuestionStatus', CheckAuth, CheckPermission, CheckPackageStatus(true, 'pazarama'), require('../controllers/Pazarama/GetQuestionStatus'))
router.get('/pazarama/getQuestionTopics', CheckAuth, CheckPermission, CheckPackageStatus(true, 'pazarama'), require('../controllers/Pazarama/GetQuestionTopics'))
router.get('/pazarama/getApprovalAnswersByMerchant', CheckAuth, CheckPermission, CheckPackageStatus(true, 'pazarama'), require('../controllers/Pazarama/GetApprovalAnswersByMerchant'))
router.get('/pazarama/getApprovalAnswerById', CheckAuth, CheckPermission, CheckPackageStatus(true, 'pazarama'), require('../controllers/Pazarama/GetApprovalAnswerById'))
router.put('/pazarama/sellerAnswer', CheckAuth, CheckPermission, CheckPackageStatus(true, 'pazarama'), require('../controllers/Pazarama/SellerAnswer'))
router.post('/pazarama/getApprovalAnswersByMerchantSearch', CheckAuth, CheckPermission, CheckPackageStatus(true, 'pazarama'), require('../controllers/Pazarama/GetApprovalAnswersByMerchantSearch'))

router.get('/packages', CheckAuth, CheckPermission, require('../controllers/AgentApp/Package/GetPackages'))

router.get('/shopify/packages', CheckAuth, CheckPermission, require('../controllers/AgentApp/Package/GetShopifyPackages'))
router.post('/shopify/subscription', CheckAuth, CheckPermission, require('../controllers/AgentApp/Shopify/NewSubscription'))
router.delete('/shopify/subscription', CheckAuth, CheckPermission, require('../controllers/AgentApp/Shopify/DeleteSubscription'))
router.post('/shopify/conversation', CheckAuth, CheckPermission, require('../controllers/AgentApp/Shopify/NewConversationPackage'))

// n11
router.post('/n11/create-account', CheckAuth, CheckPermission, CheckPackageStatus(true, 'n11'), require('../controllers/N11/CreateN11Account'))
router.get('/n11/account', CheckAuth, CheckPermission, CheckPackageStatus(false, 'n11'), require('../controllers/N11/GetN11Account'))
router.get('/n11/accounts', CheckAuth, CheckPermission, CheckPackageStatus(false, 'n11'), require('../controllers/N11/GetN11Accounts'))
router.put('/n11/edit-account', CheckAuth, CheckPermission, CheckPackageStatus(true, 'n11'), require('../controllers/N11/EditN11Account'))
router.delete('/n11/delete-account', CheckAuth, CheckPermission, CheckPackageStatus(true, 'n11'), require('../controllers/N11/DeleteN11Account'))
router.post('/n11/getProductQuestionList', CheckAuth, CheckPermission, CheckPackageStatus(true, 'n11'), require('../controllers/N11/GetProductQuestionList'))
router.post('/n11/getProductQuestionDetail', CheckAuth, CheckPermission, CheckPackageStatus(true, 'n11'), require('../controllers/N11/GetProductQuestionDetail'))
router.post('/n11/saveProductAnswer', CheckAuth, CheckPermission, CheckPackageStatus(true, 'n11'), require('../controllers/N11/SaveProductAnswer'))

// Abandoned Cart
router.get('/abandoned-cart', CheckAuth, CheckPermission, require('../controllers/AgentApp/Integration/GetAbandonedCartJobs'))
router.post('/abandoned-cart/chats', CheckAuth, CheckPermission, require('../controllers/AgentApp/Integration/GetAbandonedCartHasChat'))
router.post('/abandoned-cart', CheckAuth, CheckPermission, require('../controllers/AgentApp/Integration/NewAbandonedCartJob'))
router.delete('/abandoned-cart', CheckAuth, CheckPermission, require('../controllers/AgentApp/Integration/RemoveAbandonedCartJobs'))
router.put('/abandoned-cart', CheckAuth, CheckPermission, require('../controllers/AgentApp/Integration/ChangeStatusAbandonedCartJob'))

// service accounts
router.post('/service-account', CheckAuth, CheckPermission, require('../controllers/AgentApp/ServiceAccount/CreateServiceAccount'))
router.get('/service-account', CheckAuth, CheckPermission, require('../controllers/AgentApp/ServiceAccount/GetServiceAccounts'))
router.delete('/service-account', CheckAuth, CheckPermission, require('../controllers/AgentApp/ServiceAccount/DeleteServiceAccount'))

// billtekrom payment check
router.get('/billtekrom/payment/check', CheckAuth, CheckPermission, require('../controllers/AgentApp/Billtekrom/CheckPayment'))

// Agent Status
router.post('/agent/status', CheckAuth, CheckPermission, require('../controllers/AgentApp/Agents/AgentStatus'))

// livechat code add to site
router.post('/livechat/script', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/AddScript'))
router.delete('/livechat/script', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/RemoveScript'))
router.get('/livechat/script/check', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/CheckScript'))
router.get('/livechat/script', CheckAuth, CheckPermission, require('../controllers/AgentApp/Channel/ListScript'))

router.post('/market/quick-replies', CheckAuth, CheckPermission, require('../controllers/AgentApp/Market/GetQuickReply'))
router.post('/market/quick-reply', CheckAuth, CheckPermission, require('../controllers/AgentApp/Market/AddQuickReply'))
router.delete('/market/quick-reply', CheckAuth, CheckPermission, require('../controllers/AgentApp/Market/RemoveQuickReply'))

// google drive
router.get('/google/drive', CheckAuth, CheckPermission, require('../controllers/AgentApp/Google/Drive'))
router.get('/google/drive/auth', CheckAuth, CheckPermission, require('../controllers/AgentApp/Google/DriveAuth'))
router.post('/google/drive/backup-status', CheckAuth, CheckPermission, require('../controllers/AgentApp/Google/DriveBackupStatus'))
router.delete('/google/drive/auth', CheckAuth, CheckPermission, require('../controllers/AgentApp/Google/RemoveDrive'))
router.post('/google/drive/stop-backup', CheckAuth, CheckPermission, require('../controllers/AgentApp/Google/StopBackup'))
router.post('/google/drive/start-backup', CheckAuth, CheckPermission, require('../controllers/AgentApp/Google/StartBackup'))
router.get('/google/drive/message-backup', CheckAuth, CheckPermission, require('../controllers/AgentApp/Google/DriveMessageBackupList'))

// Meta ads
router.post('/meta-ads/user', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/CreateUser'))
router.get('/meta-ads/user', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/GetUser'))
router.get('/meta-ads/available-pages', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/GetAvailablePages'))
router.get('/meta-ads/child-bms', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/GetChildBms'))
router.post('/meta-ads/child-bms', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/CreateChildBm'))
router.delete('/meta-ads/child-bms/:childBmId', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/DeleteChildBm'))

router.get('/meta-ads/child-bms/:childBmId/campaigns', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/Campaign/GetCampaigns'))
router.get('/meta-ads/child-bms/:childBmId/campaigns/:campaignId', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/Campaign/GetCampaign'))
router.post('/meta-ads/child-bms/:childBmId/campaigns', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/Campaign/CreateCampaign'))
router.delete('/meta-ads/child-bms/:childBmId/campaigns/:campaignId', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/Campaign/DeleteCampaign'))
router.post('/meta-ads/child-bms/:childBmId/campaigns/:campaignId/update-status', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/Campaign/UpdateCampaignStatus'))

router.get('/meta-ads/child-bms/:childBmId/adsets', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/Adset/GetAdsets'))
router.post('/meta-ads/child-bms/:childBmId/adsets', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/Adset/CreateAdset'))
router.delete('/meta-ads/child-bms/:childBmId/adsets/:adsetId', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/Adset/DeleteAdset'))
router.post('/meta-ads/child-bms/:childBmId/adsets/:adsetId/update-status', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/Adset/UpdateAdsetStatus'))

router.get('/meta-ads/child-bms/:childBmId/ads', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/Ad/GetAds'))
router.post('/meta-ads/child-bms/:childBmId/ads', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/Ad/CreateAd'))
router.delete('/meta-ads/child-bms/:childBmId/ads/:adId', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/Ad/DeleteAd'))
router.post('/meta-ads/child-bms/:childBmId/ads/:adId/update-status', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/UpdateAdStatus'))

router.get('/meta-ads/child-bms/:childBmId/adcreatives', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/AdCreative/GetAdcreative'))
router.post('/meta-ads/child-bms/:childBmId/adcreatives', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/AdCreative/CreateAdcreative'))

router.get('/meta-ads/child-bms/:childBmId/instagram-posts', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/AdCreative/GetInstagramPosts'))
router.get('/meta-ads/child-bms/:childBmId/facebook-posts', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/AdCreative/GetFacebookPosts'))

router.get('/meta-ads/child-bms/:childBmId/get-child-bm-info', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/GetAccessTokenForUploadService'))
router.get('/meta-ads/child-bms/:childBmId/adimages', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/Adimages'))
router.get('/meta-ads/child-bms/:childBmId/advideos', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/Advideos'))
router.get('/meta-ads/child-bms/:childBmId/ads/:adId/preview', CheckAuth, CheckPermission, require('../controllers/AgentApp/MetaAds/Ad/PreviewAd'))

router.get('/cdn-proxy', CheckAuth, CheckPermission, require('../controllers/CDNProxy'))

module.exports = router
