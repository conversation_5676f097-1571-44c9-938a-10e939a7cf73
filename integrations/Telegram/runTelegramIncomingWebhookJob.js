const { Types } = require('mongoose')

const Channel = require('../../models/Channel')

const JobService = require('../../services/JobService')

const IncomingMessageJob = require('../../jobs/IncomingMessageJob')
const IncomingWebhookJobResultDto = require('../../dtos/IncomingWebhookJobResultDto')

/**
 * @param req
 * @param {object} body req.body job üzerinden dolaylı olarak geliyor
 *
 * @return {Promise<IncomingWebhookJobResultDto>}
 */
module.exports = async (req, body) => {

  const incomingWebhookJobResultDto = new IncomingWebhookJobResultDto()

  // samet şirket telefonu
  if (['905347989583'].includes(body.channel_ext_id)) {
    incomingWebhookJobResultDto.setHasDevelopmentContent(true)
    incomingWebhookJobResultDto.setSenderId(body.ext_id)
  }

  const channel = await Channel.findOne({
    _id: new Types.ObjectId(body.channel_id),
    ext_id: body.channel_ext_id,
    is_active: true,
    deleted_at: {
      $exists: false
    }
  }).populate('company_id')
  if (!channel) {
    throw new Error('Kanal Bulunamadı')
  }

  if (channel.company?.is_active === false) {
    throw new Error('Şirket Pasif Olduğu için Mesaj Alınmadı')
  }

  if (channel.company_id.vData.getUsingAsBridge()) {
    incomingWebhookJobResultDto.setUsingAsBridge(channel.company_id.vData.getUsingAsBridge())
    incomingWebhookJobResultDto.setWebhookUrl(channel.company_id.vData.getDataWebhookUrl())
    incomingWebhookJobResultDto.setWebhookUrlHash(channel.company_id.vData.getDataWebhookUrlHash())
  }

  await JobService.addIncomingMessageJob(
    req,
    IncomingMessageJob.TYPE_TELEGRAM,
    body
  )

  return incomingWebhookJobResultDto
}
