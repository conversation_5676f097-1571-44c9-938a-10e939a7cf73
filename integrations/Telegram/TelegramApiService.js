const axios = require('axios')

const TelegramApiService = {

  sendMessage: async (chat, channel, message) => {
    let config = {
      url: `${process.env.TELEGRAM_BASE_URL}/api/v1/message/send`,
      method: "POST",
      data: {
        chat: {
          ext_id: chat.ext_id
        },
        channel: {
          id: channel.id,
          ext_id: channel.ext_id,
        },
        message: {
          text: message.content.text || message.content.caption,
          url: message.content.url
        }
      }
    }

    const response = await axios.request(config)
    return response.data.data.message_id
  },

  markAsSeen: async (chat, channel) => {
    let config = {
      url: `${process.env.TELEGRAM_BASE_URL}/api/v1/message/mark-as-seen`,
      method: "POST",
      data: {
        chat: {
          ext_id: chat.ext_id
        },
        channel: {
          id: channel.id,
          ext_id: channel.ext_id,
        },
      }
    }

    await axios.request(config)
  },

  addChannel: async (channelId, telegramSession, extId) => {
    let config = {
      url: `${process.env.TELEGRAM_BASE_URL}/api/v1/channel`,
      method: "POST",
      data: {
        channel_id: channelId,
        telegram_session: telegramSession,
        channel_ext_id: extId
      }
    }

    return await axios.request(config).then(response => {
      return response.data.message
    })
  },

  removeChannel: async (channelId, telegramSession, extId) => {
    let config = {
      url: `${process.env.TELEGRAM_BASE_URL}/api/v1/channel`,
      method: "DELETE",
      data: {
        channel_id: channelId,
        telegram_session: telegramSession,
        ext_id: extId
      }
    }

    await axios.request(config).then(response => {
      return response.data.message
    }).catch(error => {
      throw error.response?.data?.errors
    })
  }


}

module.exports = TelegramApiService