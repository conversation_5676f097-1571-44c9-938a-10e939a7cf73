const enums = require('../../libs/enums')

const TelegramService = {

  /**
   * @param {TelegramMessageDto} dto
   * @return Object
   */

  getMessageObject: async dto => {

    switch (dto.getItem().getMessageType()) {

      case enums.message_types.TELEGRAM_UPDATE_SHORT_MESSAGE:

        return {
          type: enums.message_types.TEXT,
          content: {
            mid: dto.getItem().getMessageId(),
            text: dto.getItem().getMessage()
          }
        }

      case enums.message_types.TELEGRAM_UPDATE_NEW_MESSAGE:
        let type = ""

        if (enums.allowed_message_types.image.includes(dto.getItem().getMediaType())) {
          type = enums.message_types.IMAGE_URL
        }
        else if (enums.allowed_message_types.video.includes(dto.getItem().getMediaType())) {
          type = enums.message_types.VIDEO_URL
        }
        else if (enums.allowed_message_types.audio.includes(dto.getItem().getMediaType())) {
          type = enums.message_types.AUDIO_URL
        }
        else if (enums.allowed_message_types.file.includes(dto.getItem().getMediaType())) {
          type = enums.message_types.FILE_URL
        }

        return {
          type: type,
          content: {
            mid: dto.getItem().getMessageId(),
            caption: dto.getItem().getMessage(),
            url: dto.getItem().getUrl()
          }
        }

      default:
        throw new Error('IncomingMessage type not found: ' + JSON.stringify(dto.getItem().getData()))
    }

  }

}

module.exports = TelegramService