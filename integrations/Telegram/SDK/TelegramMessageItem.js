class TelegramMessageItem {
  constructor(data) {
    this.data = data
  }

  getData() {
    return this.data
  }

  getMessageChannelId() {
    return this.data.channel_id
  }

  getMessageChannelExtId() {
    return this.data.channel_ext_id
  }

  getMessageType() {
    return this.data.type
  }

  getUserFirstName() {
    return this.data.first_name
  }

  getUserLastName() {
    return this.data.last_name
  }

  getUsername() {
    return this.data.username
  }

  getPhoneNumber() {
    return this.data.phone_number
  }

  getExtId() {
    return this.data.ext_id
  }

  getMessageId() {
    return this.data.message_id
  }

  getMessage() {
    return this.data.message
  }

  getTimestamp() {
    return this.data.date
  }

  getUrl() {
    return this.data.media_url
  }

  getMediaType() {
    return this.data.media_type
  }

}

module.exports = TelegramMessageItem