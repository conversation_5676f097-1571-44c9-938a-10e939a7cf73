const TelegramStatusItem = require('./TelegramStatusItem')


class TelegramStatusDto {

  constructor(id, data) {

    this.id = id
    // TelegramStatusItem için kullanılmak üzere data
    this.data = data
  }

  static createFromTelegram(id, data) {
    return new TelegramStatusDto(id, data)
  }

  static createFromData(data) {
    return new TelegramStatusDto(data.id, data.data)
  }

  getItem() {
    return new TelegramStatusItem(this.data)
  }
}

module.exports = TelegramStatusDto