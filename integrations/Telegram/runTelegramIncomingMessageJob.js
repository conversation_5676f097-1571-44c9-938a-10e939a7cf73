const pino = require('pino')()
const mongoose = require('mongoose')

const enums = require('../../libs/enums')
const helpers = require('../../libs/helpers')

const Channel = require('../../models/Channel')
const CompanyHasPackage = require('../../models/CompanyHasPackage')

const ChatService = require('../../services/ChatService')

const CreatedChatMessage = require('../../services/Chat/CreatedChatMessage')
const IntegrationService = require('../../modules/AgentApp/IntegrationService')

const MessageRepo = require('../../repos/MessageRepo')

const TelegramService = require('./../Telegram/TelegramService')

const IncomingMessageJobResultDto = require('./../../dtos/IncomingMessageJobResultDto')

module.exports = async (req, dto) => {

  const incomingMessageJobResultDto = new IncomingMessageJobResultDto()

  // Gelen mesajın channelı var mı kontrol edildi
  const channel = await Channel.findOne({
    _id: new mongoose.Types.ObjectId(dto.getItem().getMessageChannelId()),
    type: enums.channel_types.TELEGRAM_ACCOUNT,
    is_active: true,
    deleted_at: {
      $exists: false
    }
  }).populate('company_id').populate('integration_id')

  if (!channel) {
    pino.info({
      trace_id: req.trace_id,
      timestamp: new Date(),
      message: 'TelegramIncomingMessageJob channel not found: ' + dto.getItem().getMessageChannelId()
    })
    throw new Error('TelegramIncomingMessageJob channel not found: ' + dto.getItem().getMessageChannelId())
  }

  const companyHasPackage = await CompanyHasPackage.findOne({
    company_id: channel.company_id,
    deleted_at: {
      $exists: false
    }
  }).sort({ _id: 1 })
  if (companyHasPackage && companyHasPackage.data.telegram) {
    if (helpers.isModuleTimeOut(companyHasPackage.data.telegram) !== false) {
      channel.is_active = false
      await channel.save()

      pino.info({
        trace_id: req.trace_id,
        timestamp: new Date(),
        message: 'TelegramIncomingMessageJob channel süresi dolduğu için pasif edildi ve mesaj işlenmedi',
        data: JSON.stringify({
          channel_id: channel.id,
          channel_ext_id: channel.ext_id
        })
      })

      throw new Error('TelegramIncomingMessageJob channel süresi dolduğu için pasif edildi ve mesaj işlenmedi')
    }
  }

  // Webhookdan gelen mesaj handle edildi
  const messageObject = await TelegramService.getMessageObject(dto)

  let userName = ''
  if (dto.getItem().getUserFirstName()) {
    userName = `${dto.getItem().getUserFirstName()} ${dto.getItem().getUserLastName()}`
  } else {
    userName = dto.getItem().getUsername()
  }

  const chat = await ChatService.getOrCreateConversation(userName, channel.id, dto.getItem().getExtId())

  if (chat.is_blocked == true) {
    return
  }

  // chat eğer gizli ise gizliliği açılacak
  if (chat.hide) {
    chat.hide = false
  }
  chat.has_unreaded_message = true
  await chat.save()

  // Webhookdan gelen mesaj bizim tarafımıza kaydedildi
  const message = await MessageRepo.create({
    type: messageObject.type,
    content: messageObject.content,
    chatId: chat.id,
    extId: dto.getItem().getMessageId(),
    fromType: enums.message_from_types.CUSTOMER,
    sendStatus: enums.message_send_statuses.SENT,
    time: dto.getItem().getTimestamp(),
    data: messageObject.data || {}
  }, channel.company_id.id, channel.id)

  message.conversation_id = chat

  // Conversation içinde kaydedilen mesajın bilgileri tutuldu
  const newChat = await ChatService.newMessageAdded(chat, message)
  let chatIntegration
  if (channel.integration) {
    chatIntegration = await IntegrationService.getOrCreateChatIntegration(chat, channel.integration)
  }

  // Class ile oluşan mesaj ve diğer bilgiler çıkıldı
  incomingMessageJobResultDto.setCreatedChatMessage(new CreatedChatMessage(channel.company, channel, newChat, channel.integration, message, chatIntegration))

  return incomingMessageJobResultDto

}