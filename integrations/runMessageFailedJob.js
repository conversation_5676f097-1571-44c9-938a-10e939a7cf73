const enums = require('../libs/enums')

const Channel = require('../models/Channel')
const Chat = require('../models/Chat')
const Message = require('../models/Message')
const User = require('../models/User')

const QueueService = require('../services/QueueService')

const DashPresenter = require('../presenters/Dash')

/**
 * @param req
 * @param channelType
 * @param channelProvider
 * @param channelExtId
 * @param messageExtId
 * @param chatExtId
 * @param errorMessage
 *
 * @return {Promise<void>}
 */
module.exports = async (req, channelType, channelProvider, channelExtId, chatExtId, messageExtId, errorMessage, errorData) => {

  const channel = await Channel.findOne({
    type: channelType,
    provider: channelProvider,
    ext_id: channelExtId,
    is_active: true,
    deleted_at: {
      $exists: false
    }
  })

  if (!channel) {
    return
  }

  const chat = await Chat.findOne({
    channel_id: channel._id,
    ext_id: chatExtId,
  }).populate('channel_id')

  if (!chat) {
    return
  }

  const message = await Message.findOne({
    conversation_id: chat._id,
    ext_id: messageExtId
  }).populate('conversation_id')

  if (!message) {
    return
  }


  message.status = enums.message_send_statuses.SENT_FAILED
  message.error_message = errorMessage
  message.error_data = errorData
  await message.save()

  // chat için agent varsa bildirim göndereceğiz
  if (chat.owner_user_id) {

    const agent = await User.findById(chat.owner_user_id)

    if (!agent) {
      return message
    }

    await QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.MESSAGE_SENT_FAILED,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_item: DashPresenter.getChatItem(chat, message, chat.channel, undefined, chat.thinker_status, chat.helobot_status),
        message_item: await DashPresenter.getMessageItemCustomerOrAgent(message, agent),
        notification_sound_status: chat.channel.vSettings.getNotificationSoundStatus()
      }
    }, req.language)

  }

  return message

}
