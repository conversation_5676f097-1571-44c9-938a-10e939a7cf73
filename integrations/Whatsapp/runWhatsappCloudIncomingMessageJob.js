const pino = require('pino')()

const enums = require('../../libs/enums')
const helpers = require('../../libs/helpers')

const WhatsappService = require('./WhatsappService')

const IncomingMessageJobResultDto = require('../../dtos/IncomingMessageJobResultDto')

const CreatedChatMessage = require('../../services/Chat/CreatedChatMessage')

const ChatService = require('../../services/ChatService')
const ChannelService = require('../../services/ChannelService')
const MetaEventService = require('../../services/MetaEventService')

const ChannelRepo = require('../../repos/ChannelRepo')
const MessageRepo = require('../../repos/MessageRepo')

const CompanyHasPackage = require('../../models/CompanyHasPackage')
const ChatReferral = require('../../models/ChatReferral')

const IntegrationService = require('../../modules/AgentApp/IntegrationService')

const WhatsappMessageTemplateActions = require('./WhatsappMessageTemplateActions')

async function SaveReferralClId(messageObject, chat, channel, messageId, traceId) {
  let referral;
  if (messageObject.type === enums.message_types.WHATSAPP_REFERRAL) {
    referral = await ChatReferral.findOne({
      chat_id: chat._id,
      channel_id: channel._id,
      ctwa_clid: messageObject.content.referral.ctwa_clid,
      deleted_at: {
        $exists: false
      }
    })
    if (!referral) {
      referral = await new ChatReferral({
        chat_id: chat._id,
        channel_id: channel._id,
        ctwa_clid: messageObject.content.referral.ctwa_clid,
        message_id: messageId,
        source_id: messageObject.content.referral.source_id
      }).save()

      MetaEventService.SendEvent(channel, chat, enums.data_set_events.LeadSubmitted, traceId)
    }
    chat.chat_referral_id = referral._id
    await chat.save()
  }
}


/**
 * @param req
 * @param {WhatsappMessageDto} dto
 *
 * @return {Promise<IncomingMessageJobResultDto>}
 */
module.exports = async (req, dto) => {

  const incomingMessageJobResultDto = new IncomingMessageJobResultDto()

  // Gelen mesajın channelı var mı kontrol edildi
  const channel = await ChannelRepo.getChannel(enums.channel_types.WHATSAPP_NUMBER, enums.channel_providers.CLOUD, dto.getId())

  if (!channel) {
    pino.info({
      trace_id: req.trace_id,
      timestamp: new Date(),
      message: 'WhatsappIncomingMessageJob channel not found: ' + dto.getId()
    })
    throw new Error('WhatsappIncomingMessageJob channel not found: ' + dto.getId())
  }

  const companyHasPackage = await CompanyHasPackage.findOne({
    company_id: channel.company_id,
    deleted_at: {
      $exists: false
    }
  }).sort({ _id: 1 })
  if (companyHasPackage && companyHasPackage.data.whatsapp) {
    if (helpers.isModuleTimeOut(companyHasPackage.data.whatsapp) !== false) {
      if (channel.vSettings.getWabaAllocationConfigId()) {
        channel.is_active = false
        await channel.save()

        process.nextTick(async () => {
          await ChannelService.removeCreditChannels(req.trace_id, channel.company_id.toString())
        })

        pino.info({
          trace_id: req.trace_id,
          timestamp: new Date(),
          message: 'WhatsappIncomingMessageJob channel süresi dolduğu için pasif edildi ve mesaj işlenmedi',
          data: JSON.stringify({
            channel_id: channel.id,
            channel_ext_id: channel.ext_id
          })
        })

        throw new Error('WhatsappIncomingMessageJob channel süresi dolduğu için pasif edildi ve mesaj işlenmedi')
      }
    }
  }

  // Webhookdan gelen mesaj handle edildi
  const messageObject = await WhatsappService.getMessageObject(dto, channel, req.trace_id)

  // Conversation kaydı yoksa kaydedildi
  const chat = await ChatService.getOrCreateConversation(dto.getItem().getProfileName(), channel.id, dto.getItem().getCustomerId())

  // if (chat.title !== dto.getItem().getProfileName()) {
  //   chat.title = dto.getItem().getProfileName()
  //   await chat.save()
  // }

  if (chat.is_blocked == true) {
    return
  }

  const chatData = chat.vData

  if (!chatData.getChatLangCode()) {

    if (chat.ext_id.substring(0, 2) === '90') {
      req.i18n.language = 'tr'
    }

    if (chat.ext_id.substring(0, 2) === '33') {
      req.i18n.language = 'fr'
    }

    chatData.setChatLangCode(req.i18n.language)
    chat.data = chatData.getData()
    chat.markModified('data')
  }

  // chat eğer gizli ise gizliliği açılacak
  if (chat.hide) {
    chat.hide = false
  }

  await chat.save()

  // Webhookdan gelen mesaj bizim tarafımıza kaydedildi
  const message = await MessageRepo.create({
    type: messageObject.type,
    content: messageObject.content,
    chatId: chat.id,
    extId: dto.getItem().getMessageId(),
    fromType: enums.message_from_types.CUSTOMER,
    sendStatus: enums.message_send_statuses.SENT,
    time: dto.getItem().getTimestamp(),
    data: messageObject.data
  }, channel.company_id.id, channel.id)

  // mesaj da gelen reklam id kaydediliyor
  await SaveReferralClId(messageObject, chat, channel, message._id, req.trace_id)

  message.conversation_id = chat

  // Butonlu Template mesaja cevap verildiğini gösterir
  if (messageObject.type === enums.message_types.WHATSAPP_REPLY_TO_MESSAGE && messageObject.content.button && messageObject.content.type === enums.message_types.WHATSAPP_TEMPLATE) {
    WhatsappMessageTemplateActions.sendActionMessage(messageObject.content.reply_to, dto.getItem().getProfileName(), chat.ext_id, channel.ext_id, messageObject.content.button, req.trace_id, chat, channel.company, channel.id)

    // mesaj template mesajların gelen mesajların cevaplarını message-template projesinede gönderiyoruz. orada raporlama işlemleri yapılıyor.
    WhatsappMessageTemplateActions.sendMessageTemplateWebhook(dto.getItem().getData(), messageObject.data, req.trace_id)
  }

  // Conversation içinde kaydedilen mesajın bilgileri tutuldu
  const newChat = await ChatService.newMessageAdded(chat, message)
  let chatIntegration
  if (channel.integration) {
    chatIntegration = await IntegrationService.getOrCreateChatIntegration(chat, channel.integration)
  }

  const messageText = message.content.text || message.content.caption
  if (messageText) {
    await ChannelService.ChannelAutoTagRecorder(channel._id, chat._id, message._id, messageText)
  }

  // Class ile oluşan mesaj ve diğer bilgiler çıkıldı
  incomingMessageJobResultDto.setCreatedChatMessage(new CreatedChatMessage(channel.company, channel, newChat, channel.integration, message, chatIntegration))

  return incomingMessageJobResultDto

}
