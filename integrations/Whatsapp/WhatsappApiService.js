const fs = require('fs')
const axios = require('axios')
const https = require('https')
const moment = require('moment')
const createError = require('http-errors')
const jsonwebtoken = require('jsonwebtoken')
const BBTag = require('bbcode-parser/bbTag')
const BBCodeParser = require('bbcode-parser')
const pino = require('pino')()
const { HttpsProxyAgent } = require('https-proxy-agent')

const enums = require('../../libs/enums')
const utils = require('../../libs/utils')
const helpers = require('../../libs/helpers')

const WebpImage = require('../../models/WebpImage')
const HeloscopeImage = require('../../models/HeloscopeImage')

const LogService = require("../../services/LogService")
const Integration = require('../../models/Integration')

const Container = require('./../../models/Container')

const proxy = {
  httpsAgent: process.env.META_PROXY_REQUEST_STATUS !== "true" ? undefined : new HttpsProxyAgent(process.env.META_PROXY_REQUEST_URL)
}

const __getInteractiveMessage = async (req, phoneNumber, message, company, channel, chatLangCode) => {
  let messageText = message.content.text

  if (message.vContentBbCode) {
    messageText = await WhatsappApiService.__getBbCodeWhatsappParser(message.content.caption || message.content.text)
  }

  if (message.vContent.buttons.length === 0) {
    throw 'Message Type Error::INTERACTIVE MESSAGE -- Butonlar yok. -> ' + message.vContent.sub_type
  }

  switch (message.vContent.sub_type) {

    case enums.message_types.LIST:

      return {
        to: phoneNumber,
        type: 'interactive',
        recipient_type: "individual",
        interactive: {
          type: 'list',
          header: message.vContent.header || {
            type: 'text',
            text: req.t('Global.chat_message.options', { lng: chatLangCode })
          },
          body: message.vContent.body || {
            text: messageText
          },
          footer: message.vContent.footer || {
            text: company.name
          },
          action: message.vContent.section_name ? {
            button: message.vContent.section_name,
            sections: message.vContent.buttons
          } : {
            button: req.t('Global.chat_message.select', { lng: chatLangCode }),
            sections: message.vContent.buttons
          }
        }
      }

    case enums.message_types.BUTTON:

      let messageData = {
        to: phoneNumber,
        type: 'interactive',
        recipient_type: "individual",
        interactive: {
          header: message.vContent.header,
          type: 'button',
          body: message.vContent.body || {
            text: messageText
          },
          footer: message.vContent.footer || {
            text: company.name
          },
          action: message.vContent.action || {
            buttons: message.vContent.buttons
          }
        }
      }

      if (message.vContent.header.type === 'image') {
        messageData.interactive.header = {
          type: 'image',
          image: {
            link: await WhatsappApiService.__getImageLinkUrl(message, channel, true)
          },
        }
      }

      return messageData

    default:
      throw 'Message Type Error::INTERACTIVE MESSAGE'
  }
}

const WhatsappApiService = {

  sendMessageSpecial: async (req, channel, message) => {

    const config = await WhatsappApiService.getConfigByChannel(channel, '/messages')

    config.data = message

    const response = await axios.request(config)

    LogService.info('WhatsappApiservice::sendMessageTemplate post Whatsapp sendMessageSpecial response', enums.log_channels.BACKEND, { data: response.data })

    let messages = response.data.messages || []

    if (messages.length !== 1) {
      throw 'Whatsapp üzerinden Template mesaj gönderilemedi.'
    }

    return response.data.messages[0].id

  },

  __getImageLinkUrl: async (message, channel, isButtonMessage) => {
    // Webp imagelar için bizim tarafta kaydı yapılıyor ve url whatsapp tarafına veriliyor

    const integration = await Integration.findOne({ _id: channel.integration_id, deleted_at: { $exists: false } })

    if (integration) {
      if (integration.type === enums.INTEGRATION_TYPES.HELOSCOPE) {
        const image = await new HeloscopeImage({
          url: url
        }).save()

        return process.env.BASE_URL + '/heloscope-image-convertor/' + image.id
      }
    }

    const url = isButtonMessage ? message.vContent.header.image.link : message.vContent.url

    // Webp imagelar için bizim tarafta kaydı yapılıyor ve url whatsapp tarafına veriliyor
    if (helpers.isWebpImage(url)) {
      const webpImage = new WebpImage()

      webpImage.url = url

      const image = await webpImage.save()

      return process.env.BASE_URL + '/webp-converter/' + image.id

    } else {
      return url
    }

  },

  getMessageContent: async (req, channel, phoneNumber, company, message, chatLangCode) => {

    let messageData

    switch (message.type) {

      case enums.message_types.TEXT:

        let text = message.vContentText

        // mesajlar BBCODE a göre uyarlanıyor
        if (message.vContentBbCode) {
          text = WhatsappApiService.__getBbCodeWhatsappParser(message.vContentText)
        }

        messageData = {
          to: phoneNumber,
          type: 'text',
          recipient_type: "individual",
          text: {
            body: text,
          }
        }
        break

      case enums.message_types.IMAGE_URL:

        let imageLinkUrl

        // Webp imagelar için bizim tarafta kaydı yapılıyor ve url whatsapp tarafına veriliyor
        if (helpers.isWebpImage(message.vContent.url)) {

          const webpImage = new WebpImage()
          webpImage.url = message.vContent.url
          const image = await webpImage.save()

          imageLinkUrl = process.env.BASE_URL + '/webp-converter/' + image.id

        } else {
          imageLinkUrl = message.vContent.url
        }

        let caption = message.content.caption

        // mesajlar BBCODE a göre uyarlanıyor
        if (message.vContentBbCode) {
          caption = WhatsappApiService.__getBbCodeWhatsappParser(message.content.caption)
        }

        messageData = {
          to: phoneNumber,
          type: 'image',
          recipient_type: "individual",
          image: {
            link: imageLinkUrl,
            caption: caption
          }
        }
        break

      case enums.app_message_types.FILE_URL:
        messageData = {
          to: phoneNumber,
          type: 'document',
          recipient_type: "individual",
          document: {
            link: message.vContent.url,
            filename: message.content.filename
          }
        }
        break

      case enums.app_message_types.VIDEO_URL:
        let mp4caption = message.content.caption

        // mesajlar BBCODE a göre uyarlanıyor
        if (message.vContentBbCode) {
          mp4caption = WhatsappApiService.__getBbCodeWhatsappParser(message.content.caption)
        }

        messageData = {
          to: phoneNumber,
          type: 'video',
          recipient_type: "individual",
          video: {
            link: message.vContent.url,
            caption: mp4caption
          }
        }
        break

      case enums.message_types.WHATSAPP_LOCATION:
        messageData = {
          to: phoneNumber,
          type: 'location',
          recipient_type: "individual",
          location: {
            longitude: message.vContent.longitude,
            latitude: message.vContent.latitude,
            name: message.vContent.name,
            address: message.vContent.address
          }
        }
        break

      case enums.message_types.WHATSAPP_LOCATION_REQUEST:
        messageData = {
          recipient_type: "individual",
          type: 'interactive',
          to: phoneNumber,
          interactive: {
            type: "location_request_message",
            body: {
              text: message.content.text
            },
            action: {
              name: "send_location"
            }
          }
        }
        break

      case enums.app_message_types.AUDIO_URL:
        messageData = {
          to: phoneNumber,
          type: 'audio',
          recipient_type: "individual",
          audio: {
            link: message.vContent.url
          }
        }

        if (channel.provider === enums.channel_providers.TEKROM) {
          let audiocaption = message.content.caption

          // mesajlar BBCODE a göre uyarlanıyor
          if (message.vContentBbCode) {
            audiocaption = WhatsappApiService.__getBbCodeWhatsappParser(message.content.caption)
          }

          messageData.audio.caption = audiocaption || ''
        }
        break

      case enums.message_types.WHATSAPP_INTERACTIVE:

        messageData = await __getInteractiveMessage(req, phoneNumber, message, company, channel, chatLangCode)
        break

      case enums.message_types.WHATSAPP_REPLY_TO_MESSAGE:

        messageData = {
          to: phoneNumber,
          type: 'text',
          recipient_type: "individual",
          context: {
            message_id: message.content.reply_to
          },
          text: {
            body: message.vContentText,
          }
        }
        break

      default:
        throw 'Message Type Error'
    }

    return messageData
  },

  sendMessage: async (req, channel, phoneNumber, company, message, chatLangCode) => {

    const messageData = await WhatsappApiService.getMessageContent(req, channel, phoneNumber, company, message, chatLangCode)

    if (channel.provider === enums.channel_providers.CLOUD) {
      messageData.messaging_product = "whatsapp"

      return WhatsappApiService.sendMessageCloudRequest(req, channel, messageData)
    }

    return WhatsappApiService.sendMessageRequest(req, channel, phoneNumber, messageData)
  },

  sendMessageCloudRequest: async (req, channel, messageData) => {

    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${channel.vSettings.getPhoneNumberId()}/messages?access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'POST',
      data: messageData,
      timeout: 10000
    }

    // Mesaj whatsapp containerına gönderiliyor
    const response = await axios.request(config)

    pino.info({
      trace_id: req.trace_id,
      channel_id: channel.id,
      message: 'Whatsapp send message',
      timestamp: new Date(),
      config: JSON.stringify({
        url: config.url,
        method: config.method,
        headers: config.headers,
      }),
      data: JSON.stringify(messageData),
      response: JSON.stringify(response.data),
    })

    let messages = response.data.messages || []

    if (messages.length !== 1) {
      throw 'Whatsapp üzerinden mesaj gönderilemedi.'
    }

    // Whatsappa gönderilen mesajın ID si alınıyor
    return response.data.messages[0].id
  },

  sendMessageRequest: async (req, channel, phoneNumber, messageData) => {
    // İstek atılacak http bilgileri alınıyor
    const config = await WhatsappApiService.getConfigByChannel(channel, '/messages')

    config.data = messageData
    config.timeout = 10000

    // Mesaj whatsapp containerına gönderiliyor
    const response = await axios.request(config)

    pino.info({
      trace_id: req.trace_id,
      channel_id: channel.id,
      message: 'Whatsapp send message',
      timestamp: new Date(),
      config: JSON.stringify({
        url: config.url,
        method: config.method,
        headers: config.headers,
        withCredentials: config.withCredentials
      }),
      response: JSON.stringify(response.data),
      chat_ext_id: phoneNumber
    })

    let messages = response.data.messages || []

    if (messages.length !== 1) {
      throw 'Whatsapp üzerinden mesaj gönderilemedi.'
    }

    // Whatsappa gönderilen mesajın ID si alınıyor
    return response.data.messages[0].id
  },

  markAsSeen: (req, channel, message) => {

    return WhatsappApiService.getConfigByChannel(channel, '/messages/' + message.ext_id).then(config => {

      config.data = {
        status: 'read'
      }

      config.method = 'PUT'

      return axios.request(config).then(response => {

        pino.info({
          trace_id: req.trace_id,
          channel_id: channel.id,
          message: 'post TEKROM Whatsapp MarkAsRead success response',
          timestamp: new Date(),
          config: JSON.stringify({
            url: config.url,
            method: config.method,
            headers: config.headers,
          }),
          response: JSON.stringify(response.data)
        })

      }).catch(error => {

        pino.error({
          trace_id: req.trace_id,
          channel_id: channel.id,
          message: 'post TEKROM Whatsapp MarkAsRead error response',
          timestamp: new Date(),
          config: JSON.stringify({
            url: config.url,
            method: config.method,
            headers: config.headers,
          }),
          error: JSON.stringify(error.response?.data || { message: 'Beklenmedik Hata Oluştu' })
        })

      })

    })

  },

  markAsSeenCloud: (req, channel, messageExtId) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${channel.vSettings.getPhoneNumberId()}/messages?access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'POST',
      data: {
        messaging_product: "whatsapp",
        status: "read",
        message_id: messageExtId
      }
    }

    return axios.request(config).then(response => {

      pino.info({
        trace_id: req.trace_id,
        channel_id: channel.id,
        message: 'post CLOUD Whatsapp MarkAsRead success response',
        timestamp: new Date(),
        config: JSON.stringify({
          url: config.url,
          method: config.method,
          headers: config.headers,
          message_ext_id: messageExtId
        }),
        response: JSON.stringify(response.data)
      })

    }).catch(error => {

      pino.error({
        trace_id: req.trace_id,
        channel_id: channel.id,
        message: 'post CLOUD Whatsapp MarkAsRead error response',
        timestamp: new Date(),
        config: JSON.stringify({
          url: config.url,
          method: config.method,
          headers: config.headers,
          message_ext_id: messageExtId
        }),
        error: JSON.stringify(error.response?.data || { message: 'Beklenmedik Hata Oluştu' })
      })

    })


  },

  updateProfilePhoto: (req, file, channel) => {

    return WhatsappApiService.getConfigByChannel(channel, '/settings/profile/photo').then(config => {

      config.method = 'POST'

      config = helpers.addFileToAxiosConfigForWhatsApp(file, config)

      return axios.request(config).then(response => {

        pino.info({
          trace_id: req.trace_id,
          channel_id: channel.id,
          message: 'post TEKROM Whatsapp updateProfilePhoto success response',
          timestamp: new Date(),
          config: JSON.stringify({
            url: config.url,
            method: config.method,
            headers: config.headers,
            withCredentials: config.withCredentials
          }),
          data: JSON.stringify(response.data)
        })

      }).catch(error => {

        pino.error({
          trace_id: req.trace_id,
          channel_id: channel.id,
          message: 'post TEKROM Whatsapp updateProfilePhoto error',
          timestamp: new Date(),
          config: JSON.stringify({
            url: config.url,
            method: config.method,
            headers: config.headers,
            withCredentials: config.withCredentials
          }),
          error: JSON.stringify(error.response?.data || { message: 'Beklenmedik Hata Oluştu' })
        })

      }).finally(() => {

        fs.unlink(file.path, err => {
        })

      })

    })

  },

  __getBbCodeWhatsappParser: (content) => {

    return new BBCodeParser({
      'BR': BBTag.createTag('BR', (tag, content, attr) => {
        return "\n";
      }),
      'SPACE': BBTag.createTag('SPACE', (tag, content, attr) => {
        return ''.padEnd(content, ' ')
      }),
      'UNDERLINE': BBTag.createTag('UNDERLINE', (tag, content, attr) => {
        return ''.padEnd(content, '_')
      }),
      'B': BBTag.createTag('B', (tag, content, attr) => {
        return " *" + content + "* "
      }),
      'S': BBTag.createTag('S', (tag, content, attr) => {
        return " ~" + content + "~ "
      })
    }).parseString(content).replace(/\&amp;/g, '\&').replace(/(<br>)/gm, "\n")

  },

  //ToDo bunu kullanan yerleri kontrol etmeyti unutma !
  getSendMessageContent: (data) => {

    const messageData = {
      caption: data.caption,
      bb_code: true,
      url: data.url
    }

    if ('hide_image' in data) {
      messageData.hide_image = data.hide_image
    }

    return messageData
  },

  /**
   *
   * @param {Container} container
   *
   * @return {Promise<container>}
   *
   * @constructor
   */
  renewToken: container => {

    const decryptedPassword = helpers.decrypt(process.env.APP_SECRET_KEY, container.password_salt, container.password)

    const config = {
      url: container.vBaseUrl + '/v1/users/login',
      method: 'POST',
      auth: {
        username: 'admin',
        password: decryptedPassword
      }
    }

    pino.info({
      message: 'Whatsapp token süresi dolmuş yenileniyor: ' + container.number,
      timestamp: new Date(),
      config: JSON.stringify(config),
      container_id: container.id
    })

    return helpers.axiosRequest(config).then(response => {

      container.token = response.data.users[0].token
      container.token_expires_at = response.data.users[0].expires_after

      return container.save()

    }).catch(err => {

      pino.error({
        message: 'Whatsapp token yenilenemedi: ' + container.number,
        timestamp: new Date(),
        config: JSON.stringify(config),
        error: JSON.stringify(err.response?.data || { message: 'Beklenmedik Hata Oluştu' })
      })
      if (err.response && err.response.status === 401) {

        throw new createError.Unauthorized('Kullanıcı adı şifre yanlış')

      }

      throw err

    })

  },

  getContainerByChannel: (apiKey) => {

    const decodedApiKey = jsonwebtoken.decode(apiKey)

    if (!decodedApiKey.code) {
      throw new createError.NotFound('Api key decode edilemedi.')
    }

    return WhatsappApiService.getContainerByCode(decodedApiKey.code)

  },

  getContainerByCode: (code) => {

    return Container.findOne({ 'code': code, deleted_at: { $exists: false } }).then(container => {

      if (!container) {
        throw new createError.NotFound('Api key içerisinde belirtilen code için container bulunamadı.')
      }

      return Promise.resolve().then(() => {

        if (!container.token || moment().isAfter(moment(container.token_expires_at))) {

          return WhatsappApiService.renewToken(container)

        }

        return container

      })

    })

  },

  getConfigByChannel: (channel, endpoint, method = 'POST') => {

    return WhatsappApiService.getContainerByChannel(channel.vSettings.getApiKey()).then(container => {

      return {
        url: container.vBaseUrl + '/v1' + endpoint,
        method: method,
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + container.token
        },
        httpsAgent: new https.Agent({
          rejectUnauthorized: false
        })
      }

    })

  },

  containerNewPassword: async (url, key) => {

    const response = await axios.post(`${url}/v1/users/login`, { new_password: key }, {
      auth: {
        username: 'admin',
        password: 'secret'
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    })

    return response.data
  },

  containerLogin: async (url, key) => {

    const response = await axios.post(`${url}/v1/users/login`, {}, {
      timeout: 3000,
      auth: {
        username: 'admin',
        password: key
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    })

    if (response.data.users.length === 0) {
      return false
    }

    return {
      token: response.data.users[0].token,
      expires_after: response.data.users[0].expires_after
    }
  },

  changeWebhookUrl: async (url, token, code) => {

    const config = {
      url: `${url}/v1/settings/application`,
      method: 'PATCH',
      data: {
        webhooks: {
          url: `${process.env.WEBHOOK_URL}/webhook/whatsapp/${code}`
        }
      },
      headers: {
        'Authorization': `Bearer ${token}`
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return await axios.request(config)

  },

  sendConfirmCode: (url, data, token) => {

    let config = {
      url: `${url}/v1/account`,
      method: 'POST',
      data: data,
      headers: {
        'Authorization': `Bearer ${token}`
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return axios.request(config).then(response => response.data).catch(err => {
      throw new createError.BadRequest(err.response.data.errors[0].details)
    })
  },

  sendConfirmCodeForCloud: async (phoneNumber, codeMethod) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${phoneNumber}/request_code?access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'POST',
      data: {
        code_method: codeMethod,
        language: "tr"
      }
    }
    return axios.request(config).then(response => response.data)
  },

  confirmCode: (url, code, token) => {

    let config = {
      url: `${url}/v1/account/verify`,
      method: 'POST',
      data: {
        code: code
      },
      headers: {
        'Authorization': `Bearer ${token}`
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return axios.request(config).then(response => response.data)
  },

  verifyConfirmCodeForCloud: async (phoneNumber, code) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${phoneNumber}/verify_code?access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'POST',
      data: {
        code: code
      }
    }
    return axios.request(config).then(response => response.data)
  },

  setWebhookUrl: (url, containerCode, token) => {

    let config = {
      url: `${url}/v1/settings/application`,
      method: 'PATCH',
      data: {
        webhooks: {
          url: `${process.env.WEBHOOK_URL}/webhook/whatsapp/${containerCode}`
        }
      },
      headers: {
        'Authorization': `Bearer ${token}`
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return axios.request(config).then(response => response.data)
  },

  getOwnerWaBaId: async () => {

    const data = await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${process.env.TSOFT_BUSINESS_ID}?fields=owned_whatsapp_business_accounts.limit(1000){phone_numbers{display_phone_number}}&access_token=${process.env.FACEBOOK_BSP_ACCESS_TOKEN}`)

    return data.data.owned_whatsapp_business_accounts.data.map(item => {

      if (item.phone_numbers) {
        return {
          id: item.id,
          numbers: item.phone_numbers.data.map(item2 => {
            return item2.display_phone_number.substring(1, item2.display_phone_number.length).replace(new RegExp(/ /g), '')
          })
        }
      }
      return false
    }).filter(item => item != false)
  },

  getClientWaBaId: async () => {

    const data = await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${process.env.TSOFT_BUSINESS_ID}/client_whatsapp_business_accounts?fields=phone_numbers{display_phone_number}&limit=1000&access_token=${process.env.FACEBOOK_BSP_ACCESS_TOKEN}`)

    return data.data.data.map(item => {

      if (item.phone_numbers) {
        return {
          id: item.id,
          numbers: item.phone_numbers.data.map(item2 => {
            return item2.display_phone_number.substring(1, item2.display_phone_number.length).replace(new RegExp(/ /g), '')
          })
        }
      }
      return false
    }).filter(item => item != false)
  },

  getWhatsappPhoneNumbers: async (wabaId, accessToken) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${wabaId}?fields=phone_numbers.limit(100){
      id,
      status,
      name_status,
      account_mode,
      new_name_status,
      display_phone_number,
      code_verification_status
      }&access_token=${accessToken}`).then(response => response.data)
  },

  getPhoneNumbersAsHeloRobo: async (wabaId) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${wabaId}?fields=phone_numbers.limit(100){
      id,
      status,
      name_status,
      account_mode,
      new_name_status,
      display_phone_number,
      code_verification_status,
      certificate
    }&access_token=${process.env.FACEBOOK_BSP_ACCESS_TOKEN}`).then(response => response.data)
  },

  setWhatsappAbout: async (req, channel, about) => {
    const config = await WhatsappApiService.getConfigByChannel(channel, '/settings/profile/about')

    config.data = {
      text: about
    }

    config.method = 'PATCH'

    return axios.request(config).then(response => {
      LogService.info('post TEKROM Whatsapp MarkAsRead success response', enums.log_channels.BACKEND, {
        config: response.config,
        data: response.data
      })
    }).catch(error => {
      LogService.error('post TEKROM Whatsapp MarkAsRead error', enums.log_channels.BACKEND, {
        config: error.response.config,
        data: error.response.data
      })
    })
  },

  getConversationLimit: async (wabaId, startDate, endDate) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${wabaId}?fields=conversation_analytics.start(${startDate}).end(${endDate}).dimensions(["CONVERSATION_CATEGORY","CONVERSATION_TYPE","COUNTRY","PHONE"]).granularity(DAILY)&access_token=${process.env.HELOROBO_BSP_ACCESS_TOKEN}`,
      method: 'GET'
    }

    return axios.request(config).then(response => response.data)
  },

  getMessageLimit: async (wabaId, startDate, endDate) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${wabaId}?fields=pricing_analytics.start(${startDate}).end(${endDate}).granularity(DAILY).dimensions(PRICING_CATEGORY,PRICING_TYPE)&access_token=${process.env.HELOROBO_BSP_ACCESS_TOKEN}`,
      method: 'GET'
    }

    return axios.request(config).then(response => response.data)
  },

  GetNumberWabaId: async (number) => {
    let numbers = await WhatsappApiService.getOwnerWaBaId()
    let getWabaId = numbers.find(item => item.numbers.includes(number))

    // Ownerlar içerisinde waba_id buluanamzsa clientlerde aranıyor
    if (!getWabaId) {
      numbers = await WhatsappApiService.getClientWaBaId()
    }

    getWabaId = numbers.find(item => item.numbers.includes(number))

    return getWabaId?.id || ''
  },

  subscribedApp: async (wabaId) => {
    return await axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${wabaId}/subscribed_apps?access_token=${process.env.HELOROBO_BSP_ACCESS_TOKEN}`)
      .catch(err => {
        throw err.response?.data?.error?.message
      })
  },

  addSystemUserToWaba: async (wabaId) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${wabaId}/assigned_users?user=${process.env.HELOROBO_SYSTEM_USER_ID}&tasks=['MANAGE']&access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'POST',
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return await axios.request(config).catch(err => {
      throw err.response?.data?.error?.message
    })
  },

  addPaymentMethodToWaba: async (wabaId) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${process.env.TEKROM_CREDIT_LINE_ID}/whatsapp_credit_sharing_and_attach?waba_id=${wabaId}&waba_currency=USD&access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'POST',
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return await axios.request(config).then(response => response.data).catch(err => {
      throw err.response?.data?.error?.message
    })
  },

  getWhatsappCertificateByPhoneNumberId: async (phoneNumberId) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${phoneNumberId}?fields=id,certificate,display_phone_number,new_certificate&access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'GET',
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return await axios.request(config).then((response) => response.data).catch(err => {
      throw err.response?.data?.error?.message
    })
  },

  revokeCreditLine: async (allocationConfigId) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${allocationConfigId}?access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'DELETE',
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return await axios.request(config)
  },

  registerToCloud: async (phoneNumberId, pin) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${phoneNumberId}/register?access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'POST',
      data: {
        messaging_product: "whatsapp",
        pin: pin
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return await axios.request(config)
  },

  getBusinessProfile: async (phoneNumberId) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${phoneNumberId}/whatsapp_business_profile?fields=about,address,description,email,profile_picture_url,websites,vertical&access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'GET',
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return await axios.request(config).then((response) => response.data).catch(err => {
      throw err.response?.data?.error?.message
    })
  },

  updateBusinessProfile: async (phoneNumberId, data) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${phoneNumberId}/whatsapp_business_profile?access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'POST',
      data: data,
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return await axios.request(config).then((response) => response.data).catch(err => {
      throw err.response?.data?.error?.message
    })
  },

  DisableTwoVerification: async (channel) => {
    const config = await WhatsappApiService.getConfigByChannel(channel, '/settings/account/two-step', 'DELETE')

    return axios.request(config)
  },

  deRegisterToCloud: async (phoneNumberId) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${phoneNumberId}/deregister?access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'POST',
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return await axios.request(config)
  },

  reactToMessage: async (phoneNumberId, to, messageId, emoji) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${phoneNumberId}/messages?access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'POST',
      data: {
        messaging_product: "whatsapp",
        recipient_type: "individual",
        to: to,
        type: "reaction",
        reaction: {
          message_id: messageId,
          emoji: emoji
        }
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return await axios.request(config).then(response => {
      pino.info({
        message: 'Whatsapp react to message',
        timestamp: new Date(),
        config: JSON.stringify({
          phoneNumberId: phoneNumberId,
          to: to,
          messageId: messageId
        }),
        response: JSON.stringify(response.data)
      })

      return response.data
    })
  },

  unReactToMessage: async (phoneNumberId, to, messageId) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${phoneNumberId}/messages?access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'POST',
      data: {
        messaging_product: "whatsapp",
        recipient_type: "individual",
        to: to,
        type: "reaction",
        reaction: {
          message_id: messageId,
          emoji: ''
        }
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return await axios.request(config).then(response => {
      pino.info({
        message: 'Whatsapp react to message',
        timestamp: new Date(),
        config: JSON.stringify({
          phoneNumberId: phoneNumberId,
          to: to,
          messageId: messageId
        }),
        response: JSON.stringify(response.data)
      })

      return response.data
    })
  },

  twoStepVerificationCloud: async (phoneNumberId, pin) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${phoneNumberId}?access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'POST',
      data: {
        pin: pin
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return axios.request(config).then(response => response.data)
  },

  twoStepVerificationTekrom: async (channel, pin) => {
    const config = await WhatsappApiService.getConfigByChannel(channel, '/settings/account/two-step', 'POST')

    config.data = {
      pin: pin
    }

    return axios.request(config).then(response => response.data)
  },

  sendEventData: async (addId, wabaId, eventId, customData, eventName, traceId) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${eventId}/events?access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'POST',
      data: {
        data: [
          {
            event_name: eventName,
            event_id: utils.CreateRandomNumbers(),
            event_time: moment().unix(),
            action_source: "business_messaging",
            user_data: {
              ctwa_clid: addId,
              whatsapp_business_account_id: wabaId
            },
            messaging_channel: "whatsapp",
            custom_data: customData,
            original_event_data: {
              event_name: eventName,
              event_time: moment().unix()
            }
          }
        ]
      },
      ...proxy
    }

    return axios.request(config).then(response => response.data).catch(error => {

      delete config.httpsAgent

      pino.error({
        trace_id: traceId,
        timestamp: new Date(),
        message: 'Instagram Event Data Gönderme İşleminde Hata Oluştu',
        error: JSON.stringify(error.response?.data || { message: 'istek yapılamadı' }),
        data: JSON.stringify(config)
      })

      throw error
    })
  }

}

module.exports = WhatsappApiService
