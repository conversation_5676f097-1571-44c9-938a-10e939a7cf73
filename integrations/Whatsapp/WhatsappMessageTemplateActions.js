const pino = require('pino')()

const enums = require('../../libs/enums')

const ThinkerService = require('../../services/ThinkerService')
const MessageTemplateService = require('../../services/MessageTemplateService')
const QueueService = require('../../services/QueueService')

const MessageTemplateActionMessages = require('../../models/MessageTemplateActionMessages')
const MessageTemplateAction = require('../../models/MessageTemplateAction')
const MessageTemplate = require('../../models/MessageTemplate')
const User = require('../../models/User')


const WhatsappMessageTemplateActions = {
  sendMessageTemplateWebhook: async (messageData, sendWizard, traceId) => {
    MessageTemplateService.SendWebhook(sendWizard.send_wizard_id, messageData, traceId).then(() => {
      pino.info({
        trace_id: traceId,
        timestamp: new Date(),
        message: `MESSAGE_TEMPLATE'E WEBHOOK GONDERILDI -> SEND_WIZARD_ID: ${sendWizard.send_wizard_id}`
      })
    }).catch(err => {
      pino.error({
        trace_id: traceId,
        timestamp: new Date(),
        error: JSON.stringify(err.response?.data || { message: err.message + 'İstek Atılamadı' }),
        message: `MESSAGE_TEMPLATE'E WEBHOOK GONDERILEMEDI -> SEND_WIZARD_ID: ${sendWizard.send_wizard_id}`
      })
    })
  },
  sendActionMessage: async (messageId, fullName, userPhone, channelNumber, messageResponse, traceId, chat, company, channelId) => {

    const messageTemplateActionMessage = await MessageTemplateActionMessages.findOne({ message_id: messageId })

    if (messageTemplateActionMessage) {
      const messageTemplateAction = await MessageTemplateAction.findById(messageTemplateActionMessage.message_template_action_id)

      try {
        if (messageTemplateAction) {

          const responseText = messageTemplateAction.buttons.find(item => item.button_text == messageResponse.text)

          if (responseText) {

            // thinker flow a atama işlemi yapılıyor
            if (responseText.flow_id) {
              try {
                await ThinkerService.StartProcedure(chat, company, channelId, responseText.flow_id, traceId, messageResponse.text, 'now')

                if (chat.owner_user_id) {
                  const agent = await User.findById(chat.owner_user_id)

                  return QueueService.publishToAppSocket({
                    event: enums.agent_app_socket_events.THINKER_BOT_STARTED,
                    socket_rooms: [agent.vSocketCode],
                    data: {
                      chat_id: chat.id
                    }
                  }, 'tr')
                }
                return
              } catch (err) {
                pino.error({
                  trace_id: traceId,
                  timestamp: new Date(),
                  error: JSON.stringify(err.response?.data || { message: 'Axios hatası. istek atılamadı' }),
                  message: `BOT MESSAGE_TEMPLATE THINKERA AKTARILAMADI -> PHONE_NUMBER: ${channelNumber} Error`
                })
                return
              }
            }

            // template mesaj gönderimi yapılıyor
            if (responseText.template_id && responseText.channel_id) {
              const messageTemplateAccount = await MessageTemplate.findOne({ company_id: company._id, deleted_at: { $exists: false } })
              if (!messageTemplateAccount) {
                return
              }

              MessageTemplateService.SingleMessage(messageTemplateAccount, {
                channel_id: responseText.channel_id,
                full_name: fullName,
                phone_number: userPhone,
                message_template_id: responseText.template_id
              }, 'tr', traceId).then(() => {
                pino.info({
                  trace_id: traceId,
                  timestamp: new Date(),
                  message: `BOT MESSAGE_TEMPLATE GONDERILDI -> SEND_WIZARD_ID: ${messageTemplateAction.send_wizard_id}`
                })
              }).catch(err => {
                pino.error({
                  trace_id: traceId,
                  timestamp: new Date(),
                  error: JSON.stringify(err.response?.data || { message: 'Axios hatası. istek atılamadı' }),
                  message: `BOT MESSAGE_TEMPLATE -> PHONE_NUMBER: ${channelNumber} Error`
                })
              })
              return
            }
          }
        }
      } catch (error) {
        pino.error({
          trace_id: traceId,
          timestamp: new Date(),
          error: JSON.stringify(error.response?.data || { message: 'İstek Atılamadı' }),
          message: `BOT MESSAGE_TEMPLATE -> PHONE_NUMBER: ${channelNumber} Error`
        })
      }
    }
  }
}

module.exports = WhatsappMessageTemplateActions