const async = require('async')
const pino = require('pino')()

const MessageSeenJob = require('../../jobs/MessageSeenJob')
const MessageSentJob = require('../../jobs/MessageSentJob')
const MessageReactedJob = require('../../jobs/MessageReactedJob')
const MessageFailedJob = require('../../jobs/MessageFailedJob')
const MessageDeletedJob = require('../../jobs/MessageDeletedJob')
const IncomingMessageJob = require('../../jobs/IncomingMessageJob')
const MessageDeliveredJob = require('../../jobs/MessageDeliveredJob')

const WhatsappStatusDto = require('./SDK/WhatsappStatusDto')
const WhatsappMessageDto = require('./SDK/WhatsappMessageDto')
const WhatsappWebhookBody = require('./SDK/WhatsappWebhookBody')

const IncomingWebhookJobResultDto = require('../../dtos/IncomingWebhookJobResultDto')

const Session = require('../../models/Session')
const Channel = require('../../models/Channel')
const enums = require('../../libs/enums')

const getOrCreateSession = async (sessionId, channelNumber, chatNumber) => {
  const sessionDoc = await Session.findOne({
    session_id: sessionId,
    chat_ext_id: chatNumber
  })

  if (sessionDoc) {
    return
  }

  return Session.create({
    channel_number: channelNumber,
    chat_ext_id: chatNumber,
    session_id: sessionId
  })
}

module.exports = async (req, body) => {

  const incomingWebhookJobResultDto = new IncomingWebhookJobResultDto()

  const whatsappWebhookBody = new WhatsappWebhookBody(body)

  // Zeliha ve Sıla Container ise direk development ortamına gönderelim.
  if (['905367132150', '905388755330', '14318311104'].includes(whatsappWebhookBody.getTo())) {
    incomingWebhookJobResultDto.setHasDevelopmentContent(true)
    incomingWebhookJobResultDto.setSenderId(whatsappWebhookBody.getSenderId())
  }

  const channel = await Channel.findOne({
    ext_id: whatsappWebhookBody.getTo(),
    is_active: true,
    deleted_at: {
      $exists: false
    }
  }).populate('company_id')
  if (!channel) {
    throw new Error('Kanal Bulunamadı')
  }

  if (channel.company?.is_active === false) {
    throw new Error('Şirket Pasif Olduğu için Mesaj Alınmadı')
  }

  if (channel.company_id.vData.getUsingAsBridge()) {
    incomingWebhookJobResultDto.setUsingAsBridge(channel.company_id.vData.getUsingAsBridge())
    incomingWebhookJobResultDto.setWebhookUrl(channel.company_id.vData.getDataWebhookUrl())
    incomingWebhookJobResultDto.setWebhookUrlHash(channel.company_id.vData.getDataWebhookUrlHash())
  }

  return Promise.resolve().then(() => {

    if (!whatsappWebhookBody.hasStatuses()) {
      return false
    }

    return async.series(whatsappWebhookBody.getStatuses().map(item => async () => {

      const whatsappStatusData = WhatsappStatusDto.createFromWhatsapp(whatsappWebhookBody.getTo(), item.getData()).to()

      pino.info({
        message: 'WhatsappIncoming Status Webhook',
        trace_id: req.trace_id,
        data: JSON.stringify(whatsappWebhookBody.getData()),
        channel_id: whatsappWebhookBody.getTo(),
        chat_id: whatsappWebhookBody.getSenderId()
      })

      // Session kontrol ediliyor
      if (item.data.conversation) {
        getOrCreateSession(item.data.conversation.id, whatsappWebhookBody.getTo(), whatsappWebhookBody.getSenderId())
      }

      if (item.isSent()) {

        return req.app.services.JobService.addMessageSentJob(
          req,
          MessageSentJob.TYPE_WHATSAPP,
          whatsappStatusData,
        ).then(() => true)

      }

      if (item.isDelivered()) {

        return req.app.services.JobService.addMessageDeliveredJob(
          req,
          MessageDeliveredJob.TYPE_WHATSAPP,
          whatsappStatusData,
        ).then(() => true)

      }

      if (item.isSeen()) {

        return req.app.services.JobService.addMessageSeenJob(
          req,
          MessageSeenJob.TYPE_WHATSAPP,
          whatsappStatusData,
        ).then(() => true)

      }

      if (item.isFailed()) {

        return req.app.services.JobService.addMessageFailedJob(
          req,
          MessageFailedJob.TYPE_WHATSAPP,
          whatsappStatusData,
        ).then(() => true)

      }

      if (item.isDeleted()) {

        return req.app.services.JobService.addMessageDeletedJob(
          req,
          MessageDeletedJob.TYPE_WHATSAPP,
          whatsappStatusData,
        ).then(() => true)

      }

      pino.info({
        message: 'WhatsappIncomingWebhookJob içinde handle edilmeyen status type',
        trace_id: req.trace_id,
        data: JSON.stringify(whatsappWebhookBody.getData()),
        channel_id: whatsappWebhookBody.getTo()
      })

      return false

    })).then(results => !results.includes(false))

  }).then(statusResult => {

    return Promise.resolve().then(() => {

      if (!whatsappWebhookBody.hasMessages()) {
        return false
      }

      return async.series(whatsappWebhookBody.getMessages().map(item => async () => {

        if (item.hasError()) {
          pino.error({
            message: 'WhatsappIncomingWebhookJob içinde error geldi',
            trace_id: req.trace_id,
            data: JSON.stringify(whatsappWebhookBody.getData()),
            channel_id: whatsappWebhookBody.getTo(),
            error: JSON.stringify(item)
          })

          if (item.getMessageType() !== enums.message_types.WHATSAPP_REFERRAL) {
            return false
          }
        }

        if (item.getMessageType() === enums.message_types.TEXT) {
          if (!item.getMessageText()) {
            return
          }
        }

        pino.info({
          message: 'WhatsappIncoming Status Webhook',
          trace_id: req.trace_id,
          data: JSON.stringify(item.getData()),
          channel_id: whatsappWebhookBody.getTo(),
          chat_id: whatsappWebhookBody.getSenderId()
        })

        return req.app.services.JobService.addIncomingMessageJob(
          req,
          IncomingMessageJob.TYPE_WHATSAPP,
          WhatsappMessageDto.createFromWhatsapp(whatsappWebhookBody.getTo(), item.getData()).to()
        ).then(() => true)

      })).then(results => !results.includes(false))

    }).then(messageResult => {

      // console.log(statusResult)
      // console.log(messageResult)
      // console.log(errorResult)

      return incomingWebhookJobResultDto

    })

  })

}
