class WhatsappStatusItem {

  constructor(data) {
    this.data = data
  }

  getData() {
    return this.data
  }

  getMessageId() {
    return this.data.id
  }

  getCustomerId() {
    return this.data.message.recipient_id
  }

  isDeleted() {
    return this.data.status === 'deleted'
  }

  isFailed() {
    return this.data.status === 'failed'
  }

  isSent() {
    return this.data.status === 'sent'
  }

  isDelivered() {
    return this.data.status === 'delivered'
  }

  isSeen() {
    return this.data.status === 'read'
  }

  getErrorMessage(req) {

    if ('errors' in this.data) {

      if (Array.isArray(this.data.errors)) {

        if (this.data.errors.length > 0) {

          const errorItem = this.data.errors[0]

          let message = ''
          if ('code' in errorItem) {
            message = 'code: ' + errorItem.code.toString()
          }
          if ('title' in errorItem) {
            message += ' -- title: ' + errorItem.title
          }
          if (errorItem.message !== errorItem.title) {
            message += ' -- message: ' + errorItem.message
          }
          if ('error_data' in errorItem) {
            message += ' -- details: ' + errorItem.error_data.details
          }

          return message
        }

      }

    }

    return req.t('Global.errors.whatsapp_message_error')

  }

}

module.exports = WhatsappStatusItem
