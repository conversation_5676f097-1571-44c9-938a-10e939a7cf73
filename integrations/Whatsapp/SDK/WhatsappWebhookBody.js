const WhatsappStatusItem = require('./WhatsappStatusItem')
const WhatsappMessageItem = require('./WhatsappMessageItem')

class WhatsappWebhookBody {

  constructor(data) {
    this.data = data
  }

  getData() {
    return this.data
  }

  /**
   *
   * @return {boolean}
   */
  hasMessages() {

    if ('messages' in this.data) {

      return Array.isArray(this.data.messages)

    }

    return false

  }

  /**
   * @return {WhatsappMessageItem[]}
   */
  getMessages() {

    if ('messages' in this.data) {

      if (Array.isArray(this.data.messages)) {

        return this.data.messages.map((item, index) => {

          item.profile_name = this.data.contacts[index].profile.name

          return new WhatsappMessageItem(item)

        })

      }

    }

    return []

  }

  /**
   *
   * @return {boolean}
   */
  hasStatuses() {

    if ('statuses' in this.data) {

      return Array.isArray(this.data.statuses)

    }

    return false

  }

  /**
   * @return {WhatsappStatusItem[]}
   */
  getStatuses() {

    if ('statuses' in this.data) {

      if (Array.isArray(this.data.statuses)) {

        return this.data.statuses.map(item => new WhatsappStatusItem(item))

      }

    }

    return []

  }

  /**
   *
   * @return {boolean}
   */
  hasErrors() {

    if ('errors' in this.data) {

      return Array.isArray(this.data.errors)

    }

    return false

  }

  /**
   * @return {[]}
   */
  getErrors() {

    if ('errors' in this.data) {

      if (Array.isArray(this.data.errors)) {

        return this.data.errors

      }

    }

    return []

  }

  getTo() {
    return this.data.to
  }

  getSenderId() {

    if ('contacts' in this.data) {
      return this.data.contacts[0].wa_id
    }
    
    if ('statuses' in this.data) {
      return this.data.statuses[0].message.recipient_id
    }

    if ('data' in this.data) {
      return this.data.data.recipient_id
    }

    return undefined
  }

}

module.exports = WhatsappWebhookBody
