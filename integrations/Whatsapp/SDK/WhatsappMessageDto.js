const WhatsappMessageItem = require('./WhatsappMessageItem')

class WhatsappMessageDto {

  /**
   * @param id
   * @param data
   *
   * @private
   */
  constructor(id, data) {

    // whatsapp number
    this.id = id

    // WhatsappMessageItem için kullanılmak üzere data
    this.data = data


  }

  /**
   * @param {string} id
   * @param {object} data
   *
   * @return {WhatsappMessageDto}
   */
  static createFromWhatsapp(id, data) {
    return new WhatsappMessageDto(id, data)
  }

  static createFromData(data) {
    return new WhatsappMessageDto(data.id, data.data)
  }

  to() {

    return {
      id: this.id,
      data: this.data
    }

  }

  /**
   * @return {string}
   */
  getId() {
    return this.id
  }

  /**
   * @return {WhatsappMessageItem}
   */
  getItem() {
    return new WhatsappMessageItem(this.data)
  }

}

module.exports = WhatsappMessageDto
