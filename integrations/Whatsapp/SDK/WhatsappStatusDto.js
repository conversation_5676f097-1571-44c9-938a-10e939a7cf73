const WhatsappStatusItem = require('./WhatsappStatusItem')

class WhatsappStatusDto {

  /**
   * @param id
   * @param data
   *
   * @private
   */
  constructor(id, data) {

    // whatsapp number
    this.id = id

    // WhatsappStatusItem için kullanılmak üzere data
    this.data = data


  }

  /**
   * @param {string} id
   * @param {object} data
   *
   * @return {WhatsappStatusDto}
   */
  static createFromWhatsapp(id, data) {
    return new WhatsappStatusDto(id, data)
  }

  static createFromData(data) {
    return new WhatsappStatusDto(data.id, data.data)
  }

  to() {

    return {
      id: this.id,
      data: this.data
    }

  }

  /**
   * @return {string}
   */
  getId() {
    return this.id
  }

  /**
   * @return {WhatsappStatusItem}
   */
  getItem() {
    return new WhatsappStatusItem(this.data)
  }

}

module.exports = WhatsappStatusDto
