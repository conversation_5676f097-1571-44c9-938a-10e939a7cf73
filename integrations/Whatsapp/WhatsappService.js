const https = require('https')
const pino = require('pino')()
const { default: axios } = require('axios')

const enums = require('../../libs/enums')
const helpers = require('../../libs/helpers')

const Message = require('../../models/Message')

const UploadService = require('../../services/UploadService')
const FfmpegService = require('../../services/FfmpegService')

const WhatsappApiService = require('../../integrations/Whatsapp/WhatsappApiService')

const WhatsappService = {
  getWhatsappMediaUrl: async (mediaId) => {
    const response = await axios.request({
      url: `https://graph.facebook.com/v19.0/${mediaId}?access_token=${process.env.HELOROBO_SYSTEM_USER_TOKEN}`,
      method: 'GET',
      withCredentials: true,
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    })

    return {
      url: response.data.url,
      mime_type: response.data.mime_type,
      sha256: response.data.sha256,
      file_size: response.data.file_size,
    }
  },

  downloadWhatsappMedia: async (url) => {
    const response = await axios.request({
      url: url,
      method: 'GET',
      headers: {
        Authorization: 'Bearer ' + process.env.HELOROBO_SYSTEM_USER_TOKEN
      },
      responseType: 'arraybuffer',
      withCredentials: true,
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    })

    return response.data
  },

  getWhatsappMedia: async (mediaId, traceId) => {
    const media = await WhatsappService.getWhatsappMediaUrl(mediaId)
    const mediaData = await WhatsappService.downloadWhatsappMedia(media.url)

    const mediaName = `${media.sha256}.${helpers.getMediaFileExtention(media.mime_type)}`
    let uploadedUrl = await UploadService.uploadFileFromPresignedUrl(mediaName, mediaData, media.mime_type, 'helorobo-chat-media')

    let fileSize = media.file_size
    let fileContentType = media.mime_type

    if (fileContentType.includes('ogg')) {
      const convertedUrl = await FfmpegService.convertFile(uploadedUrl, 'video/mp4').catch(err => {
        pino.error({
          timestamp: new Date(),
          message: 'CLOUD: ogg media formatı değiştiriminde hata Oluştu',
          error: JSON.stringify(err.response?.data || { message: 'İstek Atılamadı' }),
          trace_id: traceId,
          data: JSON.stringify({
            media_id: mediaId,
            url: uploadedUrl
          })
        })

        return false
      })

      if (convertedUrl !== false) {
        const head = await WhatsappService.getMediaContents(uploadedUrl)
        fileSize = Number(head['Content-Length'])
        fileContentType = head['Content-Type']

        uploadedUrl = convertedUrl
      }
    }

    return {
      mime_type: fileContentType,
      sha256: media.sha256,
      file_size: fileSize,
      url: uploadedUrl
    }
  },

  downloadWhatsappContainerMedia: async (container, contentId) => {
    const config = {
      url: container.vBaseUrl + '/v1/media/' + contentId,
      method: 'GET',
      responseType: 'arraybuffer',
      headers: {
        Authorization: 'Bearer ' + container.token
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    }

    return axios.request(config)
  },

  getMediaContents: async (url) => {
    return axios.request({
      url: url,
      method: 'HEAD'
    }).then(response => response.headers)
  },

  getWhatsappContainerMedia: async (channel, mediaId, traceId) => {
    const container = await WhatsappApiService.getContainerByChannel(channel.vSettings.getApiKey())

    const mediaData = await WhatsappService.downloadWhatsappContainerMedia(container, mediaId)

    let uploadedUrl = await UploadService.uploadFileFromPresignedUrl(mediaId, mediaData.data, mediaData.headers['content-type'], 'helorobo-chat-media')

    let fileSize = mediaData.data.length
    let fileContentType = mediaData.headers['content-type']

    if (fileContentType.includes('ogg')) {
      const convertedUrl = await FfmpegService.convertFile(uploadedUrl, 'video/mp4').catch(err => {
        pino.error({
          timestamp: new Date(),
          message: 'CONTAINER: ogg media formatı değiştiriminde hata Oluştu',
          error: JSON.stringify(err.response?.data || { message: 'İstek Atılamadı' }),
          trace_id: traceId,
          data: JSON.stringify({
            channel_ext_id: channel.ext_id,
            media_id: mediaId,
            container_id: container.id,
            url: uploadedUrl
          })
        })

        return false
      })

      if (convertedUrl !== false) {
        const head = await WhatsappService.getMediaContents(uploadedUrl)
        fileSize = Number(head['Content-Length'])
        fileContentType = head['Content-Type']

        uploadedUrl = convertedUrl
      }
    }

    return {
      mime_type: fileContentType,
      file_size: fileSize,
      url: uploadedUrl
    }
  },

  /**
   * @param {WhatsappMessageDto} dto
   * @return Object
   */
  createContactsContentFromIncomingMessage: dto => {

    return {

      contacts: dto.getItem().getMessageContacts().map(contact => {

        return {

          name: contact.name.formatted_name,
          phones: contact.phones.map(item => item.phone)

        }

      })
    }

  },

  /**
   * @param {WhatsappMessageDto} dto
   *
   * @return {Promise<object>}
   */
  getMessageObject: async (dto, channel, traceId) => {

    let mediaData = {}
    if (dto.getItem().getMediaId()) {
      if (channel.provider === enums.channel_providers.CLOUD) {
        mediaData = await WhatsappService.getWhatsappMedia(dto.getItem().getMediaId(), traceId)
      } else {
        mediaData = await WhatsappService.getWhatsappContainerMedia(channel, dto.getItem().getMediaId(), traceId)
      }
    }

    /**
     * Mesajlar içerisinde hasContext() bilgsi true olarak gelirse bu mesaj, 
     * Cevaplama mesajı olarak adlandırılıyor. aksi olana kadar böyle kabul edildi,
     * Mesajın gerçek type bilgisi content içerisinde belirtildi
     */
    switch (dto.getItem().getMessageType()) {

      case enums.message_types.TEXT:

        if (dto.getItem().hasReferral()) {

          return {
            type: enums.message_types.TEXT,
            content: {
              mid: dto.getItem().getMessageId(),
              text: dto.getItem().getReferralSourceUrl() + '  ' + dto.getItem().getMessageText()
            }
          }

        }

        // Bu bilgi mesaj Alıntılama bilgisi içindir
        if (dto.getItem().hasContext()) {

          return {
            type: enums.message_types.WHATSAPP_REPLY_TO_MESSAGE,
            content: {
              type: enums.message_types.TEXT,
              mid: dto.getItem().getMessageId(),
              text: dto.getItem().getMessageText(),
              reply_to: dto.getItem().getMessageReplyContextId()
            }
          }

        }

        return {
          type: enums.message_types.TEXT,
          content: {
            mid: dto.getItem().getMessageId(),
            text: dto.getItem().getMessageText()
          }
        }

      case enums.message_types.WHATSAPP_IMAGE_URL:

        return {
          type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_REPLY_TO_MESSAGE : enums.message_types.WHATSAPP_IMAGE_URL,
          content: {
            type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_IMAGE_URL : undefined,
            mid: dto.getItem().getMessageId(),
            caption: dto.getItem().getMessageCaption(),
            id: dto.getItem().getMediaId(),
            reply_to: dto.getItem().hasContext() ? dto.getItem().getMessageReplyContextId() : undefined,
            ...mediaData
          }
        }

      case enums.message_types.WHATSAPP_STICKER_URL:

        return {
          type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_REPLY_TO_MESSAGE : enums.message_types.WHATSAPP_STICKER_URL,
          content: {
            type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_STICKER_URL : undefined,
            mid: dto.getItem().getMessageId(),
            caption: dto.getItem().getMessageCaption(),
            animated: dto.getItem().getStickerAnimated(),
            meta_data: dto.getItem().getStickerMetaData(),
            mime_type: dto.getItem().getStickerMimeType(),
            id: dto.getItem().getMediaId(),
            reply_to: dto.getItem().hasContext() ? dto.getItem().getMessageReplyContextId() : undefined,
            ...mediaData
          }
        }

      case enums.message_types.WHATSAPP_VIDEO_URL:

        return {
          type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_REPLY_TO_MESSAGE : enums.message_types.WHATSAPP_VIDEO_URL,
          content: {
            type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_VIDEO_URL : undefined,
            mid: dto.getItem().getMessageId(),
            caption: dto.getItem().getMessageCaption(),
            id: dto.getItem().getMediaId(),
            reply_to: dto.getItem().hasContext() ? dto.getItem().getMessageReplyContextId() : undefined,
            ...mediaData
          }
        }

      case enums.message_types.WHATSAPP_DOCUMENT_URL:

        return {
          type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_REPLY_TO_MESSAGE : enums.message_types.WHATSAPP_DOCUMENT_URL,
          content: {
            type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_DOCUMENT_URL : undefined,
            mid: dto.getItem().getMessageId(),
            filename: dto.getItem().getMessageCaption(),
            id: dto.getItem().getMediaId(),
            reply_to: dto.getItem().hasContext() ? dto.getItem().getMessageReplyContextId() : undefined,
            ...mediaData
          }
        }

      case enums.message_types.WHATSAPP_VOICE_URL:

        return {
          type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_REPLY_TO_MESSAGE : enums.message_types.WHATSAPP_VOICE_URL,
          content: {
            type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_VOICE_URL : undefined,
            mid: dto.getItem().getMessageId(),
            id: dto.getItem().getMediaId(),
            reply_to: dto.getItem().hasContext() ? dto.getItem().getMessageReplyContextId() : undefined,
            ...mediaData
          }
        }

      case enums.message_types.WHATSAPP_AUDIO_URL:

        return {
          type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_REPLY_TO_MESSAGE : enums.message_types.WHATSAPP_AUDIO_URL,
          content: {
            type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_AUDIO_URL : undefined,
            mid: dto.getItem().getMessageId(),
            id: dto.getItem().getMediaId(),
            reply_to: dto.getItem().hasContext() ? dto.getItem().getMessageReplyContextId() : undefined,
            ...mediaData
          }
        }

      case enums.message_types.WHATSAPP_CONTACTS:

        let content = {
          mid: dto.getItem().getMessageId(),
          type: enums.message_types.WHATSAPP_CONTACTS,
          ...WhatsappService.createContactsContentFromIncomingMessage(dto)
        }

        if (dto.getItem().hasContext()) {

          content.reply_to = dto.getItem().hasContext() ? dto.getItem().getMessageReplyContextId() : undefined

          return {
            type: enums.message_types.WHATSAPP_REPLY_TO_MESSAGE,
            content: content
          }

        }

        delete content.type

        return {
          type: enums.message_types.WHATSAPP_CONTACTS,
          content: content
        }

      case enums.message_types.WHATSAPP_LOCATION:

        return {
          type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_REPLY_TO_MESSAGE : enums.message_types.WHATSAPP_LOCATION,
          content: {
            type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_LOCATION : undefined,
            mid: dto.getItem().getMessageId(),
            latitude: dto.getItem().getMessageLatitude(),
            longitude: dto.getItem().getMessageLongitude(),
            reply_to: dto.getItem().hasContext() ? dto.getItem().getMessageReplyContextId() : undefined
          }
        }

      case enums.message_types.WHATSAPP_INTERACTIVE:

        return {
          type: enums.message_types.WHATSAPP_REPLY_TO_MESSAGE,
          content: {
            type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_INTERACTIVE : undefined,
            mid: dto.getItem().getMessageId(),
            reply_to: dto.getItem().hasContext() ? dto.getItem().getMessageReplyContextId() : undefined,
            interactive: dto.getItem().getMessageReplyButtonData()
          }
        }

      case enums.message_types.WHATSAPP_TEMPLATE:

        let message = null
        if (dto.getItem().hasContext()) {
          message = await Message.findOne({ ext_id: dto.getItem().getMessageReplyContextId() })
        }

        return {
          type: enums.message_types.WHATSAPP_REPLY_TO_MESSAGE,
          content: {
            type: dto.getItem().hasContext() ? enums.message_types.WHATSAPP_TEMPLATE : undefined,
            mid: dto.getItem().getMessageId(),
            reply_to: dto.getItem().hasContext() ? dto.getItem().getMessageReplyContextId() : undefined,
            button: dto.getItem().getButtonMessageReply()
          },
          data: {
            send_wizard_id: message?.data?.send_wizard_id || ''
          }
        }

      case enums.message_types.WHATSAPP_REFERRAL:
        const data = {
          type: enums.message_types.WHATSAPP_REFERRAL,
          content: {
            mid: dto.getItem().getMessageId(),
            text: dto.getItem().getMessageText(),
            referral: dto.getItem().getReferral()
          }
        }

        const media = dto.getItem().getReferralMedia()
        if (media) {
          data.content.referral[media.type] = media.data
        }

        if (dto.getItem().hasContext()) {
          data.content.context = dto.getItem().getReferralContext()
        }
        return data

      default:
        throw new Error('IncomingMessage type not found: ' + JSON.stringify(dto.getItem().getData()))
    }

  },

  GetConversationAnalytics: async (wabaIdsArray, startDate, endDate, traceId) => {
    let conversation_count = 0
    let free_conversation_count = 0
    let cost = 0

    const promiseAll = []
    for (const item of wabaIdsArray) {
      promiseAll.push(WhatsappApiService.getConversationLimit(item, startDate, endDate).catch((err) => {
        pino.error({
          timestamp: new Date(),
          trace_id: traceId,
          message: err.message,
          error: JSON.stringify(err.response?.data || { message: 'İstek Atılamadı' })
        })

        return false
      }))
    }

    const response = await Promise.allSettled(promiseAll)

    const analyticItems = []

    for (const conversationData of response) {
      if (conversationData.value === false) {
        continue
      }

      if (!conversationData.value.conversation_analytics) {
        continue
      }

      for (const item of conversationData.value.conversation_analytics.data[0].data_points) {
        if (item.conversation_type === enums.whatsapp_business_conversation_types.FREE_TIER) {
          free_conversation_count += item.conversation
        } else {
          conversation_count += item.conversation
        }

        analyticItems.push(item)

        cost += item.cost
      }
    }

    return {
      conversation_count,
      cost,
      free_conversation_count,
      analytic_items: analyticItems
    }
  },

  GetMessageAnalytics: async (wabaIdsArray, startDate, endDate, traceId) => {
    let message_count = 0
    let free_message_count = 0
    let cost = 0

    const promiseAll = []
    for (const item of wabaIdsArray) {
      promiseAll.push(WhatsappApiService.getMessageLimit(item, startDate, endDate).catch((err) => {
        pino.error({
          timestamp: new Date(),
          trace_id: traceId,
          message: err.message,
          error: JSON.stringify(err.response?.data || { message: 'İstek Atılamadı' })
        })

        return false
      }))
    }

    const response = await Promise.allSettled(promiseAll)

    const analyticItems = []

    for (const messageData of response) {
      if (messageData.value === false) {
        continue
      }

      if (!messageData.value) {
        continue
      }

      if (!messageData.value.pricing_analytics) {
        continue
      }

      for (const item of messageData.value.pricing_analytics.data[0].data_points) {
        if (item.pricing_type === enums.whatsapp_business_message_types.FREE_CUSTOMER_SERVICE || item.pricing_type === enums.whatsapp_business_message_types.FREE_ENTRY_POINT) {
          free_message_count += item.volume
        } else {
          message_count += item.volume
        }

        analyticItems.push(item)

        cost += item.cost
      }
    }

    return {
      message_count,
      cost,
      free_message_count,
      analytic_items: analyticItems
    }
  }

}

module.exports = WhatsappService
