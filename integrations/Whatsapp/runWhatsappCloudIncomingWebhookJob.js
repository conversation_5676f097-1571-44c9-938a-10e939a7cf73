const moment = require('moment')
const pino = require('pino')()

const MessageSeenJob = require('../../jobs/MessageSeenJob')
const MessageSentJob = require('../../jobs/MessageSentJob')
const MessageReactedJob = require('../../jobs/MessageReactedJob')
const MessageFailedJob = require('../../jobs/MessageFailedJob')
const MessageDeletedJob = require('../../jobs/MessageDeletedJob')
const IncomingMessageJob = require('../../jobs/IncomingMessageJob')
const MessageDeliveredJob = require('../../jobs/MessageDeliveredJob')

const WhatsappCloudWebhookBody = require('./CloudSDK/WhatsappCloudWebhookBody')
const WhatsappCloudStatusDto = require('./CloudSDK/WhatsappCloudStatusDto')
const WhatsappCloudMessageDto = require('./CloudSDK/WhatsappCloudMessageDto')

const IncomingWebhookJobResultDto = require('../../dtos/IncomingWebhookJobResultDto')

const Session = require('../../models/Session')
const Channel = require('../../models/Channel')

const getOrCreateSession = async (sessionId, channelNumber, chatNumber, expirationTime) => {
  const sessionDoc = await Session.findOne({
    session_id: sessionId,
    chat_ext_id: chatNumber
  })

  if (sessionDoc) {
    return
  }

  return Session.create({
    channel_number: channelNumber,
    chat_ext_id: chatNumber,
    session_id: sessionId,
    expiration_time: moment.unix(expirationTime).toDate()
  })
}

module.exports = async (req, body) => {

  const incomingWebhookJobResultDto = new IncomingWebhookJobResultDto()

  const whatsappWebhookBody = new WhatsappCloudWebhookBody(body)

  pino.info({
    message: 'WhatsappIncoming Webhook',
    trace_id: req.trace_id,
    data: JSON.stringify(whatsappWebhookBody.getData()),
  })

  for (const webhookitem of whatsappWebhookBody.getEntries()) {
    for (const message of webhookitem.getMessageChanges()) {

      if (message.hasDifferentData()) {
        pino.info({
          message: 'WhatsappIncoming Different Webhook',
          trace_id: req.trace_id,
          data: JSON.stringify(message.getData()),
        })
        continue
      }

      // Zeliha ve Sıla Container ise direk development ortamına gönderelim.
      if (['905367132150', '905388755330', '908502425121', '14318311104'].includes(message.getChannelExtId()) && !incomingWebhookJobResultDto.getSenderId()) {
        incomingWebhookJobResultDto.setHasDevelopmentContent(true)
        incomingWebhookJobResultDto.setSenderId(message.getChatExtId())
      }

      const channel = await Channel.findOne({
        ext_id: message.getChannelExtId(),
        is_active: true,
        deleted_at: {
          $exists: false
        }
      }).populate('company_id')
      if (!channel) {
        throw new Error('Kanal Bulunamadı')
      }

      if (channel.company?.is_active === false) {
        throw new Error('Şirket Pasif Olduğu için Mesaj Alınmadı')
      }

      if (channel.company_id.vData.getUsingAsBridge() && !incomingWebhookJobResultDto.getUsingAsBridge()) {
        incomingWebhookJobResultDto.setUsingAsBridge(channel.company_id.vData.getUsingAsBridge())
        incomingWebhookJobResultDto.setWebhookUrl(channel.company_id.vData.getDataWebhookUrl())
        incomingWebhookJobResultDto.setWebhookUrlHash(channel.company_id.vData.getDataWebhookUrlHash())
      }

      // mesaj işlemleri yapılacak
      if (message.hasMessages()) {
        for (const messageItem of message.getMessages()) {

          if (messageItem.isMessageReacted()) {
            await req.app.services.JobService.addMessageReactedJob(
              req,
              MessageReactedJob.TYPE_WHATSAPP_CLOUD,
              WhatsappCloudMessageDto.createFromWhatsapp(message.getChannelExtId(), { ...messageItem.getData(), contact: message.getContacts() }).to()
            )

            continue
          }

          await req.app.services.JobService.addIncomingMessageJob(
            req,
            IncomingMessageJob.TYPE_WHATSAPP_CLOUD,
            WhatsappCloudMessageDto.createFromWhatsapp(message.getChannelExtId(), { ...messageItem.getData(), contact: message.getContacts() }).to()
          )
        }

        continue
        // status işlemleri yapılacak
      } else if (message.hasStatuses()) {
        for (const status of message.getStatuses()) {
          const whatsappStatusData = WhatsappCloudStatusDto.createFromWhatsapp(message.getChannelExtId(), status.getData()).to()

          if (status.isDeleted()) {
            await req.app.services.JobService.addMessageDeletedJob(
              req,
              MessageDeletedJob.TYPE_WHATSAPP_CLOUD,
              whatsappStatusData,
            )
          }
          if (status.isFailed()) {
            await req.app.services.JobService.addMessageFailedJob(
              req,
              MessageFailedJob.TYPE_WHATSAPP_CLOUD,
              whatsappStatusData
            )
          }
          if (status.isSent()) {
            await req.app.services.JobService.addMessageSentJob(
              req,
              MessageSentJob.TYPE_WHATSAPP_CLOUD,
              whatsappStatusData
            )
          }
          if (status.isDelivered()) {
            await req.app.services.JobService.addMessageDeliveredJob(
              req,
              MessageDeliveredJob.TYPE_WHATSAPP_CLOUD,
              whatsappStatusData
            )
          }
          if (status.isSeen()) {
            await req.app.services.JobService.addMessageSeenJob(
              req,
              MessageSeenJob.TYPE_WHATSAPP_CLOUD,
              whatsappStatusData
            )
          }

          // Session kontrol ediliyor
          if (status.hasConversationData() && status.getConversationExpirationTime()) {
            getOrCreateSession(status.getConversationId(), message.getChannelExtId(), message.getChatExtId(), status.getConversationExpirationTime())
          }
        }
      }

    }
  }

  return incomingWebhookJobResultDto
}
