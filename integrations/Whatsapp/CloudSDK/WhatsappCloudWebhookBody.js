const WhatsappCloudEntryItem = require('./WhatsappCloudEntryItem')

class WhatsappCloudWebhookBody {

  constructor(data) {
    this.data = data
  }

  getData() {
    return this.data
  }

  getEntries() {
    return this.data.entry.map(a => new WhatsappCloudEntryItem(a))
  }

  getObject() {
    return this.data.object
  }

}

module.exports = WhatsappCloudWebhookBody

/*
{
  "object": "whatsapp_business_account",
  "entry": [
    {
      "id": "***************",
      "changes": [
        {
          "value": {
            "messaging_product": "whatsapp",
            "metadata": {
              "display_phone_number": "***********",
              "phone_number_id": "***************"
            },
            "contacts": [
              {
                "profile": {
                  "name": "tsoft avni arikan"
                },
                "wa_id": "***********"
              }
            ],
            "messages": [
              {
                "from": "***********",
                "id": "wamid.HBgLMTQwODM5NjE5MjUVAgASGBYzRUIwQjQzMzY4NEJDODVGNkUyMURCAA==",
                "timestamp": "**********",
                "text": {
                  "body": "Bu bir test mesajıdır."
                },
                "type": "text"
              }
            ]
          },
          "field": "messages"
        }
      ]
    }
  ]
}
*/