class WhatsappCloudStatusItem {

  constructor(data) {
    this.data = data
  }

  getData() {
    return this.data
  }

  getMessageId() {
    return this.data.id
  }

  getCustomerId() {
    return this.data.recipient_id
  }

  isDeleted() {
    return this.data.status === 'deleted'
  }

  isFailed() {
    return this.data.status === 'failed'
  }

  isSent() {
    return this.data.status === 'sent'
  }

  isDelivered() {
    return this.data.status === 'delivered'
  }

  isSeen() {
    return this.data.status === 'read'
  }

  getErrorMessage(req) {

    if ('errors' in this.data) {

      if (Array.isArray(this.data.errors)) {

        if (this.data.errors.length > 0) {

          const errorItem = this.data.errors[0]

          let message = ''
          if ('code' in errorItem) {
            message = 'code: ' + errorItem.code.toString()
          }
          if ('title' in errorItem) {
            message += ' -- title: ' + errorItem.title
          }
          if (errorItem.message !== errorItem.title) {
            message += ' -- message: ' + errorItem.message
          }
          if ('error_data' in errorItem) {
            message += ' -- details: ' + errorItem.error_data.details
          }

          return message
        }

      }

    }

    return req.t('Global.errors.whatsapp_message_error')

  }

  hasConversationData() {
    return 'conversation' in this.data
  }

  getConversationId() {
    return this.data.conversation.id
  }

  getConversationExpirationTime() {
    return this.data.conversation.expiration_timestamp
  }

}

module.exports = WhatsappCloudStatusItem

/*
https://developers.facebook.com/docs/whatsapp/cloud-api/webhooks/payload-examples#message-status-updates
{
  "object": "whatsapp_business_account",
  "entry": [
    {
      "id": "<WHATSAPP_BUSINESS_ACCOUNT_ID>",
      "changes": [
        {
          "value": {
            "messaging_product": "whatsapp",
            "metadata": {
              "display_phone_number": "<BUSINESS_DISPLAY_PHONE_NUMBER>",
              "phone_number_id": "<BUSINESS_PHONE_NUMBER_ID>"
            },
            "statuses": [
              {
                "id": "<WHATSAPP_MESSAGE_ID>",
                "status": "sent",
                "timestamp": "<WEBHOOK_SENT_TIMESTAMP>",
                "recipient_id": "<WHATSAPP_USER_ID>",
                "conversation": {
                  "id": "<CONVERSATION_ID>",
                  "expiration_timestamp": "<CONVERSATION_EXPIRATION_TIMESTAMP>",
                  "origin": {
                    "type": "<CONVERSATION_CATEGORY>"
                  }
                },
                "pricing": {
                  "billable": <IS_BILLABLE?>,
                  "pricing_model": "CBP",
                  "category": "<CONVERSATION_CATEGORY>"
                }
              }
            ]
          },
          "field": "messages"
        }
      ]
    }
  ]
}
*/