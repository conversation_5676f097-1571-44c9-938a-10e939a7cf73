const WhatsappCloudMessageItem = require('./WhatsappCloudMessageItem')
const WhatsappCloudStatusItem = require('./WhatsappCloudStatusItem')

class WhatsappCloudMessageChanges {
  constructor(data) {
    this.data = data
  }

  getData() {
    return this.data
  }

  hasMessages() {
    return 'messages' in this.data.value
  }

  getChannelExtId() {
    return this.data.value.metadata.display_phone_number
  }

  getChatExtId() {
    if (this.hasStatuses()) {
      return this.data.value.statuses[0].recipient_id
    }

    return this.data.value.contacts[0].wa_id
  }

  getContacts() {
    return this.data.value.contacts[0]
  }

  getMessages() {
    return this.data.value.messages.map(a => new WhatsappCloudMessageItem(a))
  }

  hasStatuses() {
    return 'statuses' in this.data.value
  }

  getStatuses() {
    return this.data.value.statuses.map(item => new WhatsappCloudStatusItem(item))
  }

  hasDifferentData() {
    return this.data.field !== 'messages'
  }
}

module.exports = WhatsappCloudMessageChanges