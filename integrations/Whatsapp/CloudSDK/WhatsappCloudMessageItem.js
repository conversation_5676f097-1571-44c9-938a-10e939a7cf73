const enums = require('../../../libs/enums')
const helpers = require('../../../libs/helpers')

class WhatsappCloudMessageItem {

  constructor(data) {
    this.data = data
  }

  getData() {
    return this.data
  }

  getMessageId() {
    return this.data.id
  }

  getCustomerId() {
    return this.data.from
  }

  getProfileName() {
    return this.data.contact.profile.name
  }

  getMessageType() {

    switch (this.data.type) {
      case 'audio':
        return enums.message_types.WHATSAPP_AUDIO_URL
      case 'document':
        return enums.message_types.WHATSAPP_DOCUMENT_URL
      case 'image':
        return enums.message_types.WHATSAPP_IMAGE_URL
      case 'location':
        return enums.message_types.WHATSAPP_LOCATION
      case 'text':
        if (this.data.referral) {
          return enums.message_types.WHATSAPP_REFERRAL
        }
        return enums.message_types.TEXT
      case 'video':
        return enums.message_types.WHATSAPP_VIDEO_URL
      case 'voice':
        return enums.message_types.WHATSAPP_VOICE_URL
      case 'contacts':
        return enums.message_types.WHATSAPP_CONTACTS
      case 'interactive':
        return enums.message_types.WHATSAPP_INTERACTIVE
      case 'button':
        return enums.message_types.WHATSAPP_TEMPLATE
      case 'sticker':
        return enums.message_types.WHATSAPP_STICKER_URL
      case 'reaction':
        return enums.message_types.WHATSAPP_REACTION
    }
    return 'UNSUPPORTED'
  }

  getMessageText() {
    return this.data.text.body
  }

  /**
   * @return {boolean}
   */
  hasReferral() {
    return 'referral' in this.data
  }

  /**
   * @return {boolean}
   */
  hasContext() {
    return 'context' in this.data
  }

  getReferralSourceUrl() {
    return this.data.referral.source_url
  }

  getReferralSourceId() {
    return this.data.referral.source_id
  }

  getReferralSourceType() {
    return this.data.referral.source_type
  }

  getReferral() {
    return this.data.referral
  }

  getReferralBody() {
    return this.data.referral.body
  }

  getReferralHeadline() {
    return this.data.referral.headline
  }

  getReferralClId() {
    return this.data.referral.ctwa_clid
  }

  getReferralMediaType() {
    return this.data.referral.media_type
  }

  getReferralVideoUrl() {
    return this.data.referral.video_url
  }

  getReferralThumbnailUrl() {
    return this.data.referral.thumbnail_url
  }

  getReferralContext() {
    return this.data.context // example: { forwarded: true }
  }

  getReferralMedia() {
    return helpers.getReferralMedia(this.data.referral)
  }

  getMessageCaption() {

    switch (this.getMessageType()) {
      case enums.message_types.WHATSAPP_IMAGE_URL:
        return this.data.image.caption || ''
      case enums.message_types.WHATSAPP_DOCUMENT_URL:
        return this.data.document.caption || ''
      case enums.message_types.WHATSAPP_VIDEO_URL:
        return this.data.video.caption || ''
    }

    return ''
  }


  getMediaId() {

    switch (this.getMessageType()) {
      case enums.message_types.WHATSAPP_IMAGE_URL:
        return this.data.image.id
      case enums.message_types.WHATSAPP_DOCUMENT_URL:
        return this.data.document.id
      case enums.message_types.WHATSAPP_AUDIO_URL:
        return this.data.audio.id
      case enums.message_types.WHATSAPP_VIDEO_URL:
        return this.data.video.id
      case enums.message_types.WHATSAPP_VOICE_URL:
        return this.data.voice.id
      case enums.message_types.WHATSAPP_STICKER_URL:
        return this.data.sticker.id
    }

    return ''
  }

  getMessageContacts() {
    return this.data.contacts || []
  }

  getMessageLatitude() {
    return this.data.location.latitude
  }

  getMessageLongitude() {
    return this.data.location.longitude
  }

  getMessageReplyContextId() {
    return this.data.context.id || ''
  }

  getMessageReplyButtonData() {
    return this.data.interactive || {}
  }

  getButtonMessageReply() {
    return this.data.button || {}
  }

  getTimestamp() {
    return this.data.timestamp
  }

  hasError() {
    return Array.isArray(this.data.errors)
  }

  getStickerAnimated() {
    return this.data.sticker.animated
  }

  getStickerMetaData() {
    return this.data.sticker.meta_data
  }

  getStickerMimeType() {
    return this.data.sticker.mime_type
  }

  isMessageReacted() {
    return this.data.type === 'reaction'
  }

  getReactionReaction() {
    return undefined
  }

  getReactionEmoji() {
    return this.data.reaction.emoji
  }

  getReactionAction() {
    return undefined
  }

  getReactionMid() {
    return this.data.reaction.message_id
  }
}

module.exports = WhatsappCloudMessageItem
