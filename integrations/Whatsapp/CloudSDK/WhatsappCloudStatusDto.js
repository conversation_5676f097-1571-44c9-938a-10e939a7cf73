const WhatsappCloudStatusItem = require('./WhatsappCloudStatusItem')

class WhatsappCloudStatusDto {

  /**
   * @param id
   * @param data
   *
   * @private
   */
  constructor(id, data) {

    // whatsapp number
    this.id = id

    // WhatsappCloudStatusItem için kullanılmak üzere data
    this.data = data


  }

  /**
   * @param {string} id
   * @param {object} data
   *
   * @return {WhatsappCloudStatusDto}
   */
  static createFromWhatsapp(id, data) {
    return new WhatsappCloudStatusDto(id, data)
  }

  static createFromData(data) {
    return new WhatsappCloudStatusDto(data.id, data.data)
  }

  to() {

    return {
      id: this.id,
      data: this.data
    }

  }

  /**
   * @return {string}
   */
  getId() {
    return this.id
  }

  /**
   * @return {WhatsappCloudStatusItem}
   */
  getItem() {
    return new WhatsappCloudStatusItem(this.data)
  }

}

module.exports = WhatsappCloudStatusDto
