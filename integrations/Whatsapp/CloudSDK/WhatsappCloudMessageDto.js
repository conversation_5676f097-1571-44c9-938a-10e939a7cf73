const WhatsappCloudMessageItem = require('../CloudSDK/WhatsappCloudMessageItem')

class WhatsappCloudMessageDto {

  /**
   * @param id
   * @param data
   *
   * @private
   */
  constructor(id, data) {

    // whatsapp number
    this.id = id

    // WhatsappCloudMessageItem için kullanılmak üzere data
    this.data = data

  }

  /**
   * @param {string} id
   * @param {object} data
   *
   * @return {WhatsappCloudMessageDto}
   */
  static createFromWhatsapp(id, data) {
    return new WhatsappCloudMessageDto(id, data)
  }

  static createFromData(data) {
    return new WhatsappCloudMessageDto(data.id, data.data)
  }

  to() {

    return {
      id: this.id,
      data: this.data
    }

  }

  /**
   * @return {string}
   */
  getId() {
    return this.id
  }

  /**
 * @return {string}
 */
  getCustomerId() {
    return this.getItem().getCustomerId(this.id)
  }

  /**
   * @return {WhatsappCloudMessageItem}
   */
  getItem() {
    return new WhatsappCloudMessageItem(this.data)
  }

}

module.exports = WhatsappCloudMessageDto
