const utils = require('./../../libs/utils')
const enums = require('./../../libs/enums')
const helpers = require('./../../libs/helpers')

const HeloscopeIntegrationService = require('./../../integrations/Heloscope/HeloscopeIntegrationService')

const HeloscopePresentation = {

  /**
   * @param req
   * @param cart
   * @param pricePrecision
   * @param isRequiresApprove
   * @returns {string}
   */
  getCartContents(req, cart, pricePrecision, currencyCode, isRequiresApprove = true) {

    const dataCart = cart.items.map(cartItem => {

      let variantMessage = ''

      if (cartItem.variant_name) {
        variantMessage = ` (${cartItem.variant_name.trim()})`
      }

      return req.t('App.integration.send_cart_message.item', {
        emoji: "➡️",
        variant_message: variantMessage,
        title: cartItem.title,
        count: cartItem.count,
        price: utils.getCurrencyForIntlNumberFormat(
          cartItem.sell_price,
          currencyCode,
          pricePrecision || 2
        ),
        amount: utils.getCurrencyForIntlNumberFormat(cartItem.amount, currencyCode, pricePrecision || 2),
        interpolation: { escapeValue: false },
      })

    })

    const subTotalContent = req.t('App.integration.send_cart_message.sub_total', {
      total: utils.getCurrencyForIntlNumberFormat(cart.sub_total, currencyCode, pricePrecision || 2),
      interpolation: { escapeValue: false }
    })

    let contents = []

    // sepetteki her bir ürüne ait bilgiyi gösterelim, aralarına birer boşluk ekliyoruz
    contents.push(dataCart.join('[BR][/BR][BR][/BR]'))

    // sepetteki ürünlerin altına uzun bir çizgi çekiyoruz
    contents.push('[BR][/BR][BR][/BR][UNDERLINE]32[/UNDERLINE][BR][/BR]')

    // genel toplam gösterilecekse gösterilen bilgilerde farklılıklar oluşacak
    if (cart.total_amount) {

      // sepete ait alt toplamı gösteriyoruz
      contents.push(subTotalContent)
      contents.push('[BR][/BR]')

      // genel toplam'ı göstereceğiz
      contents.push(req.t('App.integration.send_cart_message.general_total', {
        total: utils.getCurrencyForIntlNumberFormat(cart.total_amount, currencyCode, pricePrecision || 2),
        interpolation: { escapeValue: false }
      }))
      contents.push('[BR][/BR]')
      contents.push('[BR][/BR]')

    } else {

      // sepet alt toplamı göstereceğiz
      contents.push(subTotalContent)
      contents.push('[BR][/BR]')
      contents.push('[BR][/BR]')

    }

    if (isRequiresApprove) {

      contents.push(req.t('App.integration.send_cart_message.approve_message'))

    }

    return contents.join('')

  },

  getOrderSummaryMessage: async (req, integration, chatIntegration, user, extId) => {

    const cart = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_CART, enums.HELOSCOPE_ACTION_TYPES.USER, {}, req.language, integration, chatIntegration, extId, req.trace_id)

    const cargoOptions = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_CARGO_OPTIONS, enums.HELOSCOPE_ACTION_TYPES.USER, {}, req.language, integration, chatIntegration, extId, req.trace_id)

    const selectedCargoOption = cargoOptions.items.find(item => item.id == chatIntegration.vData.getCargoOptionId())

    if (!selectedCargoOption) {
      throw new createError.BadRequest('Cargo Option Not Found')
    }

    const paymentOptions = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_PAYMENT_OPTIONS, enums.HELOSCOPE_ACTION_TYPES.USER, {}, req.language, integration, chatIntegration, extId, req.trace_id)

    let payment_options = []

    paymentOptions.items.forEach(item => {

      if (item.options.length > 0) {

        item.options.forEach(child => {

          payment_options.push({
            id: item.id + ' / ' + child.id,
            name: item.name + ' / ' + child.name
          })

        })

      } else {
        payment_options.push({
          id: item.id,
          name: item.name
        })
      }

    })

    const selectedPaymentOption = payment_options.find(item => item.id === chatIntegration.vData.getHeloscopePaymentOptionId())


    let deliveryAddress = {}, invoiceAddress = {}

    if (extId) {

      const addresses = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_ADDRESSES, enums.HELOSCOPE_ACTION_TYPES.USER, {}, req.language, integration, chatIntegration, extId, req.trace_id)

      deliveryAddress = addresses.items.find(item => item.id == chatIntegration.vData.getDeliveryAddressId())

      invoiceAddress = addresses.items.find(item => item.id == chatIntegration.vData.getInvoiceAddressId())

    } else {

      deliveryAddress = await chatIntegration.vData.getHeloscopeGuestAddressById(chatIntegration.vData.getDeliveryAddressId())

      invoiceAddress = await chatIntegration.vData.getHeloscopeGuestAddressById(chatIntegration.vData.getInvoiceAddressId())

    }

    const order_note = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_ORDER_NOTE,
      enums.HELOSCOPE_ACTION_TYPES.USER,
      {}, req.language, integration, chatIntegration,
      extId, req.trace_id)


    const totalAmount = utils.getParseFloat(selectedCargoOption.price) + utils.getParseFloat(cart.total_amount)

    return {
      username: helpers.getName(user),
      cart_content: HeloscopePresentation.getCartContents(req, cart, integration.vData.getPricePrecision(), cart.currency_code, false),
      total_amount: utils.getCurrencyForIntlNumberFormat(totalAmount, selectedCargoOption.currency, integration.vData.getPricePrecision()),
      cargo_option_name: selectedCargoOption.name,
      cargo_option_fee: utils.getCurrencyForIntlNumberFormat(selectedCargoOption.price, selectedCargoOption.currency, integration.vData.getPricePrecision()),
      payment_type: selectedPaymentOption.name,
      delivery_address: deliveryAddress.address,
      delivery_district: deliveryAddress.town_name,
      delivery_city: deliveryAddress.city_name,
      invoice_address: invoiceAddress.address,
      invoice_district: invoiceAddress.town_name,
      invoice_city: invoiceAddress.city_name,
      general_order_note: req.t('App.integration.general_order_note', {
        general_order_note: order_note,
        interpolation: { escapeValue: false }
      })

    }

  }

}

module.exports = HeloscopePresentation
