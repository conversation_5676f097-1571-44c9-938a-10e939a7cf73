const jwt = require('jsonwebtoken')
const axios = require('axios')
const moment = require('moment')
const dayjs = require('dayjs')
const createError = require('http-errors')

const enums = require('../../libs/enums')
const helpers = require('../../libs/helpers')

const getHeloscopeRequestConfig = async (language, action, data, type, integration, chatIntegration, extId, traceId) => {

  const config = {
    url: process.env.INTEGRATION_BASE_URL + `/process`,
    method: 'POST',
    headers: {
      'X-Lang-Code': language
    },
    data: {
      type: enums.INTEGRATION_TYPES.HELOSCOPE,
      action: action,
      data: data,
      extra: {
        token: '',
        base_url: integration.data.base_url,
        type: type,
        version: integration.data.version,
        trace_id: traceId
      },
    }
  }

  //Heloscope için hem user hemde admin endpointlerine istek atılmaktadır.
  //Admin istekleri için integration tarafındaki token kullanılırken User istekleri için ChatIntegration daki token kullanılır.

  const adminToken = await isValidTokenAdmin(integration)

  switch (type) {

    case enums.HELOSCOPE_ACTION_TYPES.ADMIN:
      config.data.extra.token = adminToken
      break

    case enums.HELOSCOPE_ACTION_TYPES.USER:
      if (extId) {
        config.data.extra.token = await isValidTokenUser(integration, chatIntegration)
      } else {
        config.data.extra.client_id = chatIntegration.vData.getClientId()
      }
      break

    default:
      throw new createError.NotFound('heloscope action type not found')

  }


  return config

}

const isValidTokenAdmin = async integration => {

  let valid

  if (!integration.token) {
    valid = false
  }

  if (!integration.token?.expires_at) {
    valid = false
  } else {
    const d = new Date()
    const date1 = moment(integration.token.expires_at, 'DD-MM-YYYY:HH-mm-ss')
    const date2 = moment(d, 'DD-MM-YYYY:HH-mm-ss')
    valid = date1.isAfter(date2)
  }

  if (valid) {
    return integration.token.token
  }

  return HeloscopeIntegrationService.adminLogin(integration, enums.HELOSCOPE_ACTION_TYPES.ADMIN)

}


const isValidTokenUser = async (integration, chatIntegration) => {

  let valid = true

  if (!chatIntegration.token) {
    valid = false
  }

  if (!chatIntegration.token?.expires_at) {
    valid = false
  } else {
    const date1 = moment(chatIntegration.token.expires_at, 'DD-MM-YYYY:HH-mm-ss')
    valid = date1.isAfter(moment())
  }

  if (valid) {
    return chatIntegration.token.token
  }

  return HeloscopeIntegrationService.userLogin(integration, chatIntegration, enums.HELOSCOPE_ACTION_TYPES.USER)

}


const HeloscopeIntegrationService = {

  process: async (action, type, data = {}, language, integration, chatIntegration, extId, traceId) => {

    try {

      data = JSON.parse(JSON.stringify(typeof data === 'object' ? data : {}))

      const config = await getHeloscopeRequestConfig(language, action, data, type, integration, chatIntegration, extId, traceId)

      let response = await axios.request(config)

      return response.data

    } catch (err) {

      return helpers.handleAxiosError(err)

    }

  },

  adminLogin: async (integration, type) => {

    const secretKey = await HeloscopeIntegrationService.getSecretKey(integration)

    const response = await HeloscopeIntegrationService.loginRaw(integration.data.base_url, secretKey, integration.data.version, type)

    integration.set('token', {
      token: response.token,
      expires_at: response.expires_at
    })

    await integration.save()

    return integration.token.token

  },

  userLogin: async (integration, chatIntegration, type) => {

    const secretKey = await HeloscopeIntegrationService.getSecretKey(integration)

    const response = await HeloscopeIntegrationService.loginRaw(integration.data.base_url, secretKey, integration.data.version, type, chatIntegration.data.phone_number, chatIntegration.data.client_id)

    chatIntegration.set('token', {
      token: response.token,
      expires_at: response.expires_at
    })

    await chatIntegration.save()

    return chatIntegration.token.token

  },

  loginRaw: (baseUrl, secretKey, version, type, phone_number, client_id) => {

    let config = {
      url: process.env.INTEGRATION_BASE_URL + `/process`,
      method: 'POST',
      data: {
        type: enums.INTEGRATION_TYPES.HELOSCOPE,
        action: 'LOGIN',
        data: {
          phone_number: phone_number,
          jwt: secretKey,
        },
        extra: {
          base_url: baseUrl,
          type: type,
          version: version,
          client_id: client_id
        }
      }
    }

    return axios.request(config).then(response => {

      if (!response.data.status) {
        throw new createError.BadRequest('İlgili siteye giriş yapılamadı: ' + baseUrl)
      }

      return {
        token: response.data.data[0].token.bearer,
        expires_at: moment.unix(response.data.data[0].token.exp_at).format('DD-MM-YYYY HH:mm:ss')
      }
    }).catch(err => {
      console.log(err)
      console.log(err.message)
      throw 'Heloscope ile iletişime Geçilemedi'
    })

  },

  getSecretKey: async (integration) => {

    const decodedKey = jwt.decode(integration.data.secret_key)

    if (dayjs(decodedKey.date).isBefore(dayjs(), 'month')) {
      integration.data.secret_key = jwt.sign({
        date: dayjs().format(),
        sub_domain: decodedKey.sub_domain
      }, `${decodedKey.sub_domain}.heloscope.netjettycart-api-${dayjs().format('MM')}`)

      integration.markModified('data')

      await integration.save()

    }

    return integration.data.secret_key

  }


}

module.exports = HeloscopeIntegrationService
