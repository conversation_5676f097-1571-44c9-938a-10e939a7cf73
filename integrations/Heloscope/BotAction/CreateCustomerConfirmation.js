const slugify = require('slugify')

const enums = require('../../../libs/enums')

const QueueService = require('../../../services/QueueService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const CreateCustomerConfirmationForWhatapp = require('./Whatsapp/CreateCustomerConfirmation')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return CreateCustomerConfirmationForWhatapp(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if ( ! botData) {
    return
  }

  if ( ! botData.agent_message.content.bot_data) {
    return false
  }

  const messageText = slugify(botData.customer_message.content.text.trim(), {
    replacement: '_',
    lower: true
  }).toString()

  if ( ! (messageText === "onayliyorum" || messageText === "onay" || messageText === "approve")) {
    return false
  }

  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CREATE_CUSTOMER_CONFIRMED,
    socket_rooms: [botData.agent.vSocketCode],
    data: {
      integration_id: integration.id,
      chat_id: chat.id
    }
  }, req.language)

  chatIntegration.data.create_customer_confirmed = true

  chatIntegration.markModified('data')

  return chatIntegration.save()

}
