const createError = require('http-errors')

const utils = require('../../../libs/utils')
const enums = require('../../../libs/enums')

const SendCargoOptions = require('../../../dtos/BotAction/SendCargoOptions')

const HeloscopeIntegrationService = require('../../../integrations/Heloscope/HeloscopeIntegrationService')

module.exports = async (req, chat, integration, chatIntegration, extId) => {

  const integrationData = integration.vData
  const sendCargoOptions = new SendCargoOptions()

  const getCargoOptionsResponse = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_CARGO_OPTIONS, enums.HELOSCOPE_ACTION_TYPES.USER, {}, req.language, integration, chatIntegration, extId)

  if (getCargoOptionsResponse.items.length === 0) {
    throw new createError.NotFound(req.t('App.errors.integration.cargos_are_empty'))
  }

  let cargos = []
  let cargoNames = []
  let cargoNamesForWhatsapp = []

  getCargoOptionsResponse.items.forEach((cargoOption, index) => {

    cargos.push({
      cargo_option_id: cargoOption.id,
      cargo_index: index + 1
    })

    cargoNames.push(utils.getMessageEmoji(index + 1, chat.channel.type) + ' ' + cargoOption.name + ' (' + utils.getCurrencyForIntlNumberFormat(
      cargoOption.price,
      cargoOption.currency,
      integrationData.getPricePrecision()
    ) + ')')

    cargoNamesForWhatsapp.push({
      id: cargoOption.id.toString(),
      title: cargoOption.name.substring(0, 23),
      description: utils.getCurrencyForIntlNumberFormat(cargoOption.price, cargoOption.currency, integrationData.getPricePrecision())
    })

  })

  sendCargoOptions.setBotData(cargos)

  return {
    cargo_names: cargoNames.join('[BR][/BR]'),
    cargo_dto: sendCargoOptions.getBotData(),
    cargo_names_whatsapp: cargoNamesForWhatsapp
  }

}
