const createError = require('http-errors')

const enums = require('../../../libs/enums')
const utils = require('../../../libs/utils')

const ChatService = require('../../../services/ChatService')

const HeloscopePresentation = require('../../Heloscope/HeloscopePresentation')

module.exports = async (req, chat, integration, chatIntegration, agentId) => {

  // Mesajın gönderilebilirliğini kontrol ediyor.
  const sendable = await ChatService.checkSendable(chat.id, chat.channel.type)

  if (!sendable.sendable) {
    throw new createError.BadRequest(req.t('App.errors.dash.conversation_active_time_expired'))
  }

  // Sepet özeti alınıyor
  const getMessageValues = await HeloscopePresentation.getOrderSummaryMessage(req, integration, chatIntegration, chat.title, chatIntegration.ext_id)

  getMessageValues.interpolation = { escapeValue: false }

  let messageType
  let messageContent

  switch (chat.channel.type) {

    case enums.channel_types.WHATSAPP_NUMBER:

      messageType = enums.message_types.WHATSAPP_INTERACTIVE
      messageContent = {
        caption: req.t('App.integration.whatsapp_order_summary_message', getMessageValues),
        sub_type: enums.message_types.BUTTON,
        header: {
          type: 'image',
          image: {
            link: integration.vData.getBasketSummaryMessageImageUrl() || enums.basket_summary_message_image_url
          }
        },
        buttons: [{
          type: 'reply',
          reply: {
            id: utils.generateHash(15),
            title: req.t('Global.chat_message.confirm')
          }
        }]
      }

      break

    default:

      messageType = enums.message_types.TEXT
      messageContent = {
        text: req.t('App.integration.order_summary_message', getMessageValues),
        bb_code: true
      }

      break

  }

  // lazım olan ek bilgileri dahil edelim
  messageContent.next_action = enums.TSOFT_BOT_MESSAGE_ACTIONS.CREATE_ORDER
  messageContent.agent_id = agentId
  messageContent.language = req.language
  messageContent.bb_code = true

  return [{
    message_data: messageContent,
    message_type: messageType
  }]

}
