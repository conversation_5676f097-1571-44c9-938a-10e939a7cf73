const enums = require('./../../../libs/enums')
const utils = require('./../../../libs/utils')

const AddToCartDto = require('../../../dtos/AddToCartDto')
const BotActionAddToCartDto = require('../../../dtos/BotAction/AddToCartDto')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')

const HeloscopeService = require('../../../integrations/Heloscope/HeloscopeService')
const HeloscopeIntegrationService = require('../../../integrations/Heloscope/HeloscopeIntegrationService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const MultiProductsShare = require('../../../modules/AgentApp/MultiProductsShare')

const AddToCartForWhatsapp = require('./Whatsapp/AddToCart')

const isAvailable = (value, getProductResponseItem) => {

  let result = false

  getProductResponseItem.variant_data.variants.forEach(variant => {

    if (variant.option1 === value) {

      result = variant

    }

  })

  return result

}

const getAvailableVariants = getProductResponseItem => {

  const items = []
  let index = 1

  getProductResponseItem.variant_data.options[0].values.forEach(value => {

    const isAvailableVariant = isAvailable(value, getProductResponseItem)

    if (isAvailableVariant !== false) {

      items.push({
        index: index++,
        name: value,
        price: isAvailableVariant.price,
        variant_id: isAvailableVariant.id
      })

    }

  })

  return items

}

const getVariantItemsMessage = (chat, getProductResponseItem, integrationData) => {

  const items = []

  getAvailableVariants(getProductResponseItem).forEach(variant => {

    const variantName = utils.getMessageEmoji(variant.index, chat.channel.type) + ' [B]' + variant.name + '[/B]'

    if (getProductResponseItem.variant_count === 1) {

      if (variant.price > 0) {
        items.push(variantName + '(' + utils.getCurrencyForIntlNumberFormat(variant.price, getProductResponseItem.currency_code, integrationData.getPricePrecision()) + ')')
      } else {
        items.push(variantName + '(' + utils.getCurrencyForIntlNumberFormat(getProductResponseItem.sell_price, getProductResponseItem.currency_code, integrationData.getPricePrecision()) + ')')
      }

    } else {
      items.push(variantName)
    }

  })

  return items.join('[BR][/BR]')

}

const getVariantTitle = item => {

  return '[B]' + item.variant_data.options[0].name + '[/B]'

}

module.exports = async (req, chat, integration, chatIntegration, data) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return AddToCartForWhatsapp(req, chat, integration, chatIntegration, data)
  }

  const botData = await GetBotData(chat, 50)

  if ( ! botData) {
    return
  }

  const agent = botData.agent

  const customerSelectedProductIndex = botData.customer_message.vContentText

  if (customerSelectedProductIndex < 0) {
    return false
  }

  // Çoklu Ürün Paylaşımı var mı kontrolü yapılır.
  if (customerSelectedProductIndex === 0 && botData.agent_message.vBotData.getMultiShare()) {
    return MultiProductsShare(req, chat, integration, chatIntegration, botData.agent_message, agent)
  }

  // Ürün mesajı bulunuyor
  const productShareMessage = botData.messages.find(message => {

    if (message.from_type !== enums.message_from_types.AGENT) {
      return false
    }

    if (message.content && message.content.bot_data && message.content.bot_data.product_share_counter) {

      if (message.content.bot_data.product_share_counter == customerSelectedProductIndex) {
        return true
      }

    }

    return false

  })

  if ( ! productShareMessage) {
    return false
  }

  if ( ! productShareMessage.content.bot_data) {
    return false
  }

  let messageData

  // Heloscope tarafından ürün bilgileri alınıyor.
  const getProductResponse = await HeloscopeService.getProduct(req.language, integration, chatIntegration, {
    product_id: productShareMessage.content.bot_data.product_id,
    fetch_product_detail: true
  }, chatIntegration.ext_id, req.trace_id)

  if ( ! getProductResponse) {
    return
  }

  // Ürün Variantı birden fazla ise variant bilgileri kullanıcıya gönderilir
  if (getProductResponse.item.variant_count > 0) {

    const botActionAddToCartDto = new BotActionAddToCartDto()
    botActionAddToCartDto.setBotData({
      product_id: productShareMessage.content.bot_data.product_id,
      variants: getAvailableVariants(getProductResponse.item)
    })

    // Müşteriye gönderilecek mesajın datası
    messageData = {
      message_type: enums.message_types.TEXT,
      message_data: {
        text: req.t('App.success.integration.add_to_cart_variant', {
          variant: getVariantItemsMessage(chat, getProductResponse.item, integration.vData),
          variant_title: getVariantTitle(getProductResponse.item),
          interpolation: {escapeValue: false}
        }),
        language: data.language,
        agent_id: agent._id,
        next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_VARIANT1,
        bb_code: true,
        bot_data: botActionAddToCartDto.getBotData()
      }
    }

  } else {

    const addToCartDto = new AddToCartDto()

    addToCartDto.setChat(chat)
    addToCartDto.setIntegration(integration)
    addToCartDto.setData({product_id: productShareMessage.content.bot_data.product_id})
    addToCartDto.setExtId(chatIntegration.ext_id)
    addToCartDto.setItemName(getProductResponse.item.title)
    addToCartDto.setAgentId(agent.id)
    addToCartDto.setChatIntegration(chatIntegration)

    // Heloscope tarafında sepete ekleme işlemi yapılıyor
    const response = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.ADD_TO_CART, enums.HELOSCOPE_ACTION_TYPES.USER, {
      product_id: productShareMessage.content.bot_data.product_id,
    }, req.language, integration, chatIntegration, chatIntegration.ext_id)

    // Müşteriye gönderilecek mesajın datası
    messageData = {
      message_type: enums.message_types.TEXT,
      message_data: {
        text: req.t('App.success.integration.add_to_cart', {
          item_name: response.item.item_name,
          interpolation: {escapeValue: false},
        })
      }
    }

  }

  // Kullanıcıya sepetine ürün eklendiğine dair mesaj gönderildi
  await ChatService.addAgentMessage(req, chat.id, messageData.message_type, messageData.message_data, agent.id, undefined, {mark_as_seen_event: true})

  // Müşterinin Stage bilgisi güncelleniyor
  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_ADD_TO_CART)

  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')

  await chatIntegration.save()

  // Soket üzerinden agent tarafına sepete ürün eklendiğine dair bilgi gönderiliyor
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ITEM_ADDED_TO_CART,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      item_name: getProductResponse.item.title,
    }
  }, req.language)

  // Soket üzerinden agenta müşterinin stage bilgisi değiştiğine dair bilgi gönderiliyor
  return QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_ADD_TO_CART
    }
  }, req.language)

}
