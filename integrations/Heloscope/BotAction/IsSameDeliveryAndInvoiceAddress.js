const enums = require('../../../libs/enums')

const QueueService = require('../../../services/QueueService')
const ChatService = require('../../../services/ChatService')

const SendInvoiceAddress = require('../../../integrations/Heloscope/BotAction/SendInvoiceAddress')

const IsSameDeliveryAndInvoiceAddress = require('../../../integrations/Heloscope/BotAction/Whatsapp/IsSameDeliveryAndInvoiceAddress')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const SendCargoOptions = require('./SendCargoOptions')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return IsSameDeliveryAndInvoiceAddress(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  const agent = botData.agent

  switch (parseInt(botData.customer_message.content.text)) {

    //Müşteri 1 e basarsa Fatura Adresini, Teslimat Adresi ile Aynı seçmek istemiştir
    case 1:

      // Stage bilgisi ve teslimat adresi bilgisi bizim tarafta kaydediliyor
      const chatIntegrationData = chatIntegration.vData

      chatIntegrationData.setInvoiceAddressId(chatIntegrationData.getDeliveryAddressId())
      chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_SELECT_ADDRESS)

      chatIntegration.data = chatIntegrationData.getData()

      chatIntegration.markModified('data')

      await chatIntegration.save()

      // Soket üzerinden fatura adresi seçildiğine dair bilgisi gönderiliyor
      QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.INVOICE_ADDRESS_SELECTED,
        socket_rooms: [agent.vSocketCode],
        data: {
          chat_id: chat.id,
          integration_id: integration.id,
          invoice_address_id: chatIntegrationData.getDeliveryAddressId()
        }
      }, req.language)

      // Soket üzerinden stage bilgisi gönderiliyor
      QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
        socket_rooms: [agent.vSocketCode],
        data: {
          chat_id: chat.id,
          integration_id: integration.id,
          stage: enums.ORDER_STAGES.STAGE_SELECT_ADDRESS
        }
      }, req.language)

      // Kargo seçeneklerine dair bilgi alınıyor.
      const messageData = await SendCargoOptions(req, chat, integration, chatIntegration, chatIntegration.ext_id)

      return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
        text: req.t('App.integration.send_cargo_message', {
          cargo_options: messageData.cargo_names,
          interpolation: { escapeValue: false }
        }),
        next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_CARGO,
        agent_id: agent.id,
        language: req.language,
        bot_data: messageData.cargo_dto,
        bb_code: true,
      }, agent.id, undefined, { mark_as_seen_event: true })


    // Müşteri 2 seçerse fatura adresi bilgileri gönderilecek
    case 2:

      // Müşteriye address bilgilerini gönderiyoruz
      const messageAddressData = await SendInvoiceAddress(req, chat, integration, chatIntegration, chatIntegration.ext_id)

      return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
        text: req.t('App.success.integration.customer_invoice_address_message', {
          interpolation: { escapeValue: false }
        }) + messageAddressData.message_data,
        next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_INVOICE_ADDRESS,
        agent_id: agent.id,
        language: req.language,
        bot_data: messageAddressData.address_dto.getBotData(),
        bb_code: true,
        hide_image: true,
      }, agent.id, undefined, { mark_as_seen_event: true })

    default:
      break

  }
}
