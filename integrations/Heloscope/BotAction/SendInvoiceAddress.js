const createError = require('http-errors')

const enums = require('../../../libs/enums')
const utils = require('../../../libs/utils')

const SendAddressMessageDto = require('../../../dtos/BotAction/SendAddressMessageDto')

const HeloscopeIntegrationService = require('../../../integrations/Heloscope/HeloscopeIntegrationService')

module.exports = async (req, chat, integration, chatIntegration, extId) => {

  const addresses = []
  const messageCaptions = []
  const messageForWhatsapp = []

  const chatIntegrationData = chatIntegration.vData

  const sendAddressMessageDto = new SendAddressMessageDto()

  // Üyeliksiz Durum için
  if ( ! extId) {

    //Kullanıcının paylaşılacak adresi yoksa
    if (chatIntegrationData.getGuestAddresses().length === 0) {
      return ChatService.addSystemMessage(req, chat, enums.message_types.TEXT, {
        text: req.t('App.integration.send_empty_address_message')
      },{mark_as_seen_event: true} )
    }

    //misafir kullanıcın adres bilgilerini geziyoruz
    chatIntegrationData.getGuestAddresses().forEach(item => {

      if (item.id != chatIntegrationData.getDeliveryAddressId()) {

        //Botdata da kullanmak için sakladık.
        addresses.push({
          address_id: item.id,
          address_index: addresses.length + 1
        })

        messageCaptions.push(req.t('App.integration.send_address_message_caption', {
          emoji: utils.getMessageEmoji(addresses.length, chat.channel.type),
          address: item.data.address,
          city: item.extra.city_name,
          town: item.extra.town_name,
          interpolation: {escapeValue: false}
        }))

        // Whatsapp List Mesaj için data alınıyor
        let title = `${item.extra.city_name} / ${item.extra.town_name}`

        messageForWhatsapp.push({
          id: item.id.toString(),
          title: title.substring(0, 23),
          description: item.data.address.substring(0, 71),
        })

      }

    })

    sendAddressMessageDto.setBotData(addresses)

    return {
      address_dto: sendAddressMessageDto,
      message_data: messageCaptions.join('[BR][/BR][BR][/BR]'),
      whatsapp_message_data: messageForWhatsapp
    }

  }

  // Kullanıcı adresleri alınıyor.
  const getCustomerAddressesResponse = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_ADDRESSES,enums.HELOSCOPE_ACTION_TYPES.USER, {}, req.language, integration, chatIntegration,extId)

  if (getCustomerAddressesResponse.items.length === 0) {
    return ChatService.addSystemMessage(req, chat, enums.message_types.TEXT, {
      text: req.t('App.integration.send_empty_address_message')
    },{mark_as_seen_event: true} )
  }

  // Adresler mesaj için uygun formata getiriliyor
  getCustomerAddressesResponse.items.forEach(item => {

    if (item.id != chatIntegrationData.getDeliveryAddressId()) {

      addresses.push({
        address_id: parseInt(item.id),
        address_index: addresses.length + 1
      })

      messageCaptions.push(req.t('App.integration.send_address_message_caption', {
        emoji: utils.getMessageEmoji(addresses.length, chat.channel.type),
        address: item.address,
        city: item.city_name,
        town: item.town_name,
        interpolation: {escapeValue: false}
      }))

      // Whatsapp List Mesaj için data alınıyor
      let title = `${item.city_name} / ${item.town_name}`

      messageForWhatsapp.push({
        id: item.id.toString(),
        title: title.substring(0, 23),
        description: item.address.substring(0, 71),
      })

    }

  })

  sendAddressMessageDto.setBotData(addresses)

  return {
    address_dto: sendAddressMessageDto,
    message_data: messageCaptions.join('[BR][/BR][BR][/BR]'),
    whatsapp_message_data: messageForWhatsapp
  }

}
