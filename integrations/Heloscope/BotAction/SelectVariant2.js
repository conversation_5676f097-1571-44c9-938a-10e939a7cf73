const enums = require('../../../libs/enums')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')

const HeloscopeService = require('../HeloscopeService')
const HeloscopeIntegrationService = require('../HeloscopeIntegrationService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const SelectVariant2ForWhatapp = require('./Whatsapp/SelectVariant2')

const getAddToCartData = (customerLastMessage, botData, item) => {

  let data = {}

  item.variant_data.variants.forEach(item => {

    if (item.option1 == botData.variant_name && item.option2 == getOption2(customerLastMessage, botData)) {

      data = {
        product_id: botData.product_id,
        variant_cid: item.id,
      }

    }

  })

  return data

}

const getOption2 = (customerLastMessage, botData) => {

  let option2 = ''

  botData.variants.forEach(variant => {
    if (variant.index == customerLastMessage.vContentText) {
      option2 = variant.name
    }
  })

  return option2

}

const getItemName = (item, botData, customerLastMessage) => {
  return item.title + ' (' + botData.variant_name + ' ' + getOption2(customerLastMessage, botData) + ')'
}

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return SelectVariant2ForWhatapp(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  const agent = botData.agent

  // ID bilgisine göre ürün tsoft tarafından alınıyor.
  const getProductResponse = await HeloscopeService.getProduct(req.language, integration, chatIntegration, {
    product_id: botData.bot_data.product_id
  }, undefined, req.trace_id)

  // Mesaj içerisinden ürün ve variant bilgileri alınıyor
  const data = getAddToCartData(botData.customer_message, botData.bot_data, getProductResponse.item)

  // Üyelikli veya üyeliksiz olarak tsofta göre sepete ürün ekleme işlemi yapılıyor
  const messageData = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.ADD_TO_CART, enums.HELOSCOPE_ACTION_TYPES.USER, {
    product_id: data.product_id,
    variant_cid: data.variant_cid,
    count: 1
  }, req.language, integration, chatIntegration, chatIntegration.ext_id)

  // Müşteriye sepetine ürün eklendiğine dair mesaj gönderiliyor
  await ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
    text: req.t('App.success.integration.add_to_cart', {
      item_name: messageData.item.item_name,
      interpolation: { escapeValue: false },
    })
  }, agent.id, undefined, { mark_as_seen_event: true })

  // Müşteri stage bilgisi güncelleniyor.
  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_ADD_TO_CART)

  chatIntegration.user_data = chatIntegrationData.getData()
  chatIntegration.markModified('chat_integration')

  await chatIntegration.save()

  // Soket üzerinden sepete ürün eklendiğine dair mesaj iletiliyor
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ITEM_ADDED_TO_CART,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      item_name: getProductResponse.item.title,
    }
  }, req.language)

  // Müşteri bilgisi soket üzerinden gönderiliyor
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_ADD_TO_CART
    }
  }, req.language)


}