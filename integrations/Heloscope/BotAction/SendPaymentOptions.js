const createError = require('http-errors')

const utils = require('../../../libs/utils')
const enums = require('../../../libs/enums')

const SendPaymentOptions = require('../../../dtos/BotAction/SendPaymentOptions')
const HeloscopeIntegrationService = require('../../../integrations/Heloscope/HeloscopeIntegrationService')

const ChatService = require('../../../services/ChatService')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajın gönderilebilirliğini kontrol ediyor.
  const sendable = await ChatService.checkSendable(chat.id, chat.channel.type)

  if (!sendable.sendable) {
    throw new createError.BadRequest(req.t('App.errors.dash.conversation_active_time_expired'))
  }

  const getPaymentOptions = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_PAYMENT_OPTIONS, enums.HELOSCOPE_ACTION_TYPES.USER, {}, req.language, integration, chatIntegration, chatIntegration.ext_id)

  const payments = []
  const paymentOptionsTexts = []
  let paymentOptionsForWhatsapp = []

  let index = 0



  for (const paymentOption of getPaymentOptions.items) {

    const childs = []

    if (paymentOption.options.length > 0) {

      paymentOption.options.forEach(child => {

        paymentOptionsTexts.push(utils.getMessageEmoji(++index, chat.channel.type) + ' [B]' + paymentOption.name + ' / ' + child.name + '[/B]')

        childs.push({
          child_id: child.id,
          child_index: index
        })

        let title = `${paymentOption.name} / ${child.name}`

        paymentOptionsForWhatsapp.push({
          id: `${paymentOption.id}/${child.id}`,
          title: title.substring(0, 23),
          description: ''
        })

      })

    } else {

      paymentOptionsTexts.push(utils.getMessageEmoji(++index, chat.channel.type) + ' [B]' + paymentOption.name + '[/B]')

      paymentOptionsForWhatsapp.push({
        id: paymentOption.id.toString(),
        title: paymentOption.name.substring(0, 23),
        description: ''
      })

    }

    payments.push({
      payment_id: paymentOption.id,
      payment_index: index,
      childs: childs
    })
  }

  const sendPaymentOptions = new SendPaymentOptions()
  sendPaymentOptions.setBotData(payments)

  return {
    payment_dto: sendPaymentOptions.getBotData(),
    message_data: paymentOptionsTexts.join('[BR][/BR]'),
    payments_whatsapp: paymentOptionsForWhatsapp
  }

}
