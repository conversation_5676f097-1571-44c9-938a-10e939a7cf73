const createError = require('http-errors')

const enums = require('../../../libs/enums')
const utils = require('../../../libs/utils')
const ChatService = require('../../../services/ChatService')

const HeloscopeIntegrationService = require('../../../integrations/Heloscope/HeloscopeIntegrationService')

const SendAddressMessageDto = require('../../../dtos/BotAction/SendAddressMessageDto')

const SendDeliveryAddress = async (req, chat, integration, chatIntegration, extId) => {

  const addresses = []
  const messageCaptions = []
  const messageForWhatsapp = []

  const chatIntegrationData = chatIntegration.vData

  const sendAddressMessageDto = new SendAddressMessageDto()

  // Üyeliksiz Müşteri için adressler bizim tarafımızdan mesaja göre formatlanıyor.
  if ( ! extId) {

    //Kullanıcının paylaşılacak adresi yoksa
    if (chatIntegrationData.getGuestAddresses().length === 0) {
      return ChatService.addSystemMessage(req, chat, enums.message_types.TEXT, {
        text: req.t('App.integration.send_empty_address_message')
      },{mark_as_seen_event: true} )
    }

    //misafir kullanıcın adres bilgilerini geziyoruz
    chatIntegrationData.getGuestAddresses().forEach(item => {

      //Botdata da kullanmak için sakladık.
      addresses.push({
        address_id: item.id,
        address_index: addresses.length + 1
      })

      messageCaptions.push(req.t('App.integration.send_address_message_caption', {
        emoji: utils.getMessageEmoji(addresses.length, chat.channel.type),
        address: item.data.address,
        city: item.extra.city_name,
        town: item.extra.town_name,
        interpolation: {escapeValue: false}
      }))

      // Whatsapp List Mesaj için data alınıyor
      let title = `${item.extra.city_name} / ${item.extra.town_name}`

      messageForWhatsapp.push({
        id: item.id.toString(),
        title: title.substring(0, 23),
        description: item.data.address.substring(0, 71),
      })

    })

    sendAddressMessageDto.setBotData(addresses)

    return {
      address_dto: sendAddressMessageDto.getBotData(),
      message_data: messageCaptions.join('[BR][/BR][BR][/BR]'),
      whatsapp_message_data: messageForWhatsapp
    }

  }

  // Üyelikli Müşteri addresleri Heloscope tarafından alınıyor
  const getCustomerAddressesResponse = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_ADDRESSES, enums.HELOSCOPE_ACTION_TYPES.USER, {}, req.language, integration, chatIntegration, extId)

  if (getCustomerAddressesResponse.items.length === 0) {
    return ChatService.addSystemMessage(req, chat, enums.message_types.TEXT, {
      text: req.t('App.integration.send_empty_address_message')
    },{mark_as_seen_event: true} )
  }

  getCustomerAddressesResponse.items.forEach(item => {

    addresses.push({
      address_id: item.id,
      address_index: addresses.length + 1
    })

    messageCaptions.push(req.t('App.integration.send_address_message_caption', {
      emoji: utils.getMessageEmoji(addresses.length, chat.channel.type),
      address: item.content,
      city: item.city_name,
      town: item.town_name,
      interpolation: {escapeValue: false}
    }))

    // Whatsapp List Mesaj için data alınıyor
    let title = `${item.city_name} / ${item.town_name}`

    messageForWhatsapp.push({
      id: item.id.toString(),
      title: title.substring(0, 23),
      description: item.content.substring(0, 71),
    })

  })

  sendAddressMessageDto.setBotData(addresses)

  return {
    address_dto: sendAddressMessageDto.getBotData(),
    message_data: messageCaptions.join('[BR][/BR][BR][/BR]'),
    whatsapp_message_data: messageForWhatsapp
  }

}

module.exports = SendDeliveryAddress
