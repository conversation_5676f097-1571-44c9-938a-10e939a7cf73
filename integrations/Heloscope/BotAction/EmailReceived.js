const enums = require('../../../libs/enums')
const helpers = require('../../../libs/helpers')

const User = require('../../../models/User')

const QueueService = require('../../../services/QueueService')
const ChatService = require('../../../services/ChatService')

const SendDeliveryAddress = require('../../../integrations/Heloscope/BotAction/SendDeliveryAddress')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  const agent = botData.agent

  //Email uygun mu diye Kontrol Edildi. Yoksa tekrar emailini almak için mesaj gönderdik
  const status = helpers.checkEmail(botData.customer_message.content.text.toLowerCase())
  if (!status) {

    return req.app.services.ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
      text: req.t('App.success.ask_form_questions.email'),
      next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.EMAIL_RECEIVED,
      agent_id: agent.id,
      language: req.language,
    }, agent.id, undefined, { mark_as_seen_event: true })

  }

  // Müşterinini bize göndermiş olduğu email i kendi tarafımıza kaydediyoruz
  chat.email = botData.customer_message.content.text.toLowerCase()
  await chat.save()

  // Müşteri bilgilerini güncellendiğine dair soketten bilgi gönderiyoruz
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CUSTOMER_DATA_UPDATED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
    }
  }, req.language)

  // Address bilgilerini alıyoruz
  const messageData = await SendDeliveryAddress(req, chat, integration, chatIntegration, chatIntegration.ext_id)

  let messageType
  let messageContent

  switch (chat.channel.type) {

    case enums.channel_types.WHATSAPP_NUMBER:

      messageType = enums.message_types.WHATSAPP_INTERACTIVE
      messageContent = {
        sub_type: enums.message_types.LIST,
        text: req.t('App.success.integration.customer_address_message'),
        bb_code: true,
        buttons: [{
          title: req.t('Global.chat_message.select_address'),
          rows: messageData.whatsapp_message_data
        }]
      }

      break

    default:

      messageType = enums.message_types.TEXT
      messageContent = {
        text: req.t('App.success.integration.customer_address_message', {
          interpolation: { escapeValue: false }
        }) + messageData.message_data,
        bb_code: true,
        bot_data: messageData.address_dto,
        hide_image: true
      }

      break
  }

  messageContent.next_action = enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS
  messageContent.agent_id = agent.id
  messageContent.language = req.language

  // Müşteriye adress bilgilerini gönderiyoruz
  await ChatService.addAgentMessage(req, chat._id, messageType, messageContent, agent.id, undefined, { mark_as_seen_event: true })
}
