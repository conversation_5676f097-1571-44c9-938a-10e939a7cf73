const enums = require('../../../libs/enums')
const utils = require('../../../libs/utils')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')

const CompletedOrder = require('../../../models/CompletedOrder')

const HeloscopeService = require('../../../integrations/Heloscope/HeloscopeService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const CreateOrderForWhatapp = require('./Whatsapp/CreateOrder')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return CreateOrderForWhatapp(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  // sipariş oluşması için 1 yazmak zorunda
  if (parseInt(botData.customer_message.vContentText) !== 1) {
    return
  }

  const agent = botData.agent

  // Tsoft tarafında sipariş oluşturuluyor
  const createOrderResponse = await HeloscopeService.createOrder(req, req.language, chat, integration, chatIntegration, chatIntegration.ext_id)

  // Müşteri stage bilgisi güncelleniyor
  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_APPROVE_CART)

  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')

  await chatIntegration.save()

  // Soket üzerinden sipariş olduşturulduğuna dair bilgi gönderiliyor
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_CREATED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      order_code: createOrderResponse.order_code,
    }
  })

  // Soket üzerinden müşteri stage bilgsii değiştiğine dair bilgi gönderiliyor
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_APPROVE_CART
    }
  }, req.language)

  process.nextTick(async () => {
    const cartData = await HeloscopeService.getCartProcess(req.language, integration, chatIntegration, chatIntegration.ext_id, req.trace_id)

    new CompletedOrder({
      channel_id: chat.channel._id,
      chat_id: chat._id,
      chat_ext_id: chat.ext_id,
      agent_id: agent._id,
      total_price: utils.getFormattedPrice(cartData.total_amount, integration.vData.getPricePrecision())
    }).save()
  })

  // Sipariş oluşturulduğna dair mesaj gönderiiliyor
  return ChatService.addAgentMessage(req, chat.id, createOrderResponse.message_type, createOrderResponse.message_data, agent.id)
}
