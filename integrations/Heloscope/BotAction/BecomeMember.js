const enums = require('../../../libs/enums')

const User = require('../../../models/User')

const ChatService = require('../../../services/ChatService')

const SendDeliveryAddress = require('../../../integrations/Heloscope/BotAction/SendDeliveryAddress')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const BecomeMemberForWhatapp = require('./Whatsapp/BecomeMember')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return BecomeMemberForWhatapp(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if ( ! botData) {
    return
  }

  // Müşteri bizim mesajımıza isteidiğimiz gibi cevap vermiş mi kontrol ediyoruz
  if ( ! ['Evet', 'evet', 'Yes', 'yes'].includes(botData.customer_message.content.text)) {
    return
  }

  const agent = botData.agent

  if ( ! chatIntegration.ext_id) {

    if ( ! chat.email) {

      // Müşteri maili bizim tarafta kayıtlı değilse müşterinin mailini almamız için mesaj gönderiyoruz
      return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
        text: req.t('App.success.ask_form_questions.email'),
        next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.EMAIL_RECEIVED,
        agent_id: agent.id,
        language: req.language,
      }, agent.id, undefined, {mark_as_seen_event: true})

    }

  }

  // Müşteri addreslerini alıyoruz.
  const messageData = await SendDeliveryAddress(req, chat, integration, chatIntegration, chatIntegration.ext_id)

  // Müşteriye addreslerini gönderiyoruz
  return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
    text: req.t('App.success.integration.customer_address_message', {
      interpolation: {escapeValue: false}
    }) + messageData.message_data,
    next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS,
    agent_id: agent.id,
    language: req.language,
    bot_data: messageData.address_dto,
    bb_code: true,
    hide_image: true,
  }, agent.id, undefined, {mark_as_seen_event: true})

}
