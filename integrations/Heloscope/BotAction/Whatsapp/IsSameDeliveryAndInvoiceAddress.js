const enums = require('../../../../libs/enums')

const QueueService = require('../../../../services/QueueService')
const ChatService = require('../../../../services/ChatService')

const SendInvoiceAddress = require('../../../../integrations/Heloscope/BotAction/SendInvoiceAddress')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

const SendCargoOptions = require('./../SendCargoOptions')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if ( ! botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.IS_SAME_DELIVERY_AND_INVOICE_ADDRESS) {
    return
  }

  const agent = botData.agent

  switch (botData.customer_message.vContent.interactive[`${botData.message_type}`].id) {

    //Müşteri 1 e basarsa Fatura Adresini, Teslimat Adresi ile Aynı seçmek istemiştir
    case botData.agent_message.vContent.buttons[0].reply.id:

      // Stage bilgisi ve teslimat adresi bilgisi bizim tarafta kaydediliyor
      const chatIntegrationData = chatIntegration.vData

      chatIntegrationData.setInvoiceAddressId(chatIntegrationData.getDeliveryAddressId())
      chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_SELECT_ADDRESS)

      chatIntegration.data = chatIntegrationData.getData()

      chatIntegration.markModified('data')

      await chatIntegration.save()

      // Soket üzerinden fatura adresi seçildiğine dair bilgisi gönderiliyor
      QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.INVOICE_ADDRESS_SELECTED,
        socket_rooms: [agent.vSocketCode],
        data: {
          chat_id: chat.id,
          integration_id: integration.id,
          invoice_address_id: chatIntegrationData.getDeliveryAddressId()
        }
      }, req.language)

      // Soket üzerinden stage bilgisi gönderiliyor
      QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
        socket_rooms: [agent.vSocketCode],
        data: {
          chat_id: chat.id,
          integration_id: integration.id,
          stage: enums.ORDER_STAGES.STAGE_SELECT_ADDRESS
        }
      }, req.language)

      // Kargo seçeneklerine dair bilgi alınıyor.
      const messageData = await SendCargoOptions(req, chat, integration, chatIntegration, chatIntegration.ext_id)

      return ChatService.addAgentMessage(req, chat.id, enums.message_types.WHATSAPP_INTERACTIVE, {
        text: req.t('App.integration.whatsapp_send_cargo_message'),
        next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_CARGO,
        agent_id: agent.id,
        language: req.language,
        bb_code: true,
        buttons: [{
          title: req.t('Global.chat_message.select_cargo'),
          rows: messageData.cargo_names_whatsapp
        }],
        sub_type: enums.message_types.LIST
      }, agent.id, undefined, { mark_as_seen_event: true })


    // Müşteri 2 seçerse fatura adresi bilgileri gönderilecek
    case botData.agent_message.vContent.buttons[1].reply.id:

      // Müşteriye address bilgilerini gönderiyoruz
      const messageAddressData = await SendInvoiceAddress(req, chat, integration, chatIntegration, chatIntegration.ext_id)

      return ChatService.addAgentMessage(req, chat.id, enums.message_types.WHATSAPP_INTERACTIVE, {
        text: req.t('App.success.integration.customer_invoice_address_message', {
          interpolation: { escapeValue: false }
        }),
        next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_INVOICE_ADDRESS,
        agent_id: agent.id,
        language: req.language,
        bb_code: true,
        hide_image: true,
        buttons: [{
          title: req.t('Global.chat_message.select_address'),
          rows: messageAddressData.whatsapp_message_data
        }],
        sub_type: enums.message_types.LIST
      }, agent.id, undefined, { mark_as_seen_event: true })

    default:
      break

  }

}
