const enums = require('../../../../libs/enums')
const utils = require('../../../../libs/utils')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

const SendDeliveryAddress = require('../../../../integrations/Heloscope/BotAction/SendDeliveryAddress')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if ( ! botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.CREATE_CART) {
    return
  }

  // Agentın gönderdiği mesajda buton id si ile müşterinin butona bastığı id ile aynı mı kontrol ediliyor
  const selectedButton = botData.agent_message.vContent.buttons.find(item => item.reply.id === botData.customer_message.vContent.interactive[`${botData.message_type}`].id)

  if ( ! selectedButton) {
    return
  }

  const agent = botData.agent

  // Müşteri Üyeliksiz ise kredi kartı ile ödeme yapamayacağına dair mesaj gönderiliyor
  if ( ! chatIntegration.ext_id) {

    return ChatService.addAgentMessage(req, chat.id, enums.message_types.WHATSAPP_INTERACTIVE, {
      text: req.t('App.success.ask_form_questions.whatsapp_become_member'),
      next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.BECOME_MEMBER,
      bb_code: true,
      sub_type: enums.message_types.BUTTON,
      header: {
        type: 'text',
        text: req.t('Global.chat_message.confirm')
      },
      buttons: [{
        type: 'reply',
        reply: {
          id: utils.generateHash(15),
          title: req.t('Global.chat_message.confirm')
        }
      }],
      agent_id: agent.id,
      language: req.language,
    }, agent.id, undefined, {mark_as_seen_event: true})

  }

  // Soket üzerinden Kart oluşturuldu bilgisi gönderiliyor.
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CREATED_CART,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
    }
  }, req.language)

  const messageData = await SendDeliveryAddress(req, chat, integration, chatIntegration, chatIntegration.ext_id)

  // Müşteriye addreslerini seçmesi için bot mesajı gönderiliyor
  return ChatService.addAgentMessage(req, chat.id, enums.message_types.WHATSAPP_INTERACTIVE, {
    text: req.t('App.success.integration.customer_address_message'),
    next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS,
    agent_id: agent.id,
    language: req.language,
    buttons: [{
      title: req.t('Global.chat_message.select_address'),
      rows: messageData.whatsapp_message_data
    }],
    bb_code: true,
    sub_type: enums.message_types.LIST
  }, agent.id, undefined, {mark_as_seen_event: true})
}
