const enums = require('../../../../libs/enums')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const UserService = require('../../../../services/UserService')
const SendPaymentOptions = require('../../BotAction/SendPaymentOptions')
const HeloscopeIntegrationService = require('../../../../integrations/Heloscope/HeloscopeIntegrationService')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

module.exports = async (req, chat, integration, chatIntegration, extId) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if ( ! botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_CARGO) {
    return
  }

  const agent = botData.agent

  // mesaj içerisinden seçilen kargo bilgisi hangisi diye bakılıyor
  const cargo = botData.agent_message.vContent.buttons[0].rows.find(item => {
    return item.id == botData.customer_message.vContent.interactive[`${botData.message_type}`].id
  })

  if ( ! cargo) {
    return
  }

  const cargoOptionId = cargo.id

  await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.SELECT_CARGO_OPTION, enums.HELOSCOPE_ACTION_TYPES.USER, {
    id: cargoOptionId
  }, req.language, integration, chatIntegration, extId)

  // müşteri bot üzerinden kargo yöntemini seçti, seçtiği bu bilgileri ilgili kısma kaydedeceğiz
  const chatIntegrationData = chatIntegration.vData

  // Eğer adres seçili değilse işlem burada sonlandırılacak
  if ( ! chatIntegrationData.getDeliveryAddressId() || ! chatIntegrationData.getInvoiceAddressId()) {
    return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
      text: req.t('Global.chat_message.empty_basket'),
      agent_id: agent.id,
      language: req.language,
    }, agent.id, undefined, {mark_as_seen_event: true})
  }

  chatIntegrationData.setCargoOptionId(cargoOptionId)
  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_SELECT_CARGO)

  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')

  await chatIntegration.save()

  // Soket üzerinden kargo seçildiğine dair mesaj gönderiliyor
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CARGO_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      cargo_option_id: cargoOptionId, // ek bilgi olarak paylaşıyoruz
    }
  }, req.language)

  // Soket üzerinden müşterinin stage bilgsini gönderiyoruz
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_SELECT_CARGO
    }
  }, req.language)

  // Heloscope tarafından ödeme yöntemleri alınıyor
  const messageData = await SendPaymentOptions(req, chat, integration, chatIntegration)

  for (let index = 0; index < messageData.payments_whatsapp.length; index += 10) {

    let messages = messageData.payments_whatsapp.slice(index, index + 10)

    // Ödeme yöntemleri mesaj olarak gönderiliyor.
    await ChatService.addAgentMessage(req, chat.id, enums.message_types.WHATSAPP_INTERACTIVE, {
      text: index == 0 ? req.t('App.success.integration.whatsapp_payment_type') : req.t('App.success.integration.whatsapp_payment_type_continue'),
      bb_code: true,
      next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_PAYMENT_OPTION,
      agent_id: agent.id,
      language: req.language,
      buttons: [{
        title: req.t('Global.chat_message.select_payment'),
        rows: messages
      }],
      sub_type: enums.message_types.LIST
    }, agent.id, undefined, {mark_as_seen_event: true})
  }

}
