const enums = require('../../../../libs/enums')

const User = require('../../../../models/User')

const ChatService = require('../../../../services/ChatService')

const SendDeliveryAddress = require('../../../../integrations/Heloscope/BotAction/SendDeliveryAddress')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if ( ! botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.BECOME_MEMBER) {
    return
  }

  // Agentın gönder<PERSON>ği mesajda buton id si ile müşterinin butona bastığı id ile aynı mı kontrol ediliyor
  const selectedButton = botData.agent_message.vContent.buttons.find(item => item.reply.id === botData.customer_message.vContent.interactive[`${botData.message_type}`].id)

  if ( ! selectedButton) {
    return
  }

  const agent = botData.agent

  if ( ! chatIntegration.ext_id) {

    if ( ! chat.email) {

      // Müşteri maili bizim tarafta kayıtlı değilse müşterinin mailini almamız için mesaj gönderiyoruz
      return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
        text: req.t('App.success.ask_form_questions.email'),
        next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.EMAIL_RECEIVED,
        agent_id: agent.id,
        language: req.language,
      }, agent.id, undefined, {mark_as_seen_event: true})

    }

  }

  // Müşteri addreslerini alıyoruz.
  const messageData = await SendDeliveryAddress(req, chat, integration, chatIntegration, chatIntegration.ext_id)

  // Müşteriye addreslerini gönderiyoruz
  return ChatService.addAgentMessage(req, chat.id, enums.message_types.WHATSAPP_INTERACTIVE, {
    text: req.t('App.success.integration.customer_address_message'),
    next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS,
    agent_id: agent.id,
    language: req.language,
    bb_code: true,
    hide_image: true,
    buttons: [{
      title: req.t('Global.chat_message.select_address'),
      rows: messageData.whatsapp_message_data
    }],
    sub_type: enums.message_types.LIST
  }, agent.id, undefined, {mark_as_seen_event: true})

}
