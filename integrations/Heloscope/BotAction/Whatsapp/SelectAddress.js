const enums = require('../../../../libs/enums')
const utils = require('../../../../libs/utils')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const HeloscopeIntegrationService = require('../../../../integrations/Heloscope/HeloscopeIntegrationService')

const SendCargoOptions = require('./../SendCargoOptions')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

const saveChatIntegrationData = (chatIntegration, address, withInvoice) => {

  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setDeliveryAddressId(address.id)

  // Eğer Addres 1 tane ise withInvoice değeri true olarak gelecek.
  if (withInvoice) {
    chatIntegrationData.setInvoiceAddressId(address.id)
    chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_SELECT_ADDRESS)
  }

  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')

  return chatIntegration.save()

}

const sendDataFromSocket = (req, chat, integration, agent) => {

  QueueService.publishToAppSocket({

    event: enums.agent_app_socket_events.DELIVERY_ADDRESS_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id
    }

  }, req.language)

  QueueService.publishToAppSocket({

    event: enums.agent_app_socket_events.INVOICE_ADDRESS_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id
    }

  }, req.language)

  QueueService.publishToAppSocket({

    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_SELECT_ADDRESS
    }

  }, req.language)

}

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if ( ! botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS) {
    return
  }

  const agent = botData.agent

  const getCartResponse = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_CART, enums.HELOSCOPE_ACTION_TYPES.USER, {}, req.language, integration, chatIntegration, chatIntegration.ext_id)

  // Sepet boş işse işleme devam etmiyoruz
  if (getCartResponse.items.length === 0) {
    return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
      text: req.t('Global.chat_message.empty_basket'),
      agent_id: agent.id,
      language: req.language,
    }, agent.id, undefined, {mark_as_seen_event: true})
  }

  // Bot mesajı içirisindeki data alınıyor
  const address = botData.agent_message.vContent.buttons[0].rows.find(item => {
    return item.id == botData.customer_message.vContent.interactive[`${botData.message_type}`].id
  })

  if ( ! address) {
    return
  }

  let status = true

  // Üyelikli durum için
  if (chatIntegration.ext_id) {

    const addresses = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_ADDRESSES, enums.HELOSCOPE_ACTION_TYPES.USER, {}, req.language, integration, chatIntegration, chatIntegration.ext_id)

    // Eğer 1 den fazla adresi bulunuyorsa invoice adresi ve delivery adresi aynı olsun mu diye sorulur
    if (addresses.items.length > 1) {

      await ChatService.addAgentMessage(req, chat.id, enums.message_types.WHATSAPP_INTERACTIVE, {
        text: req.t('App.success.ask_form_questions.whatsapp_is_same_delivery_and_invoce_address'),
        next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.IS_SAME_DELIVERY_AND_INVOICE_ADDRESS,
        agent_id: agent.id,
        bb_code: true,
        language: req.language,
        sub_type: enums.message_types.BUTTON,
        header: {
          type: 'text',
          text: req.t('Global.chat_message.select')
        },
        buttons: [{
          type: 'reply',
          reply: {
            id: utils.generateHash(15),
            title: req.t('Global.chat_message.yes')
          }
        },
          {
            type: 'reply',
            reply: {
              id: utils.generateHash(15),
              title: req.t('Global.chat_message.no')
            }
          }],
      }, agent.id)

      status = false

    }

  } else {

    // Üyeliksiz için
    const chatIntegrationData = chatIntegration.vData

    // Eğer 1 den fazla adresi bulunuyorsa invoice adresi ve delivery adresi aynı olsun mu diye sorulur
    if (chatIntegrationData.getGuestAddresses().length > 1) {

      await ChatService.addAgentMessage(req, chat.id, enums.message_types.WHATSAPP_INTERACTIVE, {
        text: req.t('App.success.ask_form_questions.whatsapp_is_same_delivery_and_invoce_address'),
        next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.IS_SAME_DELIVERY_AND_INVOICE_ADDRESS,
        agent_id: agent.id,
        bb_code: true,
        language: req.language,
        sub_type: enums.message_types.BUTTON,
        header: {
          type: 'text',
          text: req.t('Global.chat_message.select')
        },
        buttons: [{
          type: 'reply',
          reply: {
            id: utils.generateHash(15),
            title: req.t('Global.chat_message.yes')
          }
        },
          {
            type: 'reply',
            reply: {
              id: utils.generateHash(15),
              title: req.t('Global.chat_message.no')
            }
          }],
      }, agent.id)

      status = false
    }

  }

  // Addres sayısı 1 adet ise işlem buradan sonra devam etmeyecek
  if ( ! status) {

    await saveChatIntegrationData(chatIntegration, address, false)

    // Soket üzerinden Teslimat addresi seçildiğine dair bilgi soketten gönderiliyor
    return QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.DELIVERY_ADDRESS_SELECTED,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_id: chat.id,
        integration_id: integration.id
      }
    }, req.language)

  }

  const savedChatIntegration = await saveChatIntegrationData(chatIntegration, address, true)

  // address seçimi hem delivery hemde invoice ise soketten datalar gönderiliyor
  sendDataFromSocket(req, chat, integration, agent)

  // Kargo seçenekleri heloscope tarafından alınıyor
  const messageData = await SendCargoOptions(req, chat, integration, savedChatIntegration, chatIntegration.ext_id)

  // Kargo seçenekleri mesaj olarak gönderiliyor
  return ChatService.addAgentMessage(req, chat.id, enums.message_types.WHATSAPP_INTERACTIVE, {
    text: req.t('App.integration.whatsapp_send_cargo_message'),
    next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_CARGO,
    agent_id: agent.id,
    language: req.language,
    bb_code: true,
    buttons: [{
      title: req.t('Global.chat_message.select_cargo'),
      rows: messageData.cargo_names_whatsapp
    }],
    sub_type: enums.message_types.LIST
  }, agent.id, undefined, {mark_as_seen_event: true})

}
