const enums = require('./../../../../libs/enums')
const utils = require('./../../../../libs/utils')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const HeloscopeService = require('../../HeloscopeService')
const HeloscopeIntegrationService = require('../../../Heloscope/HeloscopeIntegrationService')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

const MultiProductsShare = require('../../../../modules/AgentApp/MultiProductsShare')

const isAvailable = (value, getProductResponseItem) => {

  let result = false

  getProductResponseItem.variant_data.variants.forEach(variant => {

    if (variant.option1 === value) {

      result = variant

    }

  })

  return result

}

const getAvailableVariants = getProductResponseItem => {

  return getProductResponseItem.variant_data.options[0].values.map(value => {

    const isAvailableVariant = isAvailable(value.trim(), getProductResponseItem)

    if (isAvailableVariant !== false) {

      return {
        name: value,
        price: isAvailableVariant.price,
        variant_id: isAvailableVariant.id
      }

    }

    return ''

  }).filter(item => item != '')

}

const getVariantsForListMessage = (getProductResponseItem, integrationData) => {

  return getAvailableVariants(getProductResponseItem).map(item => {

    if (getProductResponseItem.variant_count === 1) {

      if (item.price > 0) {
        return {
          id: item.variant_id.toString(),
          title: item.name,
          description: utils.getCurrencyForIntlNumberFormat(item.price, getProductResponseItem.currency_code, integrationData.getPricePrecision())
        }
      } else {
        return {
          id: item.variant_id.toString(),
          title: item.name,
          description: utils.getCurrencyForIntlNumberFormat(getProductResponseItem.sell_price, getProductResponseItem.currency_code, integrationData.getPricePrecision())
        }
      }

    }

    return {
      id: item.variant_id.toString(),
      title: item.name,
      description: ''
    }
  })
}

const getVariantTitle = item => {

  return '[B]' + item.variant_data.options[0].name + '[/B]'

}

module.exports = async (req, chat, integration, chatIntegration, data) => {

  const botData = await GetInteractiveData(chat)

  if (!botData) {
    return
  }

  const agent = botData.agent

  if (botData.agent_message.vContent.next_action !== enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.ADD_TO_CART) {
    return
  }

  // Agentın gönderdiği mesajda buton id si ile müşterinin butona bastığı id ile aynı mı kontrol ediliyor
  const selectedButton = botData.agent_message.vContent.buttons.find(item => item.reply.id === botData.customer_message.vContent.interactive[`${botData.message_type}`].id)

  if (!selectedButton) {
    return
  }

  // Ürün ID si alındı
  const productId = botData.agent_message.content.bot_data.product_id

  // Çoklu Ürün Paylaşımı var mı kontrolü yapılır.
  if (botData.agent_message.vBotData.getMultiShare()) {
    return MultiProductsShare(req, chat, integration, chatIntegration, botData.agent_message, agent)
  }

  let messageData

  // Heloscope tarafından ürün bilgileri alınıyor.
  const getProductResponse = await HeloscopeService.getProduct(req.language, integration, chatIntegration, {
    product_id: productId
  }, undefined, req.trace_id)

  if (!getProductResponse) {
    return
  }

  // Ürün Variantı birden fazla ise variant bilgileri kullanıcıya gönderilir
  if (getProductResponse.item.variant_count > 0) {

    // Müşteriye gönderilecek mesajın datası
    messageData = {
      message_type: enums.message_types.WHATSAPP_INTERACTIVE,
      message_data: {
        text: req.t('App.success.integration.whatsapp_add_to_cart_variant', {
          variant_title: getVariantTitle(getProductResponse.item),
          interpolation: { escapeValue: false }
        }),
        language: data.language,
        agent_id: agent._id,
        next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_VARIANT1,
        bb_code: true,
        bot_data: {
          product_id: productId
        },
        buttons: [{
          title: getProductResponse.item.variant_data.options[0].name,
          rows: getVariantsForListMessage(getProductResponse.item, integration.vData)
        }],
        sub_type: enums.message_types.LIST
      }
    }

  } else {

    // Heloscope tarafında sepete ekleme işlemi yapılıyor
    const response = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.ADD_TO_CART, enums.HELOSCOPE_ACTION_TYPES.USER, {
      product_id: productId
    }, req.language, integration, chatIntegration, chatIntegration.ext_id)

    // Müşteriye gönderilecek mesajın datası
    messageData = {
      message_type: enums.message_types.TEXT,
      message_data: {
        text: req.t('App.success.integration.add_to_cart', {
          item_name: response.item.item_name,
          interpolation: { escapeValue: false },
        })
      }
    }

  }

  // Kullanıcıya sepetine ürün eklendiğine dair mesaj gönderildi
  await ChatService.addAgentMessage(req, chat.id, messageData.message_type, messageData.message_data, agent.id, undefined, { mark_as_seen_event: true })

  // Müşterinin Stage bilgisi güncelleniyor
  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_ADD_TO_CART)

  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')

  await chatIntegration.save()

  // Soket üzerinden agent tarafına sepete ürün eklendiğine dair bilgi gönderiliyor
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ITEM_ADDED_TO_CART,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      item_name: getProductResponse.item.title,
    }
  }, req.language)

  // Soket üzerinden agenta müşterinin stage bilgisi değiştiğine dair bilgi gönderiliyor
  return QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_ADD_TO_CART
    }
  }, req.language)

}
