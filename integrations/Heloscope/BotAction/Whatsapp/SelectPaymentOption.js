const enums = require('../../../../libs/enums')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const SendOrderSummaryMessage = require('../../../../integrations/Heloscope/BotAction/SendOrderSummaryMessage')
const HeloscopeIntegrationService = require('../../../../integrations/Heloscope/HeloscopeIntegrationService')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')
const helpers = require('../../../../libs/helpers')

module.exports = async (req, chat, integration, chatIntegration, extId) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if ( ! botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_PAYMENT_OPTION) {
    return
  }

  const agent = botData.agent

  // mesaj içerisinden seçilen kargo bilgisi hangisi diye bakılıyor
  const paymentOptionData = botData.agent_message.vContent.buttons[0].rows.find(item => {
    return item.id == botData.customer_message.vContent.interactive[`${botData.message_type}`].id
  })

  if ( ! paymentOptionData) {
    return
  }

  const paymentSplitted = paymentOptionData.id.split('/')

  const paymentOptionId = paymentSplitted[0]
  const subPaymentOptionId = paymentSplitted.length > 1 ? paymentSplitted[1] : ''

  await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.SELECT_PAYMENT_OPTION, enums.HELOSCOPE_ACTION_TYPES.USER, {
    id: paymentOptionId,
    optionId: subPaymentOptionId
  }, req.language, integration, chatIntegration, extId)

  // müşteri bot üzerinden ödeme yöntemini seçti, seçtiği bu bilgileri ilgili kısma kaydedeceğiz
  const chatIntegrationData = chatIntegration.vData

  // Eğer cargo seçilmemiş ise işlem buradan sonra devam etmeyecek
  if ( ! chatIntegrationData.getCargoOptionId()) {
    return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
      text: req.t('Global.chat_message.empty_basket'),
      agent_id: agent.id,
      language: req.language,
    }, agent.id, undefined, {mark_as_seen_event: true})
  }

  chatIntegrationData.setPaymentOptionId(paymentOptionId)
  chatIntegrationData.setSubPaymentOptionId(subPaymentOptionId)
  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_SELECT_PAYMENT)

  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')

  await chatIntegration.save()

  // Soket üzerinden ödeme yöntemi seçildiğine dair bilgi veriyoruz
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.PAYMENT_OPTION_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      payment_option_id: paymentOptionId, // ek bilgi olarak paylaşıyoruz
      sub_payment_option_id: subPaymentOptionId, // ek bilgi olarak paylaşıyoruz
    }
  }, req.language)

  // Soket üzerinden müşteri stage bilgisi paylaşıyoruz
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_SELECT_PAYMENT
    }
  }, req.language)

  // Müşteri Sepet özetini alıyoruz
  const messageData = await SendOrderSummaryMessage(req, chat, integration, chatIntegration, agent.id)

  // Sepet özeti bilgisini mesaj olarak gönderiyoruz
  for (const message of messageData) {
    await ChatService.addAgentMessage(req, chat.id, message.message_type, message.message_data, agent.id, undefined, {mark_as_seen_event: true})
    await helpers.sleepFunction(1000)
  }
}
