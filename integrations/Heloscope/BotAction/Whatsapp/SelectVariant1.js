const enums = require('../../../../libs/enums')
const utils = require('../../../../libs/utils')

const SelectVariant1Dto = require('../../../../dtos/BotAction/SelectVariant1Dto')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const HeloscopeIntegrationService = require('../../HeloscopeIntegrationService')
const HeloscopeService = require('../../HeloscopeService')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

const getChildrenItemsMessage = (getProductResponseItem, forSecondVariants, integrationData) => {

  return getAvailableVariants(forSecondVariants).map(variant => {

    if (variant.price > 0) {
      return {
        id: variant.id.toString(),
        title: variant.name,
        description: utils.getCurrencyForIntlNumberFormat(variant.price, getProductResponseItem.currency_code, integrationData.getPricePrecision())
      }
    } else {
      return {
        id: variant.id.toString(),
        title: variant.name,
        description: utils.getCurrencyForIntlNumberFormat(getProductResponseItem.sell_price, getProductResponseItem.currency_code, integrationData.getPricePrecision())
      }
    }

  })

}

const getVariantsByFirstVariantItem = (getProductResponseItem, firstVariantItem) => {

  const variants = []

  getProductResponseItem.variant_data.variants.forEach(variant => {

    if (variant.option1 === firstVariantItem.title) {
      variants.push(variant)
    }

  })

  return variants
}

const getVariantTitle = item => {

  return '[B]' + item.variant_data.options[1].name + '[/B]'

}


const isAvailable = (item) => {

  let result = false

  if (item.stock_count > 0) {

    result = true

  }

  return result

}

const getAvailableVariants = forSecondVariants => {

  const items = []
  let index = 1

  forSecondVariants.forEach(value => {

    if (isAvailable(value, forSecondVariants)) {

      items.push({
        index: index++,
        id: value.id,
        name: value.option2,
        price: value.price
      })

    }

  })

  return items

}

const getItemName = (item, firstVariantItem) => {
  return item.title + ' (' + firstVariantItem.title + ')'
}

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if ( ! botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_VARIANT1) {
    return
  }

  const agent = botData.agent

  // Ürün ID si alındı
  const productId = botData.agent_message.content.bot_data.product_id

  let messageData

  // Heloscope tarafıdan ürün bilgileri alnıyor.
  const getProductResponse = await HeloscopeService.getProduct(req.language, integration, chatIntegration, {
    product_id: productId,
    fetch_product_detail: true
  }, undefined, req.trace_id)

  if ( ! getProductResponse) {
    return
  }

  let firstVariantItem = botData.agent_message.vContent.buttons[0].rows.find(variant => variant.id == botData.customer_message.vContent.interactive[`${botData.message_type}`].id)

  if ( ! firstVariantItem) {
    return
  }

  if (getProductResponse.item.variant_count > 1) {

    const forSecondVariants = getVariantsByFirstVariantItem(getProductResponse.item, firstVariantItem)

    const selectVariant1Dto = new SelectVariant1Dto()

    selectVariant1Dto.setBotDataForWhatsapp({
      product_id: productId,
      variant_id: firstVariantItem.id,
      variant_name: firstVariantItem.title,
    })

    messageData = {
      message_type: enums.message_types.WHATSAPP_INTERACTIVE,
      message_data: {
        text: req.t('App.success.integration.whatsapp_add_to_cart_children', {
          children_title: getVariantTitle(getProductResponse.item),
          interpolation: {escapeValue: false}
        }),
        next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_VARIANT2,
        agent_id: agent._id,
        bot_data: selectVariant1Dto.getBotData(),
        bb_code: true,
        buttons: [{
          title: getProductResponse.item.variant_data.options[1].name,
          rows: getChildrenItemsMessage(getProductResponse.item, forSecondVariants, integration.vData)
        }],
        sub_type: enums.message_types.LIST
      },
      send_data_from_socket: false
    }

  } else {

    // Heloscope tarafında sepete ekleme işlemi yapıldı
    const response = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.ADD_TO_CART, enums.HELOSCOPE_ACTION_TYPES.USER,  {
      product_id: productId,
      variant_cid: firstVariantItem.id
    }, req.language, integration, chatIntegration, chatIntegration.ext_id)

    messageData = {
      message_type: enums.message_types.TEXT,
      message_data: {
        text: req.t('App.success.integration.add_to_cart', {
          item_name: response.item.item_name,
          interpolation: {escapeValue: false},
        })
      },
      send_data_from_socket: true,
      product_title: getProductResponse.item.title
    }

  }

  // Müşteriye variant seçimi için mesaj gönderiliyor.
  await ChatService.addAgentMessage(req, chat.id, messageData.message_type, messageData.message_data, agent._id, undefined, {mark_as_seen_event: true})

  // Ürün eğer tek variantlık ise sepete eklendi işlemlerinin yapılması için soketten bilgiler gönderliyor.
  if (messageData.send_data_from_socket) {

    // Müşterinin Stage bilgisi güncelleniyor
    const chatIntegrationData = chatIntegration.vData

    chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_ADD_TO_CART)

    chatIntegration.data = chatIntegrationData.getData()
    chatIntegration.markModified('data')

    await chatIntegration.save()

    // Soket üzerinden sepete ürün eklendiğine dair bilgi gönderiliyor
    QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.ITEM_ADDED_TO_CART,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_id: chat.id,
        integration_id: integration.id,
        item_name: messageData.product_title,
      }
    }, req.language)

    // Soket üzerinden müşteri durumu hakkında bilgi gönderiliyor
    QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_id: chat.id,
        integration_id: integration.id,
        stage: enums.ORDER_STAGES.STAGE_ADD_TO_CART
      }
    }, req.language)

  }

}
