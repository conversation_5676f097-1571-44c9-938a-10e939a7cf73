const enums = require('../../../../libs/enums')
const utils = require('../../../../libs/utils')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const Session = require('../../../../models/Session')
const CompletedOrder = require('../../../../models/CompletedOrder')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

const HeloscopeService = require('../../../Heloscope/HeloscopeService')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

   if ( ! botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.TSOFT_BOT_MESSAGE_ACTIONS.CREATE_ORDER) {
    return
  }

  // Agentın gönderdiği mesajda buton id si ile müşterinin butona bastığı id ile aynı mı kontrol ediliyor
  const selectedButton = botData.agent_message.vContent.buttons.find(item => item.reply.id === botData.customer_message.vContent.interactive[`${botData.message_type}`].id)

  if ( ! selectedButton) {
    return
  }

  const agent = botData.agent

  // Heloscope tarafında sipariş oluşturuluyor
  const createOrderResponse = await HeloscopeService.createOrder(req, req.language,chat, integration, chatIntegration, chatIntegration.ext_id)

  // Müşteri stage bilgisi güncelleniyor
  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_APPROVE_CART)

  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')

  await chatIntegration.save()

  // Soket üzerinden sipariş olduşturulduğuna dair bilgi gönderiliyor
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_CREATED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      order_code: createOrderResponse.order_code,
    }
  })

  // Soket üzerinden müşteri stage bilgsii değiştiğine dair bilgi gönderiliyor
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_APPROVE_CART
    }
  }, req.language)

  // Sipariş oluşturulduğna dair mesaj gönderiiliyor
  await ChatService.addAgentMessage(req, chat.id, createOrderResponse.message_type, createOrderResponse.message_data, agent.id)

  process.nextTick(async () => {

    const sessionDocs = await Session.find({
      chat_ext_id: chat.ext_id,
      channel_number: chat.channel.ext_id
    }).sort({_id:-1})

    if (!sessionDocs.length) {
      return
    }

    const cartData = await HeloscopeService.getCartProcess(req.language, integration, chatIntegration, chatIntegration.ext_id, req.trace_id)

    new CompletedOrder({
      channel_id: chat.channel._id,
      chat_id: chat._id,
      session_id: sessionDocs[0]._id,
      chat_ext_id: chat.ext_id,
      agent_id: agent._id,
      total_price: utils.getFormattedPrice(cartData.total_amount, integration.vData.getPricePrecision())
    }).save()
  })
}
