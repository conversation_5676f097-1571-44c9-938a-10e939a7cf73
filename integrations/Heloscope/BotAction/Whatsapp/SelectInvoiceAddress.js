const enums = require('../../../../libs/enums')

const QueueService = require('../../../../services/QueueService')
const ChatService = require('../../../../services/ChatService')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

const SendCargoOptions = require('./../SendCargoOptions')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if ( ! botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_INVOICE_ADDRESS) {
    return
  }

  const agent = botData.agent

  // Mesaj içerisinden hangi adresin seçilidğine bakılıyor
  const address = botData.agent_message.vContent.buttons[0].rows.find(item => {
    return item.id == botData.customer_message.vContent.interactive[`${botData.message_type}`].id
  })

  if ( ! address) {
    return
  }

  // Stage ve fatura adress bilgisi bizim tarafımıza kaydediliyor.
  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setInvoiceAddressId(address.id)
  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_SELECT_ADDRESS)

  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')

  const savedChatIntegration = await chatIntegration.save()

  // Fatura adresi bilgisi soket üzerinden veriliyor.
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.INVOICE_ADDRESS_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      invoice_address_id: address.id
    }
  }, req.language)

  // Soket üzerinden stage bilgisi gönderiliyor
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_SELECT_ADDRESS
    }
  }, req.language)

  // Karbo bilgileri heloscope tarafından alınıyor.
  const messageData = await SendCargoOptions(req, chat, integration, savedChatIntegration, savedChatIntegration.ext_id)

  // Müşteriye mesaj gönderiliyor
  return ChatService.addAgentMessage(req, chat.id, enums.message_types.WHATSAPP_INTERACTIVE, {
    text: req.t('App.integration.whatsapp_send_cargo_message'),
    next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_CARGO,
    agent_id: agent.id,
    language: req.language,
    bb_code: true,
    buttons: [{
      title: req.t('Global.chat_message.select_cargo'),
      rows: messageData.cargo_names_whatsapp
    }],
    sub_type: enums.message_types.LIST
  }, agent.id, undefined,{mark_as_seen_event: true})
}
