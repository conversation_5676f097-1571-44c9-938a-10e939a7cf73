const enums = require('../../../libs/enums')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')
const HeloscopeIntegrationService = require('../../../integrations/Heloscope/HeloscopeIntegrationService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const SelectCargoForWhatsapp = require('./Whatsapp/SelectCargo')
const SendPaymentOptions = require('./SendPaymentOptions')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return SelectCargoForWhatsapp(req, chat, integration, chatIntegration, chatIntegration.ext_id)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if ( ! botData) {
    return
  }

  const agent = botData.agent

  // mesaj içerisinden seçilen kargo bilgisi hangisi diye bakılıyor
  const cargo = botData.bot_data.cargos.find(item => {
    return item.cargo_index == botData.customer_message.vContentText
  })

  if ( ! cargo) {
    return
  }

  const cargoOptionId = cargo.cargo_option_id

  await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.SELECT_CARGO_OPTION, enums.HELOSCOPE_ACTION_TYPES.USER, {
    id: cargoOptionId
  }, req.language, integration, chatIntegration, chatIntegration.ext_id)

  // müşteri bot üzerinden kargo yöntemini seçti, seçtiği bu bilgileri ilgili kısma kaydedeceğiz
  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setCargoOptionId(cargoOptionId)
  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_SELECT_CARGO)

  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')

  await chatIntegration.save()

  // Soket üzerinden kargo seçildiğine dair mesaj gönderiliyor
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CARGO_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      cargo_option_id: cargoOptionId, // ek bilgi olarak paylaşıyoruz
    }
  }, req.language)

  // Soket üzerinden müşterinin stage bilgsini gönderiyoruz
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_SELECT_CARGO
    }
  }, req.language)

  // Heloscope tarafından ödeme yöntemleri alınıyor
  const messageData = await SendPaymentOptions(req, chat, integration, chatIntegration, agent.id)

  // Ödeme yöntemleri mesaj olarak gönderiliyor.
  return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
    text: req.t('App.success.integration.payment_type', {
      payment_options: messageData.message_data,
      interpolation: {escapeValue: false}
    }),
    bb_code: true,
    next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_PAYMENT_OPTION,
    agent_id: agent.id,
    language: req.language,
    bot_data: messageData.payment_dto
  }, agent.id, undefined, {mark_as_seen_event: true})
}
