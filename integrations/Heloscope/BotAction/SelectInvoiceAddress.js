const enums = require('../../../libs/enums')

const QueueService = require('../../../services/QueueService')
const ChatService = require('../../../services/ChatService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const SendCargoOptions = require('./SendCargoOptions')

const SelectInvoiceAddress = require('./Whatsapp/SelectInvoiceAddress')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return SelectInvoiceAddress(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if ( ! botData) {
    return
  }

  const agent = botData.agent

  // Mesaj içerisinden hangi adresin seçilidğine bakılıyor
  const address = botData.bot_data.addresses.find(item => {
    return item.address_index == botData.customer_message.vContentText
  })

  if ( ! address) {
    return
  }

  // Stage ve fatura adress bilgisi bizim tarafımıza kaydediliyor.
  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setInvoiceAddressId(address.address_id)
  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_SELECT_ADDRESS)

  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')

  const savedChatIntegration = await chatIntegration.save()

  // Fatura adresi bilgisi soket üzerinden veriliyor.
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.INVOICE_ADDRESS_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      invoice_address_id: address.address_id
    }
  }, req.language)

  // Soket üzerinden stage bilgisi gönderiliyor
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_SELECT_ADDRESS
    }
  }, req.language)

  // Karbo bilgileri helsocope tarafından alınıyor.
  const messageData = await SendCargoOptions(req, chat, integration, savedChatIntegration, savedChatIntegration.ext_id)

  // Müşteriye mesaj gönderiliyor
  return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
    text: req.t('App.integration.send_cargo_message', {
      cargo_options: messageData.cargo_names,
      interpolation: {escapeValue: false}
    }),
    next_action: enums.HELOSCOPE_BOT_MESSAGE_ACTIONS.SELECT_CARGO,
    agent_id: agent.id,
    language: req.language,
    bot_data: messageData.cargo_dto,
    bb_code: true,
  }, agent.id, undefined,{mark_as_seen_event: true})
}
