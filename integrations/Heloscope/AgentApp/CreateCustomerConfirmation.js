const createError = require('http-errors')
const enums = require('../../../libs/enums')

const HeloscopeService = require('../HeloscopeService')
const HeloscopeIntegrationService = require('../HeloscopeIntegrationService')

module.exports = async (req, chat, integration, chatIntegration, customerData) => {

  const responseFormFields = await HeloscopeService.getAddCustomerFormFields(req, chat, integration, chatIntegration, chatIntegration.ext_id)

  let cityName = '', townName = '', countryName = ''

  const getCountriesResponse = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_COUNTRIES, enums.HELOSCOPE_ACTION_TYPES.USER, {}, req.language, integration, chatIntegration, chatIntegration.ext_id)


  const foundCountry = getCountriesResponse.items.find(item => item.code == customerData.countryCode)


  if (foundCountry) {
    countryName = foundCountry.name || ''
  }
  // Heloscope tarafından Ülke koduna göre şehir bilgsi alınıyor
  const getCitiesResponse = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_CITIES, enums.HELOSCOPE_ACTION_TYPES.USER,
    { country_code: foundCountry.code }, req.language, integration, chatIntegration, chatIntegration.ext_id)

  const foundCity = getCitiesResponse.items.find(item => item.code == customerData.cityCode)

  if (foundCity) {
    cityName = foundCity.name
  }

  let customerMessage = []

  if (!('mailNotify' in customerData)) {
    customerData.IsEmailNotificationOn = 0
  }

  const getDistrictResponse = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_DISTRICTS, enums.HELOSCOPE_ACTION_TYPES.USER,
    { city_code: foundCity.code },
    req.language, integration, chatIntegration, chatIntegration.ext_id)

  const selectedDistrict = getDistrictResponse.find(item => item.code == customerData.districtCode)

  townName = selectedDistrict.name



  // Bütün form fieldlerini tek düzeye çıkarır.
  const formFields = responseFormFields.map(field => field.fields).flat()

  formFields.forEach(formField => {

    // Gelmesi Gereken zorunlu alanların kontrolü yapıldı
    if (formField.required) {

      if (typeof customerData[`${formField.field_name}`] === 'undefined') {
        throw new createError.BadRequest(req.t('App.errors.conversation.key_not_found'))
      }

    }

    const matchedField = Object.entries(customerData).find(([fieldName, value]) => formField.field_name == fieldName)

    if (!matchedField) {
      return
    }

    // formdan gelen bilgi
    let [fieldName, value] = matchedField

    switch (fieldName) {

      case 'mailNotify':
        if (value == 1) {
          value = req.t('App.integration.yes')
          break
        }
        value = req.t('App.integration.no')
        break
      case 'memberContract':
        if (value == 1) {
          value = req.t('App.integration.yes')
          break
        }
        value = req.t('App.integration.no')
        break
      case 'countryCode':
        if (countryName.length > 0)
          value = countryName
        break

      case 'password':
        return

      case 'cityCode':
        if (cityName.length > 0)
          value = cityName
        break

      case 'districtCode':
        if (townName.length > 0)
          value = townName
        break

      case 'gender':
        if (value?.toString()?.length > 0)
          value = formField.options.find(item => item.code == value).name
        break

      case 'birthDate':
        if (value == 0)
          return
      case 'birthMonth':
        if (value == 0)
          return
      case 'birthYear':
        if (value == 0)
          return

    }

    customerMessage.push('[B]' + formField.title + ':[/B] ' + value + '[BR][/BR]')

  })

  return customerMessage = customerMessage.join('')
}   