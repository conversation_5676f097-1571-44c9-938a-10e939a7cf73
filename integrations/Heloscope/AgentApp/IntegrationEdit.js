const createError = require('http-errors')

const { isTrue } = require('../../../libs/helpers')

module.exports = async (req, integration) => {

    const integrationData = integration.vData


    if (req.method === 'POST') {

        const gdprUrl = req.body.gdpr_url
        const termsOfUseUrl = req.body.terms_of_use_url
        const requestConfirmationForCreateCustomer = req.body.request_confirmation_for_create_customer
        const pricePrecision = req.body.price_precision
        const showIsNew = req.body.show_is_new
        const perpage = req.body.perpage

        if (!perpage) {
            throw new createError.BadRequest(req.t('App.errors.integration.perpage_not_found'))
        }

        if (parseInt(perpage) > 10 || parseInt(perpage) <= 0) {
            throw new createError.BadRequest(req.t('App.errors.integration.perpage_wrong_value'))
        }

        integrationData.setPerPage(parseInt(perpage))


        if (gdprUrl) {
            integrationData.setGdprUrl(gdprUrl)
        }

        if (typeof showIsNew !== 'undefined') {
            integrationData.setShowIsNew(isTrue(showIsNew))
        }


        if (termsOfUseUrl) {
            integrationData.setTermsOfUseUrl(termsOfUseUrl)
        }

        integrationData.setRequestConfirmationForCreateCustomer(requestConfirmationForCreateCustomer)

        if (parseInt(pricePrecision) >= 0) {
            integrationData.setPricePrecision(parseInt(pricePrecision))
        }

        integration.data = integrationData.getData()
        integration.markModified('data')

        await integration.save()

        return { success: true }

    }

    const fields = {
        terms_of_use_url: integrationData.getTermsOfUseUrl(),
        gdpr_url: integrationData.getGdprUrl(),
        request_confirmation_for_create_customer: integrationData.getRequestConfirmationForCreateCustomer(),
        price_precision: integrationData.getPricePrecision(),
        perpage: integrationData.getPerPage(),
        show_is_new: integrationData.getShowIsNew()
    }

    return { fields: fields }
}