const moment = require('moment');
const { default: axios } = require('axios');

const enums = require('../../libs/enums')
const utils = require('../../libs/utils')

const User = require('../../models/User')
const ChatIntegration = require('../../models/ChatIntegration')

const HeloscopeIntegrationService = require('./HeloscopeIntegrationService')

const HeloscopeService = {

  getCartProcess: (language, integration, chatIntegration, extId, traceId) => {
    return HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_CART, enums.HELOSCOPE_ACTION_TYPES.USER, {}, language, integration, chatIntegration, extId, traceId)
  },

  getCart: async (language, integration, chatIntegration, extId, traceId) => {

    const integrationData = integration.vData

    const response = {
      addresses: [],
      cargo_options: [],
      payment_options: [],
      currency_code: 'TRY',
      cart: {
        order_note: "",
        cargo_price: "",
      },
      selected_delivery_address: {},
      selected_invoice_address: {},
      payment_option_id: chatIntegration.vData.getHeloscopePaymentOptionId(),
      cargo_option_id: chatIntegration.vData.getCargoOptionId()
    }

    const cartResponse = await HeloscopeService.getCartProcess(language, integration, chatIntegration, extId, traceId)

    cartResponse.items = cartResponse.items.map(item => {
      return {
        amount: utils.getFormattedPrice(item.amount, integrationData.getPricePrecision()),
        count: item.count,
        discounted: item.discounted,
        id: item.id,
        title: item.title,
        stock_increment: 1,
        image_url: item.image_url,
        list_price: utils.getFormattedPrice(item.list_price, integrationData.getPricePrecision()),
        sell_price: utils.getFormattedPrice(item.sell_price, integrationData.getPricePrecision()),
        subtitle: item.subtitle,
        variant_id: item.variant_id,
        variant_name: item.variant_name,
        min_order_count: 1
      }
    })

    cartResponse.sub_total = utils.getFormattedPrice(cartResponse.sub_total, integrationData.getPricePrecision())
    cartResponse.total_amount = utils.getFormattedPrice(cartResponse.total_amount, integrationData.getPricePrecision())

    response.cart = cartResponse

    response.currency_code = cartResponse.currency_code

    const addresses = await HeloscopeService.getAddresses(language, integration, chatIntegration, extId, traceId)

    response.addresses = addresses

    if (addresses.length > 0 && chatIntegration.vData.getDeliveryAddressId()?.toString() && chatIntegration.vData.getInvoiceAddressId().toString()) {
      response.selected_delivery_address = addresses.find(item => item.id == chatIntegration.vData.getDeliveryAddressId())
      response.selected_invoice_address = addresses.find(item => item.id == chatIntegration.vData.getInvoiceAddressId())
    }

    const cargoOptions = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_CARGO_OPTIONS, enums.HELOSCOPE_ACTION_TYPES.USER, {}, language, integration, chatIntegration, extId, traceId)

    response.cargo_options = cargoOptions.items

    const paymentOptions = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_PAYMENT_OPTIONS, enums.HELOSCOPE_ACTION_TYPES.USER, {}, language, integration, chatIntegration, extId, traceId)


    paymentOptions.items.forEach(item => {

      if (item.options.length > 0) {

        item.options.forEach(child => {

          response.payment_options.push({
            id: item.id + ' / ' + child.id,
            name: item.name + ' / ' + child.name
          })

        })

      } else {
        response.payment_options.push({
          id: item.id,
          name: item.name
        })
      }

    })

    const selectedCargo = cargoOptions.items?.find(item => item.id == chatIntegration.vData.getCargoOptionId())

    if (selectedCargo) {
      response.cart.cargo_price = selectedCargo.price
    }

    response.cart.order_note = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_ORDER_NOTE, enums.HELOSCOPE_ACTION_TYPES.USER, {}, language, integration, chatIntegration, extId, traceId)


    return response

  },

  getAddresses: async (language, integration, chatIntegration, extId, traceId) => {

    if (extId) {

      const addresses = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_ADDRESSES, enums.HELOSCOPE_ACTION_TYPES.USER, {}, language, integration, chatIntegration, extId, traceId)

      return addresses.items

    }

    const chatGuestAdresses = chatIntegration.vData.getGuestAddresses()
    const adresses = []

    chatGuestAdresses.forEach(element => {
      adresses.push({
        id: element.id.toString(),
        title: element.data.title,
        content: element.data.address,
        phone: element.data.mobile_phone
      })
    })

    return adresses

  },

  getAddAddressFormFields: async (req, language, integration, chatIntegration, extId) => {

    const countries = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_COUNTRIES, enums.HELOSCOPE_ACTION_TYPES.USER, {}, language, integration, chatIntegration, extId, req.trace_id)
    const cities = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_CITIES, enums.HELOSCOPE_ACTION_TYPES.USER, { country_code: 'TR' }, language, integration, chatIntegration, extId, req.trace_id)

    return {
      fields: [

        {
          "title": req.t('Global.form_field.address_type'),
          "field_type": "select",
          "field_name": "isCompanyActive",
          "field_key": "isCompanyActive",
          "hide": false,
          "required": true,
          "options": [
            {
              "key": 0,
              "value": req.t('Global.form_field.individual'),
            },
            {
              "key": 1,
              "value": req.t('Global.form_field.institutional'),
            }
          ],
          "default": 0
        },
        {
          "title": req.t('Global.form_field.address_title'),
          "field_type": "text",
          "field_name": "title",
          "field_key": "title",
          "hide": false,
          "options": [],
          "required": true,
          "default": ""
        },
        {
          "title": req.t('Global.form_field.fullname'),
          "field_type": "text",
          "field_name": "full_name",
          "field_key": "fullName",
          "hide": false,
          "options": [],
          "required": true,
          "default": ""
        },
        {
          "title": req.t('Global.form_field.company'),
          "field_type": "text",
          "field_name": "company",
          "field_key": "company",
          "hide": false,
          "options": [],
          "required": true,
          "default": ""
        },
        {
          "title": req.t('Global.form_field.tax_administration'),
          "field_type": "text",
          "field_name": "tax_office",
          "field_key": "taxOffice",
          "hide": false,
          "options": [],
          "required": true,
          "default": ""
        },
        {
          "title": req.t('Global.form_field.tax_number'),
          "field_type": "text",
          "field_name": "tax_number",
          "field_key": "taxNumber",
          "hide": false,
          "options": [],
          "required": true,
          "default": ""
        },
        {
          "title": req.t('Global.form_field.identity_number'),
          "field_type": "text",
          "field_name": "identity_number",
          "field_key": "identityNumber",
          "hide": false,
          "options": [],
          "required": true,
          "default": ""
        },
        {
          "title": req.t('Global.form_field.nationality'),
          "field_type": "checkbox",
          "field_name": "nationality",
          "field_key": "nationality",
          "hide": false,
          "options": [],
          "required": true,
          "default": "0"
        },
        {
          "title": req.t('Global.form_field.address'),
          "field_type": "textarea",
          "field_name": "address",
          "field_key": "address",
          "hide": false,
          "options": [],
          "required": true,
          "default": ""
        },
        {
          "title": req.t('Global.form_field.post_code'),
          "field_type": "text",
          "field_name": "post_code",
          "field_key": "postCode",
          "hide": false,
          "options": [],
          "required": false,
          "default": ""
        },
        {
          "title": req.t('Global.form_field.phone'),
          "field_type": "text",
          "field_name": "phone",
          "field_key": "phone",
          "hide": false,
          "options": [],
          "required": true,
          "default": ""
        },
        {
          "title": req.t('Global.form_field.country'),
          "field_type": "select",
          "field_name": "country_code",
          "field_key": "countryCode",
          "hide": false,
          "required": true,
          "default": "TR"
        },
        {
          "title": req.t('Global.form_field.city'),
          "field_type": "select",
          "field_name": "city_code",
          "field_key": "cityCode",
          "hide": false,
          "options": [],
          "required": true,
          "default": ""
        },
        {
          "title": req.t('Global.form_field.district'),
          "field_type": "select",
          "field_name": "district_code",
          "field_key": "districtCode",
          "hide": false,
          "options": [],
          "required": true,
          "default": ""
        }
      ],
      countries: countries.items,
      cities: cities.items,
    }

  },

  addCustomerAddress: async (language, integration, chatIntegration, data, extId, traceId) => {

    if (extId) {
      return HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.ADD_CUSTOMER_ADDRESS, enums.HELOSCOPE_ACTION_TYPES.USER, data.data, language, integration, chatIntegration, extId, traceId)
    }

    const chatIntegrationData = chatIntegration.vData

    chatIntegrationData.addGuestAddress(data)

    chatIntegration.data = chatIntegrationData.getData()
    chatIntegration.markModified('data')

    return chatIntegration.save()

  },


  getProduct: async (language, integration, chatIntegration, data, extId, traceId) => {
    const product = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_PRODUCT, enums.HELOSCOPE_ACTION_TYPES.USER,
      {
        product_id: data.product_id
      }, language, integration, chatIntegration, extId, traceId)

    return product

  },

  getAddCustomerFormFields: async (req, chat, integration, chatIntegration, extId) => {

    const countries = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_COUNTRIES, enums.HELOSCOPE_ACTION_TYPES.USER, {}, req.language, integration, chatIntegration, extId, req.trace_id)
    const cities = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_CITIES, enums.HELOSCOPE_ACTION_TYPES.USER, { country_code: 'TR' }, req.language, integration, chatIntegration, extId, req.trace_id)


    const fields = [
      {
        title: req.t('Global.form_field.account_info'),
        fields: [
          {
            title: req.t('Global.form_field.first_name'),
            field_type: 'text',
            field_key: 'name',
            field_name: 'name',
            required: true,
            default: chat.title
          },
          {
            title: req.t('Global.form_field.last_name'),
            field_type: 'text',
            field_key: 'surname',
            field_name: 'surname',
            required: true,
            default: ''
          },
          {
            title: req.t('Global.form_field.email'),
            field_type: 'text',
            field_key: 'email',
            field_name: 'email',
            required: true,
            default: chat.email || ''
          },
          {
            title: req.t('Global.form_field.password'),
            field_type: 'text',
            field_key: 'password',
            field_name: 'password',
            required: true,
            default: ''
          },
          {
            title: req.t('Global.form_field.phone'),
            field_type: 'text',
            field_key: 'phone',
            field_name: 'phone',
            required: true,
            default: chat.phone_number || ''
          },

          {
            title: req.t('Global.form_field.gender'),
            field_type: 'select',
            field_key: 'gender',
            field_name: 'gender',
            required: true,
            default: "",
            options: [
              {
                name: req.t('Global.form_field.woman'),
                code: 1
              },
              {
                name: req.t('Global.form_field.man'),
                code: 0
              }
            ]
          },
          {
            title: req.t('Global.form_field.birth_date'),
            field_type: 'text',
            field_key: 'birthDate',
            field_name: 'birthDate',
            required: false,
            default: 0
          },
          {
            title: req.t('Global.form_field.birth_month'),
            field_type: 'text',
            field_key: 'birthMonth',
            field_name: 'birthMonth',
            required: false,
            default: 0
          },
          {
            title: req.t('Global.form_field.birth_year'),
            field_type: 'text',
            field_key: 'birthYear',
            field_name: 'birthYear',
            required: false,
            default: 0
          },
          {
            title: req.t('Global.form_field.country'),
            field_type: 'select',
            field_key: 'countryCode',
            field_name: 'countryCode',
            required: true,
            default: 'TR',
            options: countries.items
          },
          {
            title: req.t('Global.form_field.city'),
            field_type: 'select',
            field_key: 'cityCode',
            field_name: 'cityCode',
            required: true,
            options: cities.items
          },
          {
            title: req.t('Global.form_field.district'),
            field_type: 'select',
            field_key: 'districtCode',
            field_name: 'districtCode',
            required: true,
            options: [],
            default: ""
          },
          {
            title: req.t('Global.form_field.address'),
            field_type: 'text',
            field_key: 'address',
            field_name: 'address',
            required: true,
            default: ""
          },
          {
            title: req.t('Global.form_field.mail_notify'),
            field_type: 'select',
            field_key: 'mailNotify',
            field_name: 'mailNotify',
            required: true,
            options: [
              {
                name: req.t('Global.form_field.no'),
                code: 0,
              },
              {
                name: req.t('Global.form_field.yes'),
                code: 1
              }
            ]
          },
          {
            title: req.t('Global.form_field.member_contract'),
            field_type: 'select',
            field_key: 'memberContract',
            field_name: 'memberContract',
            required: true,
            options: [
              {
                name: req.t('Global.form_field.no'),
                code: 0,
              },
              {
                name: req.t('Global.form_field.yes'),
                code: 1
              }
            ]
          }
        ]
      },
    ]

    return fields

  },

  createCustomer: async (data, chat, language, integration, chatIntegration, traceId) => {

    const response = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.REGISTER, enums.HELOSCOPE_ACTION_TYPES.USER, data, language, integration, chatIntegration, undefined, traceId)

    chat.first_name = data.name
    chat.last_name = data.surname
    chat.email = data.email
    await chat.save()

    chatIntegration.ext_id = response.customer.id
    chatIntegration.membership_date = Date.now()

    let chatIntegrationData = chatIntegration.vData
    chatIntegrationData.setPhoneNumber(data.phone)
    chatIntegrationData.setToken({
      token: response.token.bearer,
      expires_at: moment.unix(response.token.exp_at).toString()
    })


    for (let address of chatIntegrationData.getGuestAddresses()) {
      await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.ADD_CUSTOMER_ADDRESS, enums.HELOSCOPE_ACTION_TYPES.USER, address.data, language, integration, chatIntegration, chatIntegration.ext_id, traceId)
    }

    chatIntegrationData.removeGuestAddresses()
    chatIntegration.data = chatIntegrationData.getData()
    chatIntegration.markModified('token')
    chatIntegration.markModified('data')

    chatIntegration.save()

    return user
  },

  createOrder: async (req, chat, integration, chatIntegration, extId) => {

    let data

    const chatIntegrationData = chatIntegration.vData

    if (extId) {

      data = {
        delivery: chatIntegrationData.getDeliveryAddressId(),
        invoice: chatIntegrationData.getInvoiceAddressId(),
      }

    } else {

      const invoiceAddressInfo = chatIntegrationData.getGuestAddressById(chatIntegrationData.getInvoiceAddressId())

      const deliveryAddressInfo = chatIntegrationData.getGuestAddressById(chatIntegrationData.getDeliveryAddressId())

      data = {
        name: chat.title,
        surname: chat.last_name,
        email: chat.email,
        phone: chat.phone_number,
        delivery: {
          title: deliveryAddressInfo.data.title,
          isCompanyActive: deliveryAddressInfo.data.isCompanyActive,
          fullName: deliveryAddressInfo.data.fullName,
          nationality: deliveryAddressInfo.data.nationality,
          company: deliveryAddressInfo.data.company,
          tcNo: deliveryAddressInfo.data.identityNumber,
          country: deliveryAddressInfo.data.countryCode,
          city: deliveryAddressInfo.data.cityCode,
          district: deliveryAddressInfo.data.districtCode,
          address: deliveryAddressInfo.data.address,
          postCode: deliveryAddressInfo.data.postCode,
          phone: deliveryAddressInfo.data.phone,
          taxOffice: deliveryAddressInfo.data.taxOffice,
          taxNumber: deliveryAddressInfo.data.taxNumber
        },
        invoice: {
          title: invoiceAddressInfo.data.title,
          isCompanyActive: invoiceAddressInfo.data.isCompanyActive,
          fullName: invoiceAddressInfo.data.fullName,
          nationality: invoiceAddressInfo.data.nationality,
          tcNo: invoiceAddressInfo.data.identityNumber,
          company: invoiceAddressInfo.data.company,
          country: invoiceAddressInfo.data.countryCode,
          city: invoiceAddressInfo.data.cityCode,
          district: invoiceAddressInfo.data.districtCode,
          address: invoiceAddressInfo.data.address,
          postCode: invoiceAddressInfo.data.postCode,
          phone: invoiceAddressInfo.data.phone,
          taxOffice: invoiceAddressInfo.data.taxOffice,
          taxNumber: invoiceAddressInfo.data.taxNumber
        },
      }
    }

    return HeloscopeService.createOrde2r(req, chat, integration, chatIntegration, enums.HELOSCOPE_ACTIONS.COMPLETE_ORDER, data, chat.owner_user_id, extId)
  },

  createOrde2r: async (req, chat, integration, chatIntegration, action, orderData, agentId, extId) => {

    const ChatService = require('../../services/ChatService')

    const agent = await User.findById(agentId)

    const createOrderResponse = await HeloscopeIntegrationService.process(action, enums.HELOSCOPE_ACTION_TYPES.USER, orderData, req.language, integration, chatIntegration, extId, req.trace_id)

    const chatIntegrationData = chatIntegration.vData
    const agentData = agent.vData

    chatIntegrationData.setProductShareCounter(1)

    // order başarılı bir şekilde oluşturuldu, new order data bilgisi temizlenebilir
    chatIntegrationData.setNewOrderData({})

    chatIntegration.last_order_code = createOrderResponse.order_code

    chatIntegration.data = chatIntegrationData.getData()

    chatIntegration.markModified('data')

    agentData.setCompletedOrderCount(agentData.getCompletedOrderCount() + 1)
    agent.data = agentData.getData()

    await agent.save()

    await chatIntegration.save()

    await ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
      text: req.t('App.integration.send_heloscope_order_message')
    }, chat.owner_user_id, undefined, { mark_as_seen_event: true })

    return {
      order_code: createOrderResponse.order_code
    }
  },

  // kullanılmıyor
  checkUser: async (language, chat, integration, phoneNumber) => {

    const chatIntegration = await ChatIntegration.findOne({ chat_id: chat._id, integration_id: integration._id })

    const response = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_CUSTOMERS, enums.HELOSCOPE_ACTIONS.ADMIN,
      {
        phone_number: phoneNumber
      }, language, integration, chatIntegration)


    if (response.data.data === null) {

      console.log('Heloscope checkUser kayıt gelmedi.')
      return false

    }

  },

  getLangs: async (language, integration, chatIntegration, extId, traceId) => {

    const langs = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_LANGS, enums.HELOSCOPE_ACTION_TYPES.USER, {}, language, integration, chatIntegration, extId, traceId)


    if (langs) {
      return langs.items.map(item => {
        return {
          name: item.title,
          short_name: item.key,
          code: utils.getLangCode(item.key),
          short_code: item.key,
          for_app: true,
          selected: item.selected
        }
      })
    }

    return []
  },

  getCurrencies: async (language, integration, chatIntegration, extId, traceId) => {

    const currencies = await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.GET_CURRENCIES, enums.HELOSCOPE_ACTION_TYPES.USER, {}, language, integration, chatIntegration, extId, traceId)

    return currencies.data.map(currency => {
      return {
        name: currency.title,
        code: currency.shortcode,
        selected: currency.selected === 1
      }
    })
  },

  emptyCart: async (req, integration, chat, chatIntegration, agentId, extId) => {

    const ChatService = require('../../services/ChatService')

    await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.DESTROY_CART, enums.HELOSCOPE_ACTION_TYPES.USER, {}, req.language, integration, chatIntegration, extId, req.trace_id)

    // kullanıcıya mesaj gönderimi yapacağız
    await ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
      text: req.t('App.success.integration.empty_cart')
    }, agentId, undefined, { mark_as_seen_event: true })

  },

  getAdminCurrencies: async (integration) => {

    const config = {
      url: `${integration.data.base_url}/v1/currency`,
      method: 'GET'
    }

    const currencies = await axios.request(config).then(response => response.data)

    return currencies?.data?.map(currency => {
      return {
        id: currency.key,
        currency: currency.shortcode,
        default: currency.selected === 1
      }
    }) || []
  },

  setCurrencyCode: async (req, integration, chatIntegration, extId, data) => {
    return await HeloscopeIntegrationService.process(enums.HELOSCOPE_ACTIONS.SET_CURRENCY_CODE, enums.HELOSCOPE_ACTION_TYPES.USER, data, req.language, integration, chatIntegration, extId, req.trace_id)
  }

}

module.exports = HeloscopeService
