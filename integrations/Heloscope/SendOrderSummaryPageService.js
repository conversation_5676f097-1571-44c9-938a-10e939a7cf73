const enums = require('../../libs/enums')
const utils = require('../../libs/utils')
const helpers = require('../../libs/helpers')

const OrderProcessPageService = require('../../services/OrderProcessPageService')

class SendOrderSummaryMessagePageService extends OrderProcessPageService {

  async getMessageValues(user) {

    const generalData = {
      username: helpers.getName(user),
      cart_content: await this.getCartContent(),
      total_amount: utils.getCurrencyForIntlNumberFormat(await this.calcTotalAmount(), await this.getCurrencyCode(), this.getIntegrationData().getPricePrecision()),
      remittance_discount: await this.__getRemittanceDiscountAmountInfo(),
      additional_cost: await this.__getAdditionalCostMessage(),
      cargo_option_name: await this.__getCargoOptionName(),
      cargo_option_fee: utils.getCurrencyForIntlNumberFormat(await this.getCargoPrice(), await this.getCurrencyCode(), this.getIntegrationData().getPricePrecision()),
      payment_type: await this.__getPaymentOptionName()
    }

    const address = await this.__getAddressesDataForMessage()

    return { ...generalData, ...address }
  }

  __getOrderNote() {

    return this.getOrderNote() ? this.req.t('App.integration.general_order_note', {
      general_order_note: this.getOrderNote()
    }) : ' '

  }

  /**
   * @return {Promise<string>}
   *
   * @private
   */
  __getRemittanceDiscountAmountInfo() {

    return this.getRemittanceDiscountAmount().then(async remittanceDiscountAmount => {

      if (remittanceDiscountAmount > 0 && this.isPaymentOptionRemittance()) {

        const remittanceDiscountName = await this.getRemittanceDiscountName()
        const remittanceDiscountAmountContent = utils.getCurrencyForIntlNumberFormat(remittanceDiscountAmount, await this.getCurrencyCode(), this.getIntegrationData().getPricePrecision())

        return `[B]${remittanceDiscountName}:[/B][SPACE]5[/SPACE]${remittanceDiscountAmountContent}[BR][/BR][BR][/BR]`

      }

      return ''

    })

  }

  /**
   * @return {string}
   *
   * @private
   */
  async __getAdditionalCostMessage() {

    const additionalCostFee = await this.getAdditionalCostFee()

    if (additionalCostFee > 0) {

      const additionalCostName = await this.getAdditionalCostName()

      return `[B]${additionalCostName}:[/B][SPACE]5[/SPACE]${utils.getCurrencyForIntlNumberFormat(additionalCostFee, await this.getCurrencyCode(), this.getIntegrationData().getPricePrecision())}[BR][/BR][BR][/BR]`
    }

    return ''

  }

  /**
   * @return {Promise<string>}
   *
   * @private
   */
  __getCargoOptionName() {

    return this.getCargoOptionItems().then(items => {

      const cargoOption = items.find(item => item.id == this.chatIntegrationData.getCargoOptionId())

      if (cargoOption) {
        return cargoOption.name
      }

      // @todo log

      return ''

    })

  }

  __getRemittanceMessage(ibanData) {

    return this.req.t('App.integration.remittance_message', { iban: ibanData.name + '[BR][/BR]', interpolation: { escapeValue: false } })

  }

  __getPaymentOptionName() {

    return this.getCargoOptionItems().then(items => {

      for (const item of items) {

        const paymentOption = item.payment_options.find(item => item.id == this.getChatIntegrationData().getHeloscopePaymentOptionId())

        if (typeof paymentOption === 'undefined') {
          continue
        }

        if (paymentOption.childs.length > 0) {

          let paymentName = paymentOption.name

          const child = paymentOption.childs.find(item => item.id == this.getChatIntegrationData().getSubPaymentOptionId())

          // Havale Yöntemi seçildiyse ibanlar gönderilecek
          if (paymentOption.id == -1) {

            paymentName += this.__getRemittanceMessage(child)

          } else {

            paymentName += ' / ' + child.name

          }

          return paymentName

        } else {

          return paymentOption.name

        }

      }

      return ''

    })

  }

  __getAddressesDataForMessage() {

    if (this.extId) {

      return this.req.app.services.TsoftIntegrationService.process(this.req, this.integration, this.chatIntegration, enums.TSOFT_ACTIONS.GET_CUSTOMER_ADDRESS, {
        address_id: this.getChatIntegrationData().getDeliveryAddressId()
      }, this.extId).then(getCustomerDeliveryAddressResponse => {

        const deliveryAddressData = getCustomerDeliveryAddressResponse.data.data[0]

        return this.req.app.services.TsoftIntegrationService.process(this.req, this.integration, this.chatIntegration, enums.TSOFT_ACTIONS.GET_CUSTOMER_ADDRESS, {
          address_id: this.getChatIntegrationData().getInvoiceAddressId()
        }, this.extId).then(getCustomerInvioceAddressResponse => {

          const invoiceAddressData = getCustomerInvioceAddressResponse.data.data[0]

          return {
            delivery_address: deliveryAddressData.address,
            delivery_district: deliveryAddressData.town,
            delivery_city: deliveryAddressData.city,
            invoice_address: invoiceAddressData.address,
            invoice_district: invoiceAddressData.town,
            invoice_city: invoiceAddressData.city
          }

        })

      })

    }

    return {
      delivery_address: this.getChatIntegrationData().getGuestAddressById(this.getChatIntegrationData().getDeliveryAddressId()).data.address,
      delivery_district: this.getChatIntegrationData().getGuestAddressById(this.getChatIntegrationData().getDeliveryAddressId()).extra.town_name,
      delivery_city: this.getChatIntegrationData().getGuestAddressById(this.getChatIntegrationData().getDeliveryAddressId()).extra.city_name,
      invoice_address: this.getChatIntegrationData().getGuestAddressById(this.getChatIntegrationData().getInvoiceAddressId()).data.address,
      invoice_district: this.getChatIntegrationData().getGuestAddressById(this.getChatIntegrationData().getInvoiceAddressId()).extra.town_name,
      invoice_city: this.getChatIntegrationData().getGuestAddressById(this.getChatIntegrationData().getInvoiceAddressId()).extra.city_name,
    }

  }

}


module.exports = SendOrderSummaryMessagePageService
