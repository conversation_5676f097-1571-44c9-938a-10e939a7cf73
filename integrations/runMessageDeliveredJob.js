const enums = require('../libs/enums')

const DashPresenter = require('../presenters/Dash')

const Channel = require('../models/Channel')
const Chat = require('../models/Chat')
const Message = require('../models/Message')
const User = require('../models/User')

const QueueService = require('../services/QueueService')

/**
 * @param req
 * @param channelType
 * @param channelProvider
 * @param channelExtId
 * @param messageExtId
 * @param chatExtId
 *
 * @return {Promise<void>}
 */
module.exports = async (req, channelType, channelProvider, channelExtId, chatExtId, messageExtId) => {

  const channel = await Channel.findOne({
    type: channelType,
    provider: channelProvider,
    ext_id: channelExtId,
    is_active: true,
    deleted_at: {
      $exists: false
    }
  })

  if (!channel) {
    return
  }

  const chat = await Chat.findOne({
    channel_id: channel._id,
    ext_id: chatExtId,
  })

  if (!chat) {
    return
  }

  const agent = await User.findById(chat.owner_user_id)

  if (Array.isArray(messageExtId)) {
    if (messageExtId.length === 0) {
      return
    }

    const messages = await Message.find({
      conversation_id: chat._id,
      ext_id: {
        $in: messageExtId
      }
    }).populate('conversation_id').populate('user_id')

    for (const item of messages) {
      item.delivered = true
      item.status = enums.message_send_statuses.DELIVERED

      await item.save()

      // chat için agent varsa bildirim göndereceğiz
      if (chat.owner_user_id) {

        await QueueService.publishToAppSocket({
          event: enums.agent_app_socket_events.MESSAGE_DELIVERED,
          socket_rooms: [agent.vSocketCode],
          data: {
            conversation_id: chat.id,
            message_item: await DashPresenter.getRepliedMessage(item),
          }
        }, req.language)

      }
    }
  } else {
    const message = await Message.findOne({
      conversation_id: chat._id,
      ext_id: messageExtId
    }).populate('conversation_id').populate('user_id')

    if (!message) {
      return
    }

    message.delivered = true
    message.status = enums.message_send_statuses.DELIVERED

    await message.save()

    // chat için agent varsa bildirim göndereceğiz
    if (chat.owner_user_id) {

      await QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.MESSAGE_DELIVERED,
        socket_rooms: [agent.vSocketCode],
        data: {
          conversation_id: chat.id,
          message_item: await DashPresenter.getRepliedMessage(message),
        }
      }, req.language)

    }

    return message
  }

}
