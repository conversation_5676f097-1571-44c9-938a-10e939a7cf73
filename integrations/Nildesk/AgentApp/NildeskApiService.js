const { default: axios } = require('axios')
const pino = require('pino')()

const NildeskApiService = {
  getIframe: async (user, traceId) => {
    const config = {
      data: `rest[api_key]=${process.env.NILDESK_API_KEY}&rest[secret_key]=${process.env.NILDESK_SECRET_KEY}&user[email]=${user.email}&user[name]=${user.name}&user[surname]=${user.surname}&user[phone]=${user.phone}&domain=${user.domain}`,
      url: `${process.env.NILDESK_BASE_URL}/rest/user/getToken`,
      method: 'POST',
    }

    const { data } = await axios.request(config).catch(error => {

      pino.error({
        trace_id: traceId,
        error: JSON.stringify(error.response?.data || { message: 'beklenmedik bir hata o<PERSON>' }),
        timestamp: new Date(),
        message: 'NildeskApiService::getIframe',
        agent_id: user.user_id.toString()
      })
      throw 'Nildesk: Cannot Take Iframe'
    })

    return data
  }
}

module.exports = NildeskApiService
