const pino = require('pino')()

const enums = require('../../libs/enums')
const helpers = require('../../libs/helpers')

const FacebookService = require('./FacebookService')

const IncomingMessageJobResultDto = require('./../../dtos/IncomingMessageJobResultDto')

const Chat = require('../../models/Chat')
const User = require('../../models/User')
const Message = require('../../models/Message')
const CompanyHasPackage = require('../../models/CompanyHasPackage')
const ChatReferral = require('../../models/ChatReferral')

const ChannelRepo = require('./../../repos/ChannelRepo')
const MessageRepo = require('./../../repos/MessageRepo')

const ChatService = require('./../../services/ChatService')
const MetaEventService = require('./../../services/MetaEventService')

const CreatedChatMessage = require('../../services/Chat/CreatedChatMessage')

const IntegrationService = require('../../modules/AgentApp/IntegrationService')

const FacebookApiService = require('./../../integrations/Facebook/FacebookApiService')

async function SaveReferralClId(messageObject, chat, channel, messageId, traceId) {
  let referral;
  if (messageObject.type === enums.message_types.ADS_OPEN_THREAD || messageObject.type === enums.message_types.ONLY_ADS_OPEN_THREAD) {
    referral = await ChatReferral.findOne({
      chat_id: chat._id,
      channel_id: channel._id,
      ad_id: messageObject.content.referral.ad_id,
      deleted_at: {
        $exists: false
      }
    })
    if (!referral) {
      referral = await new ChatReferral({
        chat_id: chat._id,
        channel_id: channel._id,
        ad_id: messageObject.content.referral.ad_id,
        message_id: messageId
      }).save()

      MetaEventService.SendEvent(channel, chat, enums.data_set_events.LeadSubmitted, traceId)
    }

    chat.chat_referral_id = referral._id
    await chat.save()
  }
}

/**
 * @param {FacebookMessageDto} dto
 *
 * @return {Promise<IncomingMessageJobResultDto>}
 */
module.exports = async (req, dto) => {

  const incomingMessageJobResultDto = new IncomingMessageJobResultDto()

  const channel = await ChannelRepo.getChannel(enums.channel_types.FACEBOOK_PAGE, enums.channel_providers.FACEBOOK, dto.getId())

  if (!channel) {
    throw new Error('FacebookMessagingItemJob channel not found: ' + dto.getId())
  }

  const companyHasPackage = await CompanyHasPackage.findOne({
    company_id: channel.company_id,
    deleted_at: {
      $exists: false
    }
  }).sort({ _id: 1 })
  if (companyHasPackage && companyHasPackage.data.facebook) {
    if (helpers.isModuleTimeOut(companyHasPackage.data.facebook) !== false) {
      channel.is_active = false
      await channel.save()

      pino.info({
        trace_id: req.trace_id,
        timestamp: new Date(),
        message: 'FacebookMessagingItemJob channel süresi dolduğu için pasif edildi ve mesaj işlenmedi',
        data: JSON.stringify({
          channel_id: channel.id,
          channel_ext_id: channel.ext_id
        })
      })

      throw new Error('FacebookMessagingItemJob channel süresi dolduğu için pasif edildi ve mesaj işlenmedi')
    }
  }

  const messageObject = await FacebookService.getMessageObject(dto)

  if (dto.getItem().getMessageId()) {
    const isMessage = await Message.findOne({
      channel_id: channel._id,
      ext_id: dto.getItem().getMessageId()
    })
    if (isMessage) {
      throw new Error('Bu Mesaj Zaten Kaydedilmiş')
    }
  }

  let chat = null
  let user = null
  // instagram üzerinden mesaj atılmış demektir. mesajı agent atmış.
  if (dto.getId() !== dto.getItem().getRecipientId()) {
    chat = await Chat.findOne({ ext_id: dto.getItem().getRecipientId(), channel_id: channel._id })
    if (!chat) {
      const userData = await FacebookApiService.getFacebookUser(dto.getItem().getRecipientId(), channel.vSettings.getAccessToken())

      chat = await ChatService.getOrCreateConversation(userData.getName(), channel._id, dto.getItem().getRecipientId(), userData.getProfilePicUrl())
    }

    user = await User.findOne({
      company_id: channel.company._id,
      type: enums.acl_roles.COMPANY_OWNER,
      tester: { $ne: true },
      deleted_at: {
        $exists: false
      }
    }).sort({ _id: 1 })
  } else {
    const userData = await FacebookApiService.getFacebookUser(dto.getCustomerId(), channel.vSettings.getAccessToken())

    chat = await ChatService.getOrCreateConversation(userData.getName(), channel._id, dto.getCustomerId(), userData.getProfilePicUrl())

    if (chat.is_blocked == true) {
      throw new Error('Bu Chat Engelli olduğu için mesaj alınamıyor.')
    }
  }

  const message = await MessageRepo.create({
    type: messageObject.type,
    content: messageObject.content,
    chatId: chat.id,
    userId: user ? user._id : undefined,
    extId: dto.getItem().getMessageId() || '',
    fromType: dto.getId() !== dto.getItem().getRecipientId() ? enums.message_from_types.AGENT : enums.message_from_types.CUSTOMER,
    sendStatus: enums.message_send_statuses.SENT,
    time: dto.getItem().getTimestamp(),
    platform: dto.getId() !== dto.getItem().getRecipientId() ? enums.platforms.META : undefined
  }, channel.company_id.id, channel.id)

  // reklam mesajı ise reklam bilgisi kaydediliyor
  await SaveReferralClId(messageObject, chat, channel, message._id, req.trace_id)

  chat = await ChatService.newMessageAdded(chat, message, dto.getId() === dto.getItem().getRecipientId())

  message.conversation_id = chat

  let chatIntegration
  if (channel.integration) {
    chatIntegration = await IntegrationService.getOrCreateChatIntegration(chat, channel.integration)
  }

  incomingMessageJobResultDto.setCreatedChatMessage(new CreatedChatMessage(channel.company, channel, chat, channel.integration, message, chatIntegration))
  incomingMessageJobResultDto.setIsCustomerMessage(dto.getId() === dto.getItem().getRecipientId())

  return incomingMessageJobResultDto
}
