const FacebookAdsApiService = require('./FacebookAdsApiService')
const FacebookApiService = require('./FacebookApiService')
const enums = require('../../libs/enums')

const adsRequiredScopes = [
	'business_management',
	// 'catalog_management', debug kaldır
	// 'public_profile',debug kaldır

	//facebook page
	'pages_read_engagement',
	'pages_manage_metadata',
	'pages_read_user_content',
	'pages_show_list',
	'pages_messaging',
	'pages_manage_engagement',
	'pages_manage_posts', // - alınması gerek social media+ için
	// 'read_insights', //- alınması gerek social media+ için // debug kaldır
	// 'publish_video', // - alınması gerek social media+ için // debug kaldır

	'pages_manage_cta', // - alınması gerek reklam için
	'attribution_read', // - alınması gerek reklam için

	//instagram
	'instagram_basic',
	'instagram_manage_events',
	'instagram_manage_comments',
	'instagram_manage_messages',
	'instagram_manage_insights', // - alınması gerek social media+ için
	'instagram_content_publish', // - alınması gerek social media+ için

	// instagram_business_manage_insights // bu izin instagram page için değil profesyonel hesap için
	// instagram_business_content_publish // bu izin instagram page için değil profesyonel hesap için

	//ad related
	'page_events',
	'ads_management', // - alınması gerek reklam için
	'ads_read', // - alınması gerek reklam için
	'pages_manage_ads', // - alınması gerek reklam için

	'leads_retrieval' // - leads ler için alınması gerek

]

const FacebookAdsService = {

	validateUserToken: async (userAccessToken, debuggedToken, t) => {
		const missingScopes = []
		const missingGranularScopes = []

		adsRequiredScopes.forEach(scope => {
			const hasScope = debuggedToken.data.scopes.includes(scope)
			if ( ! hasScope) {
				missingScopes.push(scope)
			}
		})

		adsRequiredScopes.forEach(_granularScope => {

			// Check for existent
			const hasGranularScope = debuggedToken.data.granular_scopes.find(responseGranularScope => responseGranularScope.scope === _granularScope)
			if ( ! hasGranularScope) {
				missingGranularScopes.push(_granularScope)
				return
			}


			const hasInCorrectGranularScopeForPageOrIg = hasGranularScope.target_ids && ! hasGranularScope.target_ids.includes(entityId)
			if (hasInCorrectGranularScopeForPageOrIg) {
				missingGranularScopes.push(_granularScope)
			}

		})

		const hasInvalidToken = debuggedToken.data.is_valid === false || debuggedToken.data.error || missingScopes.length > 0 || missingGranularScopes.length > 0

		let errorMessage
		let errorMessages = []

		if (hasInvalidToken) {

			errorMessages.push(t(`Onboarding.errors.token_invalid_or_missing_permission`))

			if (debuggedToken.data.error?.message) {
				errorMessages.push(t(`Onboarding.errors.facebook_error_message`, {message: debuggedToken.data.error?.message}))
			}

			if (missingScopes.length > 0) {
				errorMessages.push(t(`Onboarding.errors.missing_scopes`, {scopes: missingScopes.join(',')}))
			}

			if (missingGranularScopes.length > 0) {
				errorMessages.push(t(`Onboarding.errors.missing_granular_scopes`, {granularScopes: missingGranularScopes.join(',')}))
			}
			errorMessage = errorMessages.join(' ')

		}

		return {
			hasInvalidToken,
			errorMessage,
			missingScopes,
			missingGranularScopes
		}
	},

	getAvailablePages: async (token) => {
		// Paralel API çağrıları
		const [pageResponse, childBmsResponse] = await Promise.all([
			FacebookApiService.getFacebookPages(token),
			FacebookAdsApiService.getChildBms()
		])

		const pages = pageResponse.data
		const childBms = childBmsResponse.data

		// Sayfa ID'lerini bir Set olarak sakla (O(1) lookup için)
		const pageIds = new Set(pages.map(page => page.id))

		// Kurulu child business manager'ları filtrele
		const installedChildBms = childBms.filter(business => {
			const isCorrectApp = business.relationship?.application.id === process.env.HELOROBO_APP_ID
			const hasClientPage = business.client_pages?.data?.[0]?.id
			const isUserPage = hasClientPage && pageIds.has(hasClientPage)

			return isCorrectApp && isUserPage
		})

		// Kurulu child BM'lerin sayfa ID'lerini Set olarak sakla
		const installedPageIds = new Set(
			installedChildBms
				.map(bm => bm.client_pages?.data?.[0]?.id)
				.filter(Boolean) // undefined/null değerleri filtrele
		)

		// Kurulu olmayan sayfaları döndür
		return pages.filter(page => ! installedPageIds.has(page.id))

	},

	getChildBms: async (pageIds) => {

		const childBms = await FacebookAdsApiService.getChildBms()

		return childBms.data.filter(
			(business) =>
				business.relationship?.application.id === process.env.HELOROBO_APP_ID &&
				pageIds.includes(business.client_pages.data?.[0].id)
		)
	},

	createChildBMWithAddAccount: async (pageId, pageName, userAccessToken, metaAdsChildBmDoc) => {
		//@TODO: Buraya retry mekanizması ayarlanacak
		const combinedName = pageName + ' Child BM'
		metaAdsChildBmDoc.name = combinedName
		metaAdsChildBmDoc.page_name = pageName
		metaAdsChildBmDoc.primary_page_id = pageId
		await metaAdsChildBmDoc.save()

		const stepper = new StepsGenerator(metaAdsChildBmDoc.steps)
		stepper.startStep(enums.meta_ads_create_child_bm_steps.create_child_bm)
		const response = await FacebookAdsApiService.createChildBm({
			name: combinedName,
			page_id: pageId,
			user_access_token: userAccessToken
		})

		const childBmId = response.data.id

		metaAdsChildBmDoc.ext_id = childBmId
		stepper.finishStep(enums.meta_ads_create_child_bm_steps.create_child_bm)


		stepper.startStep(enums.meta_ads_create_child_bm_steps.create_system_user)
		const systemUserResponse = await FacebookAdsApiService.createChildBmSystemUser({child_bm_id: childBmId})
		const childBMSuat = systemUserResponse.data.access_token

		metaAdsChildBmDoc.system_user_access_token = childBMSuat
		stepper.finishStep(enums.meta_ads_create_child_bm_steps.create_system_user)

		stepper.startStep(enums.meta_ads_create_child_bm_steps.get_child_bm_system_user_id)
		const systemUserIdResponse = await FacebookAdsApiService.getChildBmSystemUserId({
			child_bm_id: childBmId,
			child_bm_suat: childBMSuat
		})
		const systemUserId = systemUserIdResponse.data.data[0].id
		metaAdsChildBmDoc.system_user_id = systemUserId
		stepper.finishStep(enums.meta_ads_create_child_bm_steps.get_child_bm_system_user_id)


		stepper.startStep(enums.meta_ads_create_child_bm_steps.share_credit_line)
		await FacebookAdsApiService.shareCreditLine({child_bm_id: childBmId})
		stepper.finishStep(enums.meta_ads_create_child_bm_steps.share_credit_line)

		stepper.startStep(enums.meta_ads_create_child_bm_steps.get_fund_id)
		const fundId = await FacebookAdsApiService.getFundId({child_bm_id: childBmId, child_bm_suat: childBMSuat})
		metaAdsChildBmDoc.fund_id = fundId
		stepper.finishStep(enums.meta_ads_create_child_bm_steps.get_fund_id)


		stepper.startStep(enums.meta_ads_create_child_bm_steps.create_ad_account)
		const combinedAdAccountName = pageName + ' Child BM Ad Account'
		const adAccountResponse = await FacebookAdsApiService.createChildBMAdAccount({
			child_bm_id: childBmId,
			page_id: pageId,
			name: combinedAdAccountName,
			child_bm_suat: childBMSuat,
			funding_id: fundId
		})
		stepper.finishStep(enums.meta_ads_create_child_bm_steps.create_ad_account)

		const adAccountId = adAccountResponse.data.account_id
		metaAdsChildBmDoc.ad_account_id = adAccountId
		metaAdsChildBmDoc.ad_account_name = combinedAdAccountName


		stepper.startStep(enums.meta_ads_create_child_bm_steps.assign_cbm_su_to_ad_account)
		await FacebookAdsApiService.assignCBMSUToAdAccount({
			ad_account_id: adAccountId,
			child_bm_suat: childBMSuat,
			child_bm_suid: systemUserId,
			child_bm_id: childBmId
		})
		stepper.finishStep(enums.meta_ads_create_child_bm_steps.assign_cbm_su_to_ad_account)

	},
	async deleteChildBm(childBmId, systemUserAccessToken) {

		// const createSystemUserTokenResponse = await FacebookAdsApiService.createChildBmSystemUser({child_bm_id: childBmId})
		// const childBMSuat = createSystemUserTokenResponse.data.access_token

		const adAccounts = await FacebookAdsApiService.getChildBmAdAccounts(childBmId, systemUserAccessToken)
		const adAccountIds = adAccounts.data.data.map((adAccount) => adAccount.account_id).flat()

		let campaigns = []
		for (const adAccountId of adAccountIds) {
			await FacebookAdsApiService.getCampaigns(adAccountId).then((response) => {
				if (response.data.data.length > 0) {
					campaigns.push(...response.data.data)
				}
			})
		}

		// Stop BMs campaigns
		for (const campaign of campaigns) {
			await FacebookAdsApiService.updateCampaignStatus(campaign.id, 'PAUSED',systemUserAccessToken)
		}

		await FacebookAdsApiService.deleteChildBm(childBmId)
	},
}

class StepsGenerator {
	stepObject
	status = {
		none: 'none',
		started: 'started',
		done: 'done',
		error: 'error'
	}
	constructor(stepObject) {
		this.stepObject = stepObject
	}

	startStep(stepString){
		this.stepObject.find(stepEntry => stepEntry.step === stepString).status = this.status.started
	}
	finishStep(stepString){
		this.stepObject.find(stepEntry => stepEntry.step === stepString).status = this.status.done
	}

}

module.exports = FacebookAdsService
