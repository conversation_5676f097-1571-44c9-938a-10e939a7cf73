const FacebookMessagingItem = require('./FacebookMessagingItem')

class FacebookMessageDto {

  /**
   * @param id
   * @param data
   *
   * @private
   */
  constructor(id, data) {

    // facebook page id
    this.id = id

    // FacebookMessagingItem için kullanılmak üzere data
    this.data = data


  }

  /**
   * @param {string} id
   * @param {object} data
   *
   * @return {FacebookMessageDto}
   */
  static createFromFacebook(id, data) {
    return new FacebookMessageDto(id, data)
  }

  static createFromData(data) {
    return new FacebookMessageDto(data.id, data.data)
  }

  to() {

    return {
      id: this.id,
      data: this.data
    }

  }

  /**
   * @return {string}
   */
  getId() {
    return this.id
  }

  /**
   * @return {FacebookMessagingItem}
   */
  getItem() {
    return new FacebookMessagingItem(this.data)
  }

  /**
   * @return {string}
   */
  getCustomerId() {
    return this.getItem().getCustomerId(this.id)
  }

}

module.exports = FacebookMessageDto
