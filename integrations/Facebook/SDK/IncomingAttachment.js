const Types = require('./Types')

class IncomingAttachment {

  constructor(data) {
    this.data = data
  }

  getType() {
    return this.data.type
  }

  /**
   * @return {boolean}
   */
  isImage() {
    return this.getType() === Types.attachment.IMAGE
  }

  /**
   * @return {boolean}
   */
  isVideo() {
    return this.getType() === Types.attachment.VIDEO
  }

  /**
   * @return {boolean}
   */
  isAudio() {
    return this.getType() === Types.attachment.AUDIO
  }

  /**
   * @return {boolean}
   */
  isFile() {
    return this.getType() === Types.attachment.FILE
  }

  /**
   * @return {boolean}
   */
  isFallback() {
    return this.getType() === Types.attachment.FALLBACK
  }

  /**
   * @return {boolean}
   */
  isLocation() {
    return this.getType() === Types.attachment.LOCATION
  }

  /**
   * @return {boolean}
   */
  isProductTemplate() {
    return this.getType() === Types.attachment.TEMPLATE
  }

  /**
   * Sadece image olarak geliyor, payload'da ekstradan sticker_id bilgisi var url ile birlikte
   *
   * @return {boolean}
   */
  isSticker() {

    if (!this.isImage()) {
      return false
    }

    if ('payload' in this.data) {

      return 'sticker_id' in this.data.payload

    }

    return false

  }

  /**
   * @return {string}
   */
  getPayloadTitle() {
    return this.data.payload.title
  }

  /**
   * @return {string}
   */
  getPayloadUrl() {
    return this.data.payload.url
  }

  /**
   * @return {string}
   */
  getPayloadStickerId() {
    return this.data.payload.sticker_id
  }

  /**
   * @return {string}
   */
  getFilename() {
    return this.getPayloadUrl().split('/').pop().split('?').shift()
  }

  getLat() {
    return this.data.payload.coordinates.lat
  }

  getLng() {
    return this.data.payload.coordinates.long
  }

}

module.exports = IncomingAttachment
