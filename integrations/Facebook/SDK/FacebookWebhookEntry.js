const Types = require('./Types')

const FacebookMessagingItem = require('./FacebookMessagingItem')

class FacebookWebhookEntry {

  constructor(data) {
    this.data = data
  }

  /**
   * @return {object}
   */
  getData() {
    return this.data
  }

  /**
   * Facebook page id bilgisi
   *
   * @return {string}
   */
  getId() {
    return this.data.id
  }

  /**
   * Mesajlaşma amaçlı gönderilen bir entry olup olmadığını anlamamıza yarıyor
   *
   * @return {boolean}
   */
  isMessagingEntry() {

    if ('messaging' in this.data) {
      return Array.isArray(this.data.messaging)
    }

    if ('standby' in this.data) {
      return Array.isArray(this.data.standby)
    }

    return false

  }

  getMessagingType() {

    if (this.data.messaging[0].message) {
      return Types.messaging.MESSAGE
    }

    if (this.data.messaging[0].postback) {
      return Types.messaging.POSTBACK
    }

    if (this.data.messaging[0].read) {
      return Types.messaging.READ
    }

    return undefined

  }

  /**
   * @return {FacebookMessagingItem[]}
   */
  getMessagingItems() {
    if ('messaging' in this.data) {
      return this.data.messaging.map(item => new FacebookMessagingItem(item))
    }

    if ('standby' in this.data) {
      return this.data.standby.map(item => new FacebookMessagingItem(item))
    }

    return []
  }

  getFrom() {
    if ('messaging' in this.data) {
      return this.data.messaging[0].sender.id
    }

    if ('standby' in this.data) {
      return this.data.standby[0].sender.id
    }

    return ''
  }

  getRecipientId() {
    if ('messaging' in this.data) {
      return this.data.messaging[0].recipient.id
    }

    if ('standby' in this.data) {
      return this.data.standby[0].recipient.id
    }

    return ''
  }

}

module.exports = FacebookWebhookEntry

/*

{
  "id":"<PAGE_ID>",
  "time":1458692752478,
  "messaging":[  // Array containing one messaging object. Note that even though this is an array, it will only contain one messaging object.
    {
      "sender":{
        "id":"<PSID>"
      },
      "recipient":{
        "id":"<PAGE_ID>"
      },

      ...
    }
  ]
}

 */
