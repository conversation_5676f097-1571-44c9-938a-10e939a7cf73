const { nanoid } = require('nanoid')
const IncomingAttachment = require("./IncomingAttachment")

class FacebookMessagingItem {

  constructor(data) {
    this.data = data
  }

  getData() {
    return this.data
  }

  /**
   * @return {string}
   */
  getSenderId() {
    return this.data.sender.id
  }

  getRecipientId() {
    return this.data.recipient.id.toString()
  }

  /**
   * @param {string} accountId
   *
   * @return {string}
   */
  getCustomerId(accountId) {

    if (accountId === this.getSenderId()) {
      return this.getRecipientId()
    }

    return this.getSenderId()

  }

  /**
   * @return {boolean}
   */
  isIncomingMessage() {
    if ('message' in this.data) {
      return true
    }
    // generic mesaja verilmiş bir cevap demek oluyor
    if ('postback' in this.data) {
      return true
    }
    // quick_reply a cevap olarak gelen webhook
    if ('quick_reply' in this.data) {
      return true
    }
    return false
  }


  /**
   * Facebook is_echo mesajın facebook'a ulaştığının bilgisini içerir. @todo Instagram için farklı mı kontrol edilecek
   *
   * @link https://developers.facebook.com/docs/messenger-platform/reference/webhook-events/message-echoes
   *
   * @return {boolean}
   */
  isMessageSent() {

    if ('message' in this.data) {

      if ('is_echo' in this.data.message) {

        return Boolean(this.data.message.is_echo)

      }

    }

    return false

  }

  /**
   * @return {boolean}
   */
  isMessageSeen() {
    if ('read' in this.data) {
      if ('watermark' in this.data.read) {
        return true
      }
    }
  }

  /*
   * @return {string}
   */
  isOpenThread() {
    if ('message' in this.data) {
      if ('referral' in this.data.message) {
        if ('type' in this.data.message.referral) {
          return this.data.message.referral.type
        }
      }
    }
  }

  /**
   * @return {boolean}
   */
  hasMessageReferral() {
    if ('message' in this.data) {
      return 'referral' in this.data.message
    }

    return false
  }

  /**
   * @return {boolean}
   */
  hasReferral() {
    if ('referral' in this.data) {
      return true
    }

    return false
  }

  /**
   * @return {boolean}
   */
  getMessageSeenTime() {
    if ('read' in this.data) {
      if ('watermark' in this.data.read) {
        return this.data.read.watermark
      }
    }

    throw 'facebook okundu bilgisinde watermark bilgisi yok'
  }

  /**
   * @return {boolean}
   */
  isMessageReacted() {
    return 'reaction' in this.data
  }

  getReactionMid() {
    return this.data.reaction.mid
  }

  getReactionReaction() {
    return this.data.reaction.reaction
  }

  getReactionEmoji() {
    return this.data.reaction.emoji
  }

  getReactionAction() {
    return this.data.reaction.action
  }

  /**
   * @return {object}
   */
  getMessageReferral() {
    if ('message' in this.data) {
      if ('referral' in this.data.message) {
        return this.data.message.referral
      }
      return false
    }
  }

  /**
   * @return {object}
   */
  getReferral() {
    if ('referral' in this.data) {
      return true
    }
    return false
  }

  /**
   * @return {object}
   */
  getReferralData() {
    if ('referral' in this.data) {
      return this.data.referral
    }
    return false
  }

  /**
   * @link https://developers.facebook.com/docs/messenger-platform/reference/webhook-events/messaging_account_linking
   *
   * @return {boolean}
   */
  isAccountLinking() {
    return 'account_linking' in this.data
  }

  /**
   * @link https://developers.facebook.com/docs/messenger-platform/reference/webhook-events/message-deliveries
   *
   * @return {boolean}
   */
  isMessageDelivered() {
    return 'delivery' in this.data
  }

  /**
   * @link https://developers.facebook.com/docs/messenger-platform/reference/webhook-events/messaging_game_plays
   *
   * @return {boolean}
   */
  isGamePlay() {
    return 'game_play' in this.data
  }

  /**
   * @link https://developers.facebook.com/docs/messenger-platform/reference/webhook-events/messaging_handovers
   *
   * @return {boolean}
   */
  isPassThreadControl() {
    return 'pass_thread_control' in this.data
  }

  /**
   * @link https://developers.facebook.com/docs/messenger-platform/reference/webhook-events/messaging_handovers
   *
   * @return {boolean}
   */
  isTakeThreadControl() {
    return 'take_thread_control' in this.data
  }

  /**
   * @link https://developers.facebook.com/docs/messenger-platform/reference/webhook-events/messaging_handovers
   *
   * @return {boolean}
   */
  isRequestThreadControl() {
    return 'request_thread_control' in this.data
  }

  /**
   * Örnek'de sender yoktu, kullanılmaya başlanırsa gelen event kontrol edilecek.
   *
   * @link https://developers.facebook.com/docs/messenger-platform/reference/webhook-events/messaging_handovers
   *
   * @return {boolean}
   */
  isAppRoles() {
    return 'app_roles' in this.data
  }

  /**
   * @link https://developers.facebook.com/docs/messenger-platform/reference/webhook-events/messaging_optins
   *
   * @return {boolean}
   */
  isOptin() {
    return 'optin' in this.data
  }

  /**
   * @link https://developers.facebook.com/docs/messenger-platform/reference/webhook-events/messaging_policy_enforcement
   *
   * @return {boolean}
   */
  isPolicyEnforcement() {
    return 'policy_enforcement' in this.data
  }

  /**
   * @return {boolean}
   */
  isMessageTypeQuickReply() {

    if ('message' in this.data) {

      return 'quick_reply' in this.data.message

    }

    return false

  }

  /**
   * @return {boolean}
   */
  isMessageTypeReplyToMessage() {

    if ('message' in this.data) {

      if ('reply_to' in this.data.message) {

        return 'mid' in this.data.message.reply_to

      }

    }

    return false

  }

  /**
   * @return {boolean}
   */
  isMessageTypeStickerAttachments() {

    if (!this.__isMessageTypeAttachments()) {
      return false
    }

    for (const attachment of this.getAttachments()) {

      if (!attachment.isSticker()) {
        return false
      }

    }

    return true

  }

  /**
   * @return {boolean}
   */
  isMessageTypeImageAttachments() {

    if (!this.__isMessageTypeAttachments()) {
      return false
    }

    for (const attachment of this.getAttachments()) {

      if (!attachment.isImage()) {
        return false
      }

    }

    return true

  }

  /**
   * @return {boolean}
   */
  isMessageTypeVideoAttachments() {

    if (!this.__isMessageTypeAttachments()) {
      return false
    }

    for (const attachment of this.getAttachments()) {

      if (!attachment.isVideo()) {
        return false
      }

    }

    return true

  }

  /**
   * @return {boolean}
   */
  isMessageTypeAudioAttachments() {

    if (!this.__isMessageTypeAttachments()) {
      return false
    }

    for (const attachment of this.getAttachments()) {

      if (!attachment.isAudio()) {
        return false
      }

    }

    return true

  }

  /**
   * @return {boolean}
   */
  isMessageTypeFileAttachments() {

    if (!this.__isMessageTypeAttachments()) {
      return false
    }

    for (const attachment of this.getAttachments()) {

      if (!attachment.isFile()) {
        return false
      }

    }

    return true

  }

  /**
   * @return {boolean}
   */
  isMessageTypeFallbackAttachments() {

    if (!this.__isMessageTypeAttachments()) {
      return false
    }

    for (const attachment of this.getAttachments()) {

      if (!attachment.isFallback()) {
        return false
      }

    }

    return true

  }

  /**
   * @return {boolean}
   */
  isMessageTypeLocationAttachments() {

    if (!this.__isMessageTypeAttachments()) {
      return false
    }

    for (const attachment of this.getAttachments()) {

      if (!attachment.isLocation()) {
        return false
      }

    }

    return true

  }

  /**
   * @return {boolean}
   */
  isMessageTypeProductTemplateAttachments() {

    if (!this.__isMessageTypeAttachments()) {
      return false
    }

    for (const attachment of this.getAttachments()) {

      if (!attachment.isProductTemplate()) {
        return false
      }

    }

    return true

  }

  isMessageTypeText() {

    if ('message' in this.data) {

      return 'text' in this.data.message

    }

    return false

  }

  isMessageTypeTextCommand() {
    if ('message' in this.data) {
      if ('text' in this.data.message) {
        return 'commands' in this.data.message
      }
    }

    return false
  }

  /**
   * @return {string}
   */
  getTextForIncomingMessage() {
    if ('message' in this.data) {
      return this.data.message.text
    }
    if ('postback' in this.data) {
      return this.data.postback.title
    }
    return ''
  }

  /**
   * @return {string}
   */
  getTextPayloadForIncomingMessage() {
    if ('postback' in this.data) {
      return this.data.postback.payload
    }
    return undefined
  }

  isIncomingMessageTypeText() {
    if ('message' in this.data) {
      return 'text' in this.data.message
    }

    // generic mesaja verilmiş bir cevap demek oluyor
    if ('postback' in this.data) {
      return 'title' in this.data.postback
    }

    return false
  }

  /**
   * @return {boolean}
   */
  __isMessageTypeAttachments() {

    if ('message' in this.data) {

      if ('attachments' in this.data.message) {

        if (Array.isArray(this.data.message.attachments)) {

          return this.data.message.attachments.length > 0

        }

      }

    }

    return false

  }


  /**
   * @return {IncomingAttachment[]}
   */
  getAttachments() {

    if (!('message' in this.data)) {
      return []
    }

    if (!('attachments' in this.data.message)) {
      return []
    }

    if (Array.isArray(this.data.message.attachments)) {
      return this.data.message.attachments.map(item => new IncomingAttachment(item))
    }

    return []

  }

  getMessageId() {
    if ('message' in this.data) {
      return this.data.message.mid
    }
    // generic mesaj durumu için kendimiz bir id üretiyoruz
    if ('postback' in this.data) {
      return nanoid(60).toLowerCase()
    }
    return ''
  }

  /**
   * @return {string}
   */
  getMessageText() {
    return this.data.message.text
  }

  /**
   * @return {Array<string>}
   */
  getMessageCommands() {
    return this.data.message.commands
  }

  /**
   * @return {string}
   */
  getQuickReplyPayload() {
    return this.data.message.quick_reply.payload
  }

  /**
   * @return {string}
   */
  getReplyToMid() {
    return this.data.message.reply_to.mid
  }

  getTimestamp() {
    return this.data.timestamp
  }

  isProductMessage() {
    if ('message' in this.data) {
      if ('referral' in this.data.message) {
        if ('product' in this.data.message.referral) {
          if ('id' in this.data.message.referral.product) {
            return this.data.message.referral.product.id
          }
        }
      }
    }
  }

  /**
   * @return {boolean}
   */
  isMessageUnsupported() {
    if ('message' in this.data) {
      if ('is_unsupported' in this.data.message) {
        return Boolean(this.data.message.is_unsupported)
      }
    }

    return false
  }

  /**
   * @returns {Array<string>}
   */
  getDeliveredMessageIds() {
    return this.data.delivery.mids
  }

}

module.exports = FacebookMessagingItem

/*

// Text

{
  "sender": {
    "id": "2558108657626810"
  },
  "recipient": {
    "id": "110519843668861"
  },
  "timestamp": 1594200179659,
  "message": {
    "mid": "m_mMcN8DkzZSs8yNOLqp_o_TGBa52hOvBxhdJES2gdnqTTAXuR4X0zMNiDXMGfKr3pleQPRy36xBoUuvmadYqH9w",
    "text": "merhaba"
  }
}

// One Image or Gif

{
  "sender": {
    "id": "2558108657626810"
  },
  "recipient": {
    "id": "110519843668861"
  },
  "timestamp": 1594200231408,
  "message": {
    "mid": "m_wX43Rd4qrEmr6I8rWrqyQzGBa52hOvBxhdJES2gdnqRZaYOvUD_KBC0fG0sU_N6YkzQa_iinpkcyfeWEmgUEXQ",
    "attachments": [
      {
        "type": "image",
        "payload": {
          "url": "https://scontent.xx.fbcdn.net/v/t1.15752-9/103959449_1134492923573108_2268239627639618782_n.jpg?_nc_cat=106&_nc_sid=b96e70&_nc_ohc=kTq6WiXwbSkAX9rwBgi&_nc_ad=z-m&_nc_cid=0&_nc_ht=scontent.xx&oh=80c2f0ee72148689439f55a2494c0bee&oe=5F296A8D"
        }
      }
    ]
  }
}

// Two images at same time

{
  "sender": {
    "id": "2558108657626810"
  },
  "recipient": {
    "id": "110519843668861"
  },
  "timestamp": 1594200317150,
  "message": {
    "mid": "m_AxXYoTAfW7Q6BpsJHzLkZTGBa52hOvBxhdJES2gdnqRPSAUfJhF4aIsTjnSz3V5LQOVEZvu4_sTq4IME7ArNGA",
    "attachments": [
      {
        "type": "image",
        "payload": {
          "url": "https://scontent.xx.fbcdn.net/v/t1.15752-9/107103181_951751318609180_3177220915317531060_n.jpg?_nc_cat=101&_nc_sid=b96e70&_nc_ohc=VVj6T-b_efIAX-JGik9&_nc_ad=z-m&_nc_cid=0&_nc_ht=scontent.xx&oh=85e30f3ede9dd075141857ce43893afe&oe=5F2BD6EA"
        }
      },
      {
        "type": "image",
        "payload": {
          "url": "https://scontent.xx.fbcdn.net/v/t1.15752-9/105487050_299472671223982_2692703211087990735_n.jpg?_nc_cat=100&_nc_sid=b96e70&_nc_ohc=mj9ROV7QPu0AX_3FYyV&_nc_ad=z-m&_nc_cid=0&_nc_ht=scontent.xx&oh=072e551a9f26bc1aaf13374ae69120ec&oe=5F2D0DA4"
        }
      }
    ]
  }
}

// File. One by one

{
  "sender": {
    "id": "2558108657626810"
  },
  "recipient": {
    "id": "110519843668861"
  },
  "timestamp": 1594213817964,
  "message": {
    "mid": "m_KH_cXhf6VLUN7hhwWlf-QzGBa52hOvBxhdJES2gdnqTaXQqUtIduzbxwkEyyUOYJsO_ShjnPw05WXJ8W8bh0QA",
    "attachments": [
      {
        "type": "file",
        "payload": {
          "url": "https://cdn.fbsbx.com/v/t59.2708-21/107101624_1338248919857745_571634123137882233_n.txt/g%C3%B6nderme-senaryosu.txt?_nc_cat=104&_nc_sid=0cab14&_nc_ohc=xwVYBWNIchkAX9PJTTE&_nc_ht=cdn.fbsbx.com&oh=7d4afba6cac727d333601e8ad0acc605&oe=5F079882"
        }
      }
    ]
  }
}

// Sticker Send. One by One

{
  "sender": {
    "id": "2558108657626810"
  },
  "recipient": {
    "id": "110519843668861"
  },
  "timestamp": 1594214239949,
  "message": {
    "mid": "m_R6OjEvI3BRO3-sB0uIJ7JzGBa52hOvBxhdJES2gdnqRzISoQiGsWlKDlyedWhTyCc7X1-Z-kQOIP02ShATvLpg",
    "attachments": [
      {
        "type": "image",
        "payload": {
          "url": "https://scontent.xx.fbcdn.net/v/t39.1997-6/p100x100/851575_126362190881911_254357215_n.png?_nc_cat=1&_nc_sid=ac3552&_nc_ohc=HgjwThoNqb4AX8lLiNm&_nc_ad=z-m&_nc_cid=0&_nc_ht=scontent.xx&oh=07fb3fafa5ab38a8757ffc0f1c6c1aa5&oe=5F2A972B",
          "sticker_id": 126362187548578
        }
      }
    ],
    "sticker_id": 126362187548578
  }
}

// Video. One by One

{
  "sender": {
    "id": "2558108657626810"
  },
  "recipient": {
    "id": "110519843668861"
  },
  "timestamp": 1594215341817,
  "message": {
    "mid": "m_RI90z6RiGsmhk_XCzf6teTGBa52hOvBxhdJES2gdnqTVBeUmph8ajS6yX_EV5RYF8Wq7Jjx1MIup6FyNVerYWg",
    "attachments": [
      {
        "type": "video",
        "payload": {
          "url": "https://video.xx.fbcdn.net/v/t42.3356-2/107063638_3145693615510873_8779930662404072223_n.mp4/video-1594215341.mp4?_nc_cat=107&_nc_sid=060d78&_nc_ohc=DdzY5GD5jF4AX8QRjDv&vabr=387472&_nc_ht=video.xx&oh=8d4d44f17f2244f5dc13fa9c4edce9cb&oe=5F071778"
        }
      }
    ]
  }
}

// Audio. One by one

{
  "sender": {
    "id": "2558108657626810"
  },
  "recipient": {
    "id": "110519843668861"
  },
  "timestamp": 1594217828723,
  "message": {
    "mid": "m_GEwlOd6GpRJVJvQR-8tVEjGBa52hOvBxhdJES2gdnqTijVSHasTlusfF7vGON0tEgFF_b6J1JpX1KhzBQrWSNQ",
    "attachments": [
      {
        "type": "audio",
        "payload": {
          "url": "https://cdn.fbsbx.com/v/t59.3654-21/12023607_1505023613145935_249648830_n.mp3/First-Of-The-Year-Equinox-Skrillex-OFFICIAL.mp3?_nc_cat=102&_nc_sid=7272a8&_nc_ohc=yNjMGnwie9QAX9y8C-O&_nc_ht=cdn.fbsbx.com&oh=aa02dfd3ea1d1ca80604f2fa082c9d43&oe=5F07A86B"
        }
      }
    ]
  }
}

// location

{
  "sender": {
    "id": "2373232006123727"
  },
  "recipient": {
    "id": "110519843668861"
  },
  "timestamp": 1596625370451,
  "message": {
    "mid": "m_ulcYFcP8ujmszamB2oSFzjqTXkxkKRuUAufXT57j50-oDpmVLRfVN_HTbeM3V3VTn5Dl8PHBZzk0SdbSQBO9yg",
    "attachments": [
      {
        "type": "location",
        "payload": {
          "coordinates": {
            "lat": 37.591817,
            "long": 36.825807
          },
          "url": "https://l.facebook.com/l.php?u=https%3A%2F%2Fwww.bing.com%2Fmaps%2Fdefault.aspx%3Fv%3D2%26pc%3DFACEBK%26mid%3D8100%26where1%3D37.591817%252C%2B36.825807%26FORM%3DFBKPL1%26mkt%3Den-US&h=AT1HesF9RmPg6Uik3EUHmT42BajuNafZ8IYT1yQ5V0l1ZaZPX97cUnuUP6-tMUa9LuUTuBY36kPqwa4gWmMkukst0WFVrDk52TT0BwcEYGzWl64JqJ1mFWnlLBBu7wHkSiM1Y4OQ5IWKzKQ&s=1",
          "title": "Pinned Location"
        }
      }
    ]
  }
}

*/
