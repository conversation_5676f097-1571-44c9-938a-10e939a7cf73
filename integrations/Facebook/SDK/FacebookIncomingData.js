const FacebookWebhookEntry = require('./FacebookWebhookEntry')

class FacebookIncomingData {

  constructor(data) {
    this.data = data
  }

  /**
   * @return {boolean}
   */
  isPage() {
    return this.data.object === 'page'
  }

  /**
   * @return {FacebookWebhookEntry[]}
   */
  getFacebookIncomingEntries() {
    return this.data.entry.map(entry => {
      return new FacebookWebhookEntry(entry)
    })
  }

}

module.exports = FacebookIncomingData

/*

{
  "object":"page",
  "entry":[
    {
      "id":"<PAGE_ID>",
      "time":1458692752478,
      "messaging":[
        {
          "sender":{
            "id":"<PSID>"
          },
          "recipient":{
            "id":"<PAGE_ID>"
          },

          ...
        }
      ]
    }
  ]
}

 */
