const {default: axios} = require('axios')

// constantst
const FB_BASE_URL = `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}`
const HELOROBO_BSP_ACCESS_TOKEN = process.env.HELOROBO_BSP_ACCESS_TOKEN
const PARENT_BM_ID = process.env.TSOFT_BUSINESS_ID
const PARENT_BM_CREDIT_LINE_ID = process.env.PARENT_BM_LINE_OF_CREDIT_ID
const APP_ID = process.env.HELOROBO_APP_ID

const destinationTypes = {
	INSTAGRAM_DIRECT: 'INSTAGRAM_DIRECT',
	WHATSAPP: 'WHATSAPP',
	MESSENGER: 'MESSENGER',
	//multidestiantion
	MESSAGING_INSTAGRAM_DIRECT_MESSENGER_WHATSAPP: 'MESSAGING_INSTAGRAM_DIRECT_MESSENGER_WHATSAPP',
	MESSAGING_INSTAGRAM_DIRECT_MESSENGER: 'MESSAGING_INSTAGRAM_DIRECT_MESSENGER',
	MESSAGING_INSTAGRAM_DIRECT_WHATSAPP: 'MESSAGING_INSTAGRAM_DIRECT_WHATSAPP',
	MESSAGING_MESSENGER_WHATSAPP: 'MESSAGING_MESSENGER_WHATSAPP'
}
const devicePlatforms = {
	mobile: 'mobile',
	desktop: 'desktop'
}

const metaMarketingScopes = [
	'ads_management',
	'business_management',
	'pages_read_engagement',
	'pages_manage_metadata',
	'pages_read_user_content',
	'pages_show_list',
	'pages_manage_engagement',
	// 'pages_manage_ads',
	// 'public_profile',
	//newlly added accounts
	'pages_show_list',
	'pages_manage_engagement'
	// 'pages_manage_cta',
	// 'ads_read',
	// 'page_events'
]


const metricFields = [
	'results',
	'cost_per_result',
	'spend',
	'conversions',
	'conversion_values',
	'catalog_segment_value',
	'clicks',
	'conversion_rate_ranking',
	'converted_product_app_custom_event_fb_mobile_purchase',
	'converted_product_app_custom_event_fb_mobile_purchase_value',
	'converted_product_offline_purchase',
	'converted_product_offline_purchase_value',
	'converted_product_omni_purchase',
	'converted_product_omni_purchase_values',
	'converted_product_quantity',
	'converted_product_value',
	'converted_product_website_pixel_purchase',
	'converted_product_website_pixel_purchase_value',
	'converted_promoted_product_app_custom_event_fb_mobile_purchase',
	'converted_promoted_product_app_custom_event_fb_mobile_purchase_value',
	'converted_promoted_product_offline_purchase',
	'converted_promoted_product_offline_purchase_value',
	'converted_promoted_product_omni_purchase',
	'converted_promoted_product_omni_purchase_values',
	'converted_promoted_product_quantity',
	'converted_promoted_product_value',
	'converted_promoted_product_website_pixel_purchase',
	'converted_promoted_product_website_pixel_purchase_value',
	'cost_per_action_type',
	'cost_per_conversion',
	'cost_per_estimated_ad_recallers',
	'cost_per_inline_link_click',
	'cost_per_inline_post_engagement',
	'cost_per_objective_result',
	'cost_per_outbound_click',
	'cost_per_thruplay',
	'cost_per_unique_action_type',
	'cost_per_unique_click',
	'cost_per_unique_inline_link_click',
	'cost_per_unique_outbound_click'
]

const fields = [
	'account_id',
	'automatic_manual_state',
	'bid_strategy',
	'billing_event',
	'brand_safety_config',
	'budget_remaining',
	'campaign{name,id,objective}',
	'campaign_active_time',
	'campaign_attribution',
	'created_time',
	'daily_budget',
	'daily_min_spend_target',
	'daily_spend_cap',
	'destination_type',
	'end_time',
	'effective_status',
	'id',
	'instagram_user_id',
	'is_dynamic_creative',
	'is_incremental_attribution_enabled',
	'frequency_control_specs',
	'issues_info',
	'learning_stage_info',
	'lifetime_budget',
	'lifetime_imps',
	'lifetime_min_spend_target',
	'lifetime_spend_cap',
	'min_budget_spend_percentage',
	'multi_optimization_goal_weight',
	'name',
	'optimization_goal',
	'optimization_sub_event',
	'pacing_type',
	'promoted_object',
	'recommendations',
	'recurring_budget_semantics',
	'source_adset',
	'start_time',
	'status',
	'targeting',
	'targeting_optimization_types',
	'delivery_estimate',
	`insights{${metricFields.join(',')}}`
]


const adaccountImageFields = [
	'hash', 'height', 'id', 'name', 'original_height', 'is_associated_creatives_in_adgroups', 'original_width', 'url', 'url_128', 'updated_time', 'status', 'permalink_url', 'creatives', 'created_time', 'account_id', 'width'
]
const adaccountVideoFields = ['thumbnails', 'id', 'description', 'post_id', 'post_views', 'title', 'views', 'likes', 'permalink_url', 'picture', 'source', 'length', 'format', 'boost_eligibility_info']

const FacebookAdsApiService = {
	getChildBms() {
		// sonradan bu otomatik çekilecek
		return axios
			.get(`${FB_BASE_URL}/${PARENT_BM_ID}/owned_businesses`, {
				params: {
					fields:
						'relationship{ad_accounts{name,account_id},application,system_user{id,name}},name,owned_businesses,client_pages',
					limit: 10000,
					access_token: HELOROBO_BSP_ACCESS_TOKEN
				}
			})
			.then(({data}) => data) // burada relationShip olmama ad_account durumu olmama ve system_user olmama durumları var.
	},
	getAdAccounts(token) {
		return axios.get(`${FB_BASE_URL}/me/adaccounts`, {
			params: {access_token: token}
		})
	},

	getChildBmAdAccounts(childBmId, childBmAccessToken) {
		return axios.get(`${FB_BASE_URL}/${childBmId}/owned_ad_accounts`, {
			params: {
				access_token: childBmAccessToken
			}
		})
	},
	getCampaign(campaignId, childBMAccessToken) {
		return axios.get(`${FB_BASE_URL}/${campaignId}`, {
			params: {
				fields:
					'name,status,objective,bid_strategy,created_time,insights{clicks,results,reach,impressions,cost_per_result,spend},effective_status',
				access_token: childBMAccessToken
			}
		})
	},
	getCampaigns(actId) {
		return axios.get(`${FB_BASE_URL}/act_${actId}/campaigns`, {
			params: {
				fields:
					'name,status,objective,bid_strategy,created_time,insights{clicks,results,reach,impressions,cost_per_result,spend},effective_status',
				access_token: HELOROBO_BSP_ACCESS_TOKEN
			}
		})
	},
	createCampaign({
		               actId,
		               name,
		               objective,
		               status,
		               specialAdCategories,
		               childBMAccessToken
	               }) {

		return axios.post(`${FB_BASE_URL}/act_${actId}/campaigns`, {
			name: name,
			objective: objective,
			status: status ? 'ACTIVE' : 'PAUSED',
			access_token: childBMAccessToken,
			special_ad_categories: specialAdCategories || 'NONE'
		})
	},
	deleteCampaign(campaignId, childBMAccessToken) {
		return axios.delete(`${FB_BASE_URL}/${campaignId}`, {
			params: {
				access_token: childBMAccessToken
			}
		})
	},
	getCampaignAdsets(campaignId, childBMAccessToken) {
		//https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group/adsets/
		return axios.get(`${FB_BASE_URL}/${campaignId}/adsets`, {
			params: {
				// fields: 'name,status,created_time,insights{clicks,results,reach,impressions,cost_per_result,spend}',
				fields: fields.join(','),
				access_token: childBMAccessToken
			}
		})
	},
	getAdSets(adAccountId, childBMAccessToken) {
		//https://developers.facebook.com/docs/marketing-api/reference/ad-account/adsets/
		return axios.get(`${FB_BASE_URL}/act_${adAccountId}/adsets`, {
			params: {
				// fields: 'name,status,created_time,budget_remaining,bid_constraints,learning_stage_info,bid_strategy,budget_type,delivery_estimate{bid_estimate}',//,insights{clicks,results,reach,impressions,cost_per_result,spend}',
				fields: fields.join(','),
				access_token: childBMAccessToken
			}
		})
	},
	createAdSet({
		            adAccountId,
		            campaignId,
		            name,
		            status,
		            childBMAccessToken,
		            bidStrategy = 'LOWEST_COST_WITHOUT_CAP',
		            billingEvent = 'IMPRESSIONS',
		            dailyBudget,
		            bidAmount = 0,
		            lifetimeBudget, // daily budget verilmezse bu verilecek
		            destinationType = destinationTypes.INSTAGRAM_DIRECT,
		            pageId,
		            optimizationGoal = 'CONVERSATIONS',
		            startTime = Math.floor(Date.now() / 1000),
		            endTime, // lifetimeBudget var ise bu alan zorunlu
		            countries = ['TR'],
		            devicePlatformsArgs = [devicePlatforms.mobile, devicePlatforms.desktop]
	            }) {
		return axios.post(`${FB_BASE_URL}/act_${adAccountId}/adsets`, {
			name: name,
			campaign_id: campaignId,
			status: status,
			bid_strategy: bidStrategy,
			billing_event: billingEvent,
			daily_budget: dailyBudget,
			lifetime_budget: lifetimeBudget,
			destination_type: destinationType,
			optimization_goal: optimizationGoal,
			end_time: endTime,
			promoted_object: {
				page_id: pageId
			},
			start_time: startTime,
			targeting: {
				geo_locations: {
					countries
				},
				device_platforms: devicePlatformsArgs
			},
			access_token: childBMAccessToken
		})
	},
	async updateAdsetStatus(adsetId, status, childBMAccessToken) {
		return axios.post(`${FB_BASE_URL}/${adsetId}`, {
			status: status,
			access_token: childBMAccessToken
		})
	},
	deleteAdset(adsetId, childBMAccessToken) {
		return axios.delete(`${FB_BASE_URL}/${adsetId}`, {
			params: {
				access_token: childBMAccessToken
			}
		})
	},
	getAds(adAccountId, childBMAccessToken) {
		return axios.get(`${FB_BASE_URL}/act_${adAccountId}/ads`, {
			params: {
				fields: 'name,status,created_time,insights{clicks,results,reach,impressions,cost_per_result,spend},campaign{name,status},creative{name,instagram_permalink_url,thumbnail_url}',
				access_token: childBMAccessToken
			}
		})
	},
	createAd({
		         adAccountId,
		         adsetId,
		         adcreativeId,
		         name,
		         status,
		         childBMAccessToken
	         }) {
		return axios.post(`${FB_BASE_URL}/act_${adAccountId}/ads`, {
			name: name,
			adset_id: adsetId,
			creative: {
				creative_id: adcreativeId
			},
			status: status,
			access_token: childBMAccessToken
		})
	},
	updateAdStatus(adId, status, childBMAccessToken) {
		return axios.post(`${FB_BASE_URL}/${adId}`, {
			status: status,
			access_token: childBMAccessToken
		})
	},
	deleteAd(adId, childBMAccessToken) {
		return axios.delete(`${FB_BASE_URL}/${adId}`, {
			params: {
				access_token: childBMAccessToken
			}
		})
	},

	getCampaignAds(campaignId, childBMAccessToken) {
		return axios.get(`${FB_BASE_URL}/${campaignId}/ads`, {
			params: {
				fields: 'name,status,created_time,insights{clicks,results,reach,impressions,cost_per_result,spend},campaign{name,status},creative{name,instagram_permalink_url,thumbnail_url}',
				access_token: childBMAccessToken
			}
		})
	},

	getAdsetAds(adsetId, childBMAccessToken) {
		return axios.get(`${FB_BASE_URL}/${adsetId}/ads`, {
			params: {
				fields: 'name,status,created_time,insights{clicks,results,reach,impressions,cost_per_result,spend},campaign{name,status},creative{name,instagram_permalink_url,thumbnail_url}',
				access_token: childBMAccessToken
			}
		})
	},

	async updateCampaignStatus(campaignId, status, childBMAccessToken) {
		return axios.post(`${FB_BASE_URL}/${campaignId}`, {
			status: status,
			access_token: childBMAccessToken
		})
	},
	async deleteChildBm(childBmId) {
		return axios.delete(`${FB_BASE_URL}/${PARENT_BM_ID}/owned_businesses`, {
			params: {
				client_id: childBmId,
				access_token: HELOROBO_BSP_ACCESS_TOKEN
			}
		})
	},

	async createChildBm(body) {
		const name = body.name
		const pageId = body.page_id
		const userAccessToken = body.user_access_token

		return axios.post(
			`${FB_BASE_URL}/${PARENT_BM_ID}/owned_businesses`,
			{
				id: PARENT_BM_ID,
				name: name,
				vertical: 'OTHER',
				shared_page_id: pageId,
				page_permitted_tasks: ['CREATE_CONTENT', 'MODERATE', 'ADVERTISE', 'ANALYZE'],
				timezone_id: 134, // Türkiye probably.
				access_token: userAccessToken
			},
			{
				headers: {
					'Content-Type': 'application/json'
				}
			}
		)
	},
	async createChildBmSystemUser(body) {
		const childBmId = body.child_bm_id

		return axios.post(
			`${FB_BASE_URL}/${childBmId}/access_token`,
			{
				id: childBmId,
				app_id: APP_ID,
				scope: metaMarketingScopes.join(','),
				access_token: HELOROBO_BSP_ACCESS_TOKEN
			},
			{
				headers: {
					'Content-Type': 'application/json'
				}
			}
		)
	},

	async getChildBmSystemUserId(body) {
		const childBmId = body.child_bm_id
		const childBMSuat = body.child_bm_suat

		return axios.get(`${FB_BASE_URL}/${childBmId}/system_users`, {
			params: {
				fields: 'name,id,role,access_token',
				access_token: childBMSuat
			}
		})
	},

	async shareCreditLine(body) {
		const childBmId = body.child_bm_id
		const amount = body.amount || 10

		return axios.post(
			`${FB_BASE_URL}/${PARENT_BM_CREDIT_LINE_ID}/owning_credit_allocation_configs`,
			{
				receiving_business_id: childBmId,
				amount: amount,
				access_token: HELOROBO_BSP_ACCESS_TOKEN
			},
			{
				headers: {
					'Content-Type': 'application/json'
				}
			}
		)
	},

	async getFundId(body) {
		const childBMSuat = body.child_bm_suat
		const childBmId = body.child_bm_id

		return axios
			.get(`${FB_BASE_URL}/${childBmId}/extendedcredits`, {
				params: {
					fields:
						'balance,allocated_amount,max_balance,online_max_balance,send_bill_to_biz_name,owning_credit_allocation_configs,partition_from,liable_biz_name,credit_type,credit_available,is_access_revoked,id,owner_business,owner_business_name,receiving_credit_allocation_config,extended_credit_invoice_groups,is_automated_experience,legal_entity_name',
					access_token: childBMSuat
				}
			})
			.then(({data}) => data.data[0].id)
	},
	async createChildBMAdAccount(body) {
		const pageId = body.page_id
		const name = body.name
		const childBMSuat = body.child_bm_suat
		const fundingId = body.funding_id
		const childBmId = body.child_bm_id

		return axios.post(
			`${FB_BASE_URL}/${childBmId}/adaccount`,
			{
				name: name,
				currency: 'USD', // bkaılacak
				timezone_id: 1,
				end_advertiser: pageId,
				media_agency: 'NONE',
				partner: 'NONE',
				funding_id: fundingId,
				access_token: childBMSuat
			},
			{
				headers: {
					'Content-Type': 'application/json'
				}
			}
		)
	},

	async assignCBMSUToAdAccount(body) {
		const adAccountId = body.ad_account_id
		const childBMSuat = body.child_bm_suat
		const childBMSuid = body.child_bm_suid
		const childBmId = body.child_bm_id

		return axios.post(
			`${FB_BASE_URL}/act_${adAccountId}/assigned_users`,
			{
				user: childBMSuid,
				tasks: 'MANAGE,ADVERTISE,ANALYZE',
				business: childBmId,
				access_token: childBMSuat
			},
			{
				headers: {
					'Content-Type': 'application/json'
				}
			}
		)
	},


	async createAdcreative(adAccountId, childBMAccessToken, adCreativeObject, pageWelcomeMessage, callToActions) {

		// const imageCreative = {
		// 	page_id: adCreativeObject.pageId,
		// 	instagram_actor_id: adCreativeObject.pageId,
		// 	link_data: {
		// 		message: adCreativeObject.message,
		// 		image_hash: adCreativeObject.imageHash
		// 		// page_welcome_message: pageWelcomeMessage,
		// 		// call_to_action: callToActions // sonra eklenecek
		// 	}
		// }
		// const videoCreative = {
		// 	message: adCreativeObject.example,
		// 	page_id: adCreativeObject.pageId,
		// 	instagram_actor_id: adCreativeObject.pageId,
		// 	video_data: {
		// 		video_id: adCreativeObject.videoId,
		// 		image_url: adCreativeObject.imageUrl
		// 		// page_welcome_message: pageWelcomeMessage,
		// 		// call_to_action: callToActions // sonra eklenecek
		// 	}
		// }
		//
		// const carouselCreative = {
		// 	page_id: adCreativeObject.pageId,
		// 	instagram_actor_id: adCreativeObject.pageId,
		// 	child_attachments: [
		// 		{
		// 			name: 'pictureAttachment',
		// 			image_hash: adCreativeObject?.imageHashes?.[0]
		// 			// call_to_action: callToActions // sonra eklenecek
		// 		},
		// 		{
		// 			name: 'videoAttachment',
		// 			video_id: adCreativeObject.videoId,
		// 			picture: adCreativeObject?.imageHashes?.[1]
		// 			// call_to_action: callToActions // sonra eklenecek
		// 		}
		// 	]
		// }
		//
		// const objectCreativeRequest = {
		// 	object_story_spec: imageCreative
		// }
		//
		// const instagramPostCreativeRequest = {
		// 	object_id: adCreativeObject.pageId,
		// 	instagram_user_id: adCreativeObject.instagramUserId,
		// 	source_instagram_media_id: adCreativeObject.sourceInstagramMediaId
		// 	// call_to_action: callToActions // sonra eklenecek
		// }
		//
		// const instagramImageCreativeRequest = {
		// 	object_story_spec: {
		// 		page_id: adCreativeObject.pageId,
		// 		instagram_actor_id: adCreativeObject.pageId,
		// 		link_data: {
		// 			message: adCreativeObject.message,
		// 			picture: adCreativeObject.picture // image_url
		// 			// page_welcome_message: pageWelcomeMessage,
		// 			// call_to_action: callToActions // sonra eklenecek
		// 		}
		// 	}
		// }

		// Instagram CTX
		return axios.post(`${FB_BASE_URL}/act_${adAccountId}/adcreatives`, {
			...adCreativeObject.toJson(),
			access_token: childBMAccessToken
		})
	},

	async getAdcreatives(adAccountId, childBMAccessToken) {
		return axios.get(`${FB_BASE_URL}/act_${adAccountId}/adcreatives`, {
			params: {
				fields: 'id,source_instagram_media_id,source_facebook_post_id',
				access_token: childBMAccessToken
			}
		})
	},

	/**
	 *
	 * @param pageId
	 * @param userAccessToken
	 * @returns {Promise<({id:string,name:string})>}
	 */
	async getInstagramAccount(pageId, userAccessToken) {
		return axios.get(`${FB_BASE_URL}/${pageId}`, {
			params: {
				fields: 'connected_instagram_account{id,name}',
				access_token: userAccessToken
			}
		})
			.then(response => response.data.connected_instagram_account)
			.then(({id, name}) => ({id, name}))
	},

	async getPageAccessToken(pageId, userAccessToken) {
		return axios.get(`${FB_BASE_URL}/${pageId}`, {
			params: {
				fields: 'access_token',
				access_token: userAccessToken
			}
		})
			.then(response => response.data.access_token)
	},

	/**
	 *
	 * @param igUserId
	 * @param pageAccessToken
	 * @returns {Promise<AxiosResponse<{data:InstagramMedia[]}>>}
	 */
	async getInstagramPosts(igUserId, pageAccessToken) {
		const fields = [
			'id',
			'media_url',
			'like_count',
			'comments_count',
			'caption',
			'timestamp',
			'thumbnail_url',
			'permalink',
			'media_type',
			'media_product_type',
			'boost_eligibility_info',
			'message'
		]
		return axios.get(`${FB_BASE_URL}/${igUserId}/media`, {
			params: {
				fields: fields.join(','),
				access_token: pageAccessToken
			}
		})

	},

	/**
	 *
	 * @param pageId
	 * @param pageAccessToken
	 * @returns {Promise<AxiosResponse<{data:FacebookPost[]}>>}
	 */
	async getFacebookPosts(pageId, pageAccessToken) {
		const fields = [
			'id',
			'permalink_url',
			'created_time',
			'full_picture',
			'is_eligible_for_promotion',
			'message',
			'call_to_action',
			'story',
			'status_type',
			'is_instagram_eligible'
		]
		return axios.get(`${FB_BASE_URL}/${pageId}/feed`, {
			params: {
				fields: fields.join(','),
				access_token: pageAccessToken
			}
		})

	},

	/**
	 *
	 * @param {string} adAccountId
	 * @param {string} childBMAccessToken
	 * @returns {Promise<AxiosResponse<AdaccountImages>>}
	 */
	async getAdaccountImages(adAccountId, childBMAccessToken) {
		return axios.get(`${FB_BASE_URL}/act_${adAccountId}/adimages`, {
			params: {
				fields: adaccountImageFields.join(','),
				access_token: childBMAccessToken
			}
		})
	},

	/**
	 *
	 * @param {string} adAccountId
	 * @param {string} childBMAccessToken
	 * @returns {Promise<AxiosResponse<AdaccountVideos>>}
	 */
	async getAdaccountVideos(adAccountId, childBMAccessToken) {
		return axios.get(`${FB_BASE_URL}/act_${adAccountId}/advideos`, {
			params: {
				fields: adaccountVideoFields.join(','),
				access_token: childBMAccessToken
			}
		})
	},

	async getAdaccountImageDetail(imageId, childBMAccessToken) {
		return axios.get(`${FB_BASE_URL}/${imageId}`, {
			params: {
				fields: adaccountImageFields.join(','),
				access_token: childBMAccessToken
			}
		})
	},

	async getAdaccountVideoDetail(videoId, childBMAccessToken) {
		return axios.get(`${FB_BASE_URL}/${videoId}`, {
			params: {
				fields: adaccountVideoFields.join(','),
				access_token: childBMAccessToken
			}
		})
	},


	async uploadAdImage(adAccountId, childBMAccessToken, file) {
		const formData = new FormData()
		formData.append('bytes', file)
		formData.append('messaging_product', 'whatsapp')


		return axios.post(`${FB_BASE_URL}/act_${adAccountId}/adimages?access_token=` + childBMAccessToken, formData, {
			headers: {
				'Content-Type': 'multipart/form-data'
			}
		}).then(response => response.data.id)
	},

	async fetchSUATS(body) {
		const childBmId = body.child_bm_id

		return axios.post(`${FB_BASE_URL}/${childBmId}/access_token`, {
			params: {
				id: childBmId,
				app_id: APP_ID,
				scope: 'ads_read,ads_management,business_management',
				access_token: HELOROBO_BSP_ACCESS_TOKEN
			}
		})

	},
	deprecatedCreateAdAccount(pageId) {
		return axios.post(
			`${FB_BASE_URL}/${pageId}/adaccounts`,
			{
				name: 'HeloRobo Ad Account',
				access_token: HELOROBO_BSP_ACCESS_TOKEN
			},
			{
				headers: {
					'Content-Type': 'application/json'
				}
			}
		)
	}


}

module.exports = FacebookAdsApiService


/**
 * @typedef {Object} FacebookPost
 * @property {string} id - Facebook postunun ID'si.
 * @property {string} permalink_url - Postun permalink URL'si.
 * @property {string} created_time - ISO 8601 formatında oluşturulma tarihi.
 * @property {string} full_picture - Görselin URL'si.
 * @property {boolean} is_eligible_for_promotion - Reklam için uygun mu?
 * @property {boolean} is_instagram_eligible - Instagram reklam çıkılabilir mi? ?
 * @property {string} status_type - Postun türü. <added_video,added_photos,mobile_status_update,shared_story>
 * @property {string} [message] - Mesaj metni.
 * @property {string} [story] - Postun altta ne olduğunu belirten metin.
 * @property {Object} [call_to_action] - Çağrı metni bilgileri.
 * @property {string} [call_to_action.type] - Çağrı metni türü.
 * @property {Object} [call_to_action.value] - Çağrı metni değeri.
 */

/**
 * @typedef {Object} InstagramMedia
 * @property {string} id - Medyanın Instagram üzerindeki ID'si.
 * @property {string} media_url - Görselin veya videonun URL'si.
 * @property {number} like_count - Beğeni sayısı.
 * @property {number} comments_count - Yorum sayısı.
 * @property {string} [caption] - Açıklama (varsa).
 * @property {string} timestamp - ISO 8601 formatında oluşturulma tarihi.
 * @property {string} [thumbnail_url] - Video için küçük resim URL’si (IMAGE için olmayabilir).
 * @property {string} permalink - Medyaya doğrudan bağlantı.
 * @property {string} media_type - Medya türü (IMAGE, VIDEO, CAROUSEL_ALBUM).
 * @property {string} media_product_type - Yayın türü (REELS, FEED vs.).
 * @property {Object} [boost_eligibility_info] - Sponsorlu içerik uygunluk bilgisi.
 * @property {boolean} [boost_eligibility_info.eligible_to_boost] - Boost edilebilir mi? Bu reklam için kullanıp kullanılmayacğaını bleirtir.
 */


/** @typedef {object} AdaccountImages
 * @property {object[]} data
 * @property {string} data.hash
 * @property {number} data.height
 * @property {string} data.id
 * @property {string} data.name
 * @property {number} data.original_height
 * @property {boolean} data.is_associated_creatives_in_adgroups
 * @property {number} data.original_width
 * @property {string} data.url
 * @property {string} data.url_128
 * @property {string} data.updated_time
 * @property {string} data.status
 * @property {string} data.permalink_url
 * @property {string} data.created_time
 * @property {string} data.account_id
 * @property {number} data.width
 * @property {string[]} data.creatives
 * @property {object} paging
 * @property {object} paging.cursors
 * @property {string} paging.cursors.before
 * @property {string} paging.cursors.after
 */


/** @typedef {object} AdaccountVideosResponse
 * @property {AdaccountVideo[]} data
 * @property {object} paging
 * @property {object} paging.cursors
 * @property {string} paging.cursors.before
 * @property {string} paging.cursors.after
 */

/** @typedef {object} AdaccountVideoDetailResponse
 * 
 */


/** @typedef {object} AdaccountVideo
 * @property {object} thumbnails
 * @property {object[]} thumbnails.data
 * @property {string} thumbnails.data.id
 * @property {number} thumbnails.data.height
 * @property {number} thumbnails.data.scale
 * @property {string} thumbnails.data.uri
 * @property {number} thumbnails.data.width
 * @property {boolean} thumbnails.data.is_preferred
 * @property {string} id
 * @property {string} post_id
 * @property {number} post_views
 * @property {string} title
 * @property {number} views
 * @property {string} permalink_url
 * @property {string} picture
 * @property {string} source
 * @property {number} length
 * @property {object[]} format
 * @property {string} format.embed_html
 * @property {string} format.filter
 * @property {number} format.height
 * @property {string} format.picture
 * @property {number} format.width
 * @property {object} boost_eligibility_info
 * @property {boolean} boost_eligibility_info.eligible_to_boost
 */
