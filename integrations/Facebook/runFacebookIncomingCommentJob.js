const pino = require('pino')()
const enums = require('../../libs/enums')

const Channel = require('../../models/Channel')
const Comment = require('../../models/Comment')
const Reviewer = require('../../models/Reviewer')

const FacebookCommentDto = require('../../dtos/Marketing/FacebookCommentDto')

const FacebookApiService = require('../Facebook/FacebookApiService')

// Kullanıcı oluşturulur veya bulunur
const createReviewer = async (userData, companyId, profilePictureUrl) => {

  const newReviewer = new Reviewer()

  newReviewer.type = enums.acl_roles.FACEBOOK_COMMENT_USER
  newReviewer.username = userData.name || 'İsimsiz Kullanıcı'
  newReviewer.ext_id = userData.id
  newReviewer.company_id = companyId

  if ('picture' in profilePictureUrl) {
    if ('data' in profilePictureUrl.picture) {
      newReviewer.profile_picture_url = profilePictureUrl.picture.data.url
    }
  }

  return newReviewer.save()
}

const saveComment = (postId, commentId, commentData, field, reviewerId, channelId, facebookPost) => {

  const comment = new Comment()

  comment.reviewer_id = reviewerId
  comment.post_id = postId
  comment.ext_id = commentId
  comment.comment = {
    ...commentData,
    field: field
  }
  comment.readed = false
  comment.channel_id = channelId
  comment.post_data = {
    image: facebookPost.full_picture,
    text: facebookPost.message,
    url: facebookPost.permalink_url
  }

  return comment.save()
}

module.exports = async (body, traceId) => {

  const facebookCommentDto = new FacebookCommentDto(body)

  const facebookCommentData = []

  for (const entry of facebookCommentDto.getEntries()) {

    for (const commentData of entry.getChanges()) {

      // Yorum dışında bir type gelirse şimdlik loglanacak
      if (!commentData.isComment()) {
        pino.info({
          message: 'FacebookIncomingCommentJob Yorum dışında bir bilgi geldi.',
          trace_id: traceId,
          data: commentData.getData(),
          channel_id: entry.getPageId(),
          chat_id: commentData.getUserId()
        })
        continue
      }

      // agentin kendi yorumu gelmiş demektir.
      if (commentData.getUserId() === entry.getPageId()) {
        continue
      }

      const channel = await Channel.findOne({
        type: enums.channel_types.FACEBOOK_PAGE,
        provider: enums.channel_providers.FACEBOOK,
        ext_id: entry.getPageId(),
        is_active: true,
        deleted_at: {
          $exists: false
        }
      }).populate('company_id').populate('integration_id')

      if (!channel) {

        pino.info({
          message: 'FacebookIncomingCommentJob channel not found.',
          trace_id: traceId,
          data: commentData.getData(),
          channel_id: entry.getPageId(),
          chat_id: commentData.getUserId()
        })

        throw new Error('FacebookIncomingCommentJob channel not found: ' + entry.getPageId())
      }

      if (channel.company?.is_active === false) {
        throw new Error('Şirket Pasif Olduğu için Mesaj Alınmadı')
      }

      let reviewer = await Reviewer.findOne({
        type: enums.acl_roles.FACEBOOK_COMMENT_USER,
        ext_id: commentData.getUserId()
      })

      let getProfilePicture
      // Kullanıcı yoksa facebook tarafında kullanıcı bilgileri alınıyor
      if (!reviewer) {
        // Yorum yapan kullanıcnın profile resmi Alınıyor.
        getProfilePicture = await FacebookApiService.getProfilePicture(commentData.getUserId(), channel.vSettings.getAccessToken()).catch(err => {
          pino.info({
            message: 'FacebookIncomingCommentJob profile pic cannot take.',
            trace_id: traceId,
            data: commentData.getData(),
            channel_id: entry.getPageId(),
            chat_id: commentData.getUserId(),
            error: err.response
          })

          return {
            picture: {
              data: {
                url: ''
              }
            }
          }
        })

        // Reviewer kullanıcı oluşturuluor veya alınıyor
        reviewer = await createReviewer(commentData.getUserData(), channel.company._id, getProfilePicture)
      } else {
        getProfilePicture = reviewer.profile_picture_url
      }

      // Facebook tarafından post alınıyor      
      const facebookPost = await FacebookApiService.getPostAsId(commentData.getPostId(), channel.vSettings.getAccessToken()).catch(err => {
        pino.info({
          message: 'FacebookIncomingCommentJob -> post data cannot take.',
          trace_id: traceId,
          data: commentData.getData(),
          channel_id: entry.getChannelId(),
          chat_id: commentData.getUserId(),
          error: err
        })
        throw new Error('FacebookIncomingCommentJob channel not found: ' + entry.getChannelId())
      })

      // Yorum bilgileri kaydediliyor
      const comment = await saveComment(commentData.getPostId(), commentData.getCommentId(), commentData.getCommentData(), commentData.getField(), reviewer._id, channel._id, facebookPost)

      facebookCommentData.push({
        id: comment.id,
        company_socket_code: channel.id,
        comment_id: commentData.getCommentId(),
        post_id: commentData.getPostId(),
        channel_type: enums.channel_types.FACEBOOK_PAGE,
        channel_id: entry.getPageId(),
        user: {
          id: reviewer.id,
          name: commentData.getUserName() || 'İsimsiz Kullanıcı',
          image_url: getProfilePicture
        },
        created_at: Date.now(),
        comment: {
          ...commentData.getCommentData(),
          field: commentData.getField()
        },
        is_development_data: {
          status: ["101387518922317", "108258554913521", "107737934223238", "100470372925857"].includes(entry.getPageId()),
          body: body,
          type: enums.channel_types.FACEBOOK_PAGE
        },
      })

    }

  }

  // kuyruk için data dönüldü
  return facebookCommentData

}
