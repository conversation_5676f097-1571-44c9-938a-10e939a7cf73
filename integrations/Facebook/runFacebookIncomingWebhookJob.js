const async = require('async')
const pino = require('pino')()

const MessageSentJob = require('../../jobs/MessageSentJob')
const MessageSeenJob = require('../../jobs/MessageSeenJob')
const MessageReactedJob = require('../../jobs/MessageReactedJob')
const IncomingMessageJob = require('../../jobs/IncomingMessageJob')

const FacebookMessageDto = require('./SDK/FacebookMessageDto')
const FacebookIncomingData = require('./SDK/FacebookIncomingData')

const IncomingWebhookJobResultDto = require('../../dtos/IncomingWebhookJobResultDto')
const Channel = require("../../models/Channel");

/**
 * @param req
 * @param {object} body req.body job üzerinden dolaylı olarak geliyor
 *
 * @return {Promise<IncomingWebhookJobResultDto>}
 */
module.exports = (req, body) => {

  const incomingWebhookJobResultDto = new IncomingWebhookJobResultDto()

  const incomingData = new FacebookIncomingData(body)

  if (!incomingData.isPage()) {
    pino.info({
      message: 'Facebook webhook tarafında yeni bir type geldi',
      trace_id: req.trace_id,
      data: JSON.stringify(body),
      timestamp: new Date()
    })

    throw new Error('Facebook webhook body not supported.')
  }

  return async.series(incomingData.getFacebookIncomingEntries().map(facebookWebhookEntry => async () => {

    // Helorobo Offical ve test id ve 2.tanımtim
    if (['143550241200148', '107737934223238', '108258554913521', '100470372925857', '144431015424945'].includes(facebookWebhookEntry.getId())) {
      incomingWebhookJobResultDto.setHasDevelopmentContent(true)
      if (facebookWebhookEntry.getFrom() !== facebookWebhookEntry.getId()) {
        incomingWebhookJobResultDto.setSenderId(facebookWebhookEntry.getFrom())
      } else {
        incomingWebhookJobResultDto.setSenderId(facebookWebhookEntry.getRecipientId())
      }
    }

    const channel = await Channel.findOne({
      ext_id: facebookWebhookEntry.getId(),
      is_active: true,
      deleted_at: {
        $exists: false
      }
    }).populate('company_id')
    if (!channel) {
      throw new Error('Kanal Bulunamadı')
    }

    if (channel.company?.is_active === false) {
      throw new Error('Şirket Pasif Olduğu için Mesaj Alınmadı')
    }

    if (channel.company_id.vData.getUsingAsBridge()) {
      incomingWebhookJobResultDto.setUsingAsBridge(channel.company_id.vData.getUsingAsBridge())
      incomingWebhookJobResultDto.setWebhookUrl(channel.company_id.vData.getDataWebhookUrl())
      incomingWebhookJobResultDto.setWebhookUrlHash(channel.company_id.vData.getDataWebhookUrlHash())
    }

    if (!facebookWebhookEntry.isMessagingEntry()) {
      pino.info({
        message: 'runFacebookIncomingWebhookJob beklenmeyen FacebookWebhookEntry tipi',
        trace_id: req.trace_id,
        data: JSON.stringify(facebookWebhookEntry.data),
        channel_id: facebookWebhookEntry.getId(),
        chat_id: facebookWebhookEntry.getFrom(),
        timestamp: new Date()
      })
      // devam etmeye gerek yok
      return false

    }

    return async.series(facebookWebhookEntry.getMessagingItems().map(item => async () => {

      pino.info({
        message: 'runFacebookIncomingWebhookJob webhook',
        trace_id: req.trace_id,
        data: JSON.stringify(item.getData()),
        channel_id: facebookWebhookEntry.getId(),
        chat_id: facebookWebhookEntry.getFrom(),
        timestamp: new Date()
      })

      if (item.isMessageSent()) {
        if (facebookWebhookEntry.getId() === item.getRecipientId()) {
          return req.app.services.JobService.addMessageSentJob(
            req,
            MessageSentJob.TYPE_FACEBOOK,
            FacebookMessageDto.createFromFacebook(facebookWebhookEntry.getId(), item.getData()).to()
          ).then(() => true)
        } else {
          return req.app.services.JobService.addIncomingMessageJob(
            req,
            IncomingMessageJob.TYPE_FACEBOOK,
            FacebookMessageDto.createFromFacebook(facebookWebhookEntry.getId(), item.getData()).to()
          ).then(() => true)
        }
      }

      if (item.isMessageSeen()) {
        return req.app.services.JobService.addMessageSeenJob(
          req,
          MessageSeenJob.TYPE_FACEBOOK,
          FacebookMessageDto.createFromFacebook(facebookWebhookEntry.getId(), item.getData()).to()
        ).then(() => true)
      }

      if (item.isMessageDelivered()) {
        return req.app.services.JobService.addMessageDeliveredJob(
          req,
          MessageSeenJob.TYPE_FACEBOOK,
          FacebookMessageDto.createFromFacebook(facebookWebhookEntry.getId(), item.getData()).to()
        ).then(() => true)
      }

      if (item.isMessageReacted()) {
        return req.app.services.JobService.addMessageReactedJob(
          req,
          MessageReactedJob.TYPE_FACEBOOK,
          FacebookMessageDto.createFromFacebook(facebookWebhookEntry.getId(), item.getData()).to()
        ).then(() => true)
      }

      // facebook için desteklenmeyen mesaj işlemi yapılacak
      if (item.isMessageUnsupported()) {
        return req.app.services.JobService.addIncomingMessageJob(
          req,
          IncomingMessageJob.TYPE_INSTAGRAM,
          FacebookMessageDto.createFromInstagram(facebookWebhookEntry.getId(), item.getData()).to()
        ).then(() => true)
      }

      if (item.hasMessageReferral()) {
        // facebook için gelen mesaj için işlem yapılacak
        return req.app.services.JobService.addIncomingMessageJob(
          req,
          IncomingMessageJob.TYPE_FACEBOOK,
          FacebookMessageDto.createFromFacebook(facebookWebhookEntry.getId(), item.getData()).to()
        ).then(() => true)
      }

      if (item.hasReferral()) {
        // facebook için gelen mesaj için işlem yapılacak
        return req.app.services.JobService.addIncomingMessageJob(
          req,
          IncomingMessageJob.TYPE_FACEBOOK,
          FacebookMessageDto.createFromFacebook(facebookWebhookEntry.getId(), item.getData()).to()
        ).then(() => true)
      }

      if (item.isIncomingMessage()) {
        // facebook için gelen mesaj için işlem yapılacak
        return req.app.services.JobService.addIncomingMessageJob(
          req,
          IncomingMessageJob.TYPE_FACEBOOK,
          FacebookMessageDto.createFromFacebook(facebookWebhookEntry.getId(), item.getData()).to()
        ).then(() => true)
      }

      pino.info({
        message: 'FacebookIncomingWebhookJob içinde unknown messaging type',
        trace_id: req.trace_id,
        data: JSON.stringify(item.getData()),
        channel_id: facebookWebhookEntry.getId(),
        chat_id: facebookWebhookEntry.getFrom(),
        timestamp: new Date()
      })

      return false

    })).then(results => !results.includes(false))

  })).then(results => {

    if (results.includes(false)) {
      // yolunda gitmeyen bir şeyler oldu
      pino.info({
        message: 'Facebook webhook tarafında yolunda gitmeyen bir şeyler olmuş olabilir',
        trace_id: req.trace_id,
        data: JSON.stringify(body),
        timestamp: new Date()
      })
    }

    return incomingWebhookJobResultDto

  })

}
