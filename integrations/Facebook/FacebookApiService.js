const crypto = require('crypto')
const moment = require('moment')
const { HttpsProxyAgent } = require('https-proxy-agent')
const { default: axios } = require('axios')
const pino = require('pino')()

const enums = require('./../../libs/enums')
const utils = require('./../../libs/utils')
const helpers = require('./../../libs/helpers')

const FacebookUser = require('./SDK/User')

const FacebookGenerateSecretKey = (token) => {
  return crypto.createHmac('SHA256', process.env.TSOFT_APP_SECRET_KEY).update(token).digest('hex')
}

const proxy = {
  httpsAgent: process.env.META_PROXY_REQUEST_STATUS !== "true" ? undefined : new HttpsProxyAgent(process.env.META_PROXY_REQUEST_URL)
}

const FacebookApiService = {

  /**
   * @param {string} userPsId
   * @param {string} accessToken
   *
   * @return {Promise<FacebookUser>}
   */
  getFacebookUser: (userPsId, accessToken) => {

    return FacebookApiService.__get('/' + userPsId, accessToken, {
      fields: 'id,name,first_name,last_name,profile_pic'
    }).then(data => new FacebookUser(data))

  },

  sendMessage: (recipientId, message, accessToken, traceId) => {

    return Promise.resolve().then(() => {

      switch (message.type) {

        case enums.message_types.TEXT:

          let text = message.vContentText

          if (message.vContentBbCode) {
            text = helpers.getBbCodeProviderParser(message.vContentText)
          }

          return {
            recipient: {
              id: recipientId
            },
            message: {
              text: text
            },
          }

        case enums.message_types.IMAGE_URL:

          return {
            recipient: {
              id: recipientId
            },
            message: {
              attachment: {
                type: 'image',
                payload: {
                  url: message.vContentUrl
                }
              }
            }
          }

        case enums.message_types.FACEBOOK_GENERIC_BUTTON:

          return {
            recipient: {
              id: recipientId
            },
            message: {
              attachment: {
                type: 'template',
                payload: {
                  template_type: 'generic',
                  elements: [
                    {
                      buttons: message.vContent.buttons,
                      image_url: message.vContent.image_url,
                      default_action: message.vContent.default_action,
                      subtitle: message.vContentBbCode === true ? helpers.getBbCodeProviderParser(message.vContent.subtitle) : message.vContent.subtitle,
                      title: message.vContent.title
                    }
                  ]
                }
              }
            }
          }

        case enums.message_types.VIDEO_URL:

          return {
            recipient: {
              id: recipientId
            },
            message: {
              attachment: {
                type: 'video',
                payload: {
                  url: message.vContentUrl
                }
              }
            }
          }

        case enums.message_types.AUDIO_URL:

          return {
            recipient: {
              id: recipientId
            },
            message: {
              attachment: {
                type: 'audio',
                payload: {
                  url: message.vContentUrl
                }
              }
            }
          }

        case enums.message_types.FACEBOOK_MEDIA_REPLY:

          return {
            recipient: {
              comment_id: message.vContent.comment.id
            },
            message: {
              text: message.vContentText
            }
          }

        default:
          throw 'Message Type Error'
      }

    }).then(async data => {
      try {
        if (message.data?.tag === enums.message_types.HUMAN_AGENT) {
          data.messaging_type = 'MESSAGE_TAG'
          data.tag = enums.message_types.HUMAN_AGENT
        }

        const response = await FacebookApiService.__post('/me/messages', accessToken, data)

        pino.info({
          trace_id: traceId,
          timestamp: new Date(),
          message: 'facebook send message',
          data: JSON.stringify(response.data),
          chat_ext_id: recipientId,
          access_token: accessToken,
          config: JSON.stringify(data)
        })

        return response.message_id
      } catch (error) {
        pino.error({
          trace_id: traceId,
          timestamp: new Date(),
          message: 'facebook send message failed',
          error: JSON.stringify(error.response?.data || { message: 'facebook send message failed' }),
          chat_ext_id: recipientId,
          access_token: accessToken,
          config: JSON.stringify(data)
        })

        throw error
      }
    })

  },

  markAsSeen: (req, recipientId, accessToken) => {

    return FacebookApiService.__post('/me/messages', accessToken, {
      'recipient': {
        'id': recipientId
      },
      'sender_action': 'mark_seen'
    }).then(response => {
      pino.info({
        trace_id: req.trace_id,
        timestamp: new Date(),
        message: 'marked seen',
        data: response,
        chat_ext_id: recipientId,
        access_token: accessToken,
      })
    }).catch(error => {
      pino.error({
        trace_id: req.trace_id,
        chat_ext_id: recipientId,
        access_token: accessToken,
        error: JSON.stringify(error.response?.data || { message: 'beklenmedik bir hata oluştu' }),
        timestamp: new Date(),
        message: 'Services::ConversationService ConversationService::markAsSeen markAsSeen yapılamadı.'
      })
    })

  },

  __get: (endpoint, accessToken, params = {}) => {

    params.access_token = accessToken

    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}` + endpoint,
      method: 'get',
      params: params,
      ...proxy
    }

    return axios.request(config).then((response) => {
      pino.info({
        timestamp: new Date(),
        config: JSON.stringify(config),
        message: 'get graph response',
        data: JSON.stringify(response.data)
      })

      return response.data

    }).catch(error => {
      pino.error({
        error: JSON.stringify(error.response?.data || { message: 'beklenmedik bir hata oluştu' }),
        timestamp: new Date(),
        message: 'get facebook graph error'
      })
      helpers.handleAxiosError(error, error.response?.data?.error?.message)
    })

  },

  __post: (endpoint, accessToken, json = {}) => {

    json.access_token = accessToken

    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}` + endpoint,
      method: 'post',
      data: json,
      ...proxy
    }

    return axios.request(config).then((response) => {
      delete config.httpsAgent

      pino.info({
        timestamp: new Date(),
        message: 'post graph response',
        config: JSON.stringify(config),
        data: JSON.stringify(response.data)
      })

      return response.data
    })

  },


  getPostComments: (postId, accessToken, paginationInfo) => {

    let url = `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${postId}/comments?fields=
    id,
    from,
    parent,
    message,
    like_count,
    created_time,
    comment_count,
    comments{
      id,
      from{
      id,
      name
      },
      message,
      like_count,
      created_time,
      comment_count
    }&access_token=${accessToken}&limit=50`

    if (paginationInfo) {
      url += `&${paginationInfo.process}=${paginationInfo.key}`
    }

    return axios.get(url, { ...proxy }).then(response => response.data)

  },

  getPosts: (faUserId, accessToken, perpage, paginationInfo) => {

    let url = `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${faUserId}/posts?fields=
      id,
      message,
      created_time,
      full_picture,
      likes.summary(true).limit(0),
      permalink_url,
      comments.summary(true).limit(0)&access_token=${accessToken}&limit=20`

    if (paginationInfo) {
      url += `&${paginationInfo.process}=${paginationInfo.key}`
    }

    return axios.get(url, { ...proxy }).then(response => response.data)

  },

  sendCommentToPost: (postId, message, accessToken) => {
    return axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${postId}/comments?message=${encodeURI(message)}&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  sendReplyToComment: (commentId, comment, accessToken) => {
    return axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${commentId}/comments?message=${encodeURI(comment)}&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  getProfilePicture: (pageId, accessToken) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${pageId}?fields=picture&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  getPostAsId: (postId, accessToken) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${postId}?fields=id,message,created_time,full_picture,permalink_url,comments.summary(true).limit(0),from&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  getCommentDetail: (commentId, accessToken) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${commentId}?fields=like_count&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  getCommentDetailV2: (commentId, accessToken) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${commentId}?fields=message,created_time,from&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  createLongLivedTokenForAdvermind: (token) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/oauth/access_token?grant_type=fb_exchange_token&client_id=410909726533893&client_secret=0769c3382c185f8933af15261816bdc4&fb_exchange_token=${token}`, { ...proxy }).then(response => response.data)
  },

  getFacebookConversationAnalytics: (wabaId, startDate, endDate, period) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${wabaId}?fields=
      conversation_analytics.start(${startDate})
      .end(${endDate})
      .granularity(${period})
      .dimensions(["conversation_type", "conversation_direction", "phone"])
    &access_token=${process.env.FACEBOOK_BSP_ACCESS_TOKEN}`, { ...proxy }).then(response => response.data)
  },

  getFacebookMessageAnalytics: (wabaId, startDate, endDate, period) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${wabaId}?fields=
      analytics
      .start(${startDate})
      .end(${endDate})
      .granularity(${period})
    &access_token=${process.env.FACEBOOK_BSP_ACCESS_TOKEN}`, { ...proxy }).then(response => response.data)
  },

  tokenDebug: async (shortLivedToken) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/debug_token?input_token=${shortLivedToken}&access_token=${process.env.FACEBOOK_APP_ACCESS_TOKEN}`, { ...proxy }).then(response => response.data)
  },

  tokenDebugForAdvermind: async (token) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/debug_token?input_token=${token}&access_token=${process.env.TSOFT_BSP_ACCESS_TOKEN}`, { ...proxy }).then(response => response.data)
  },

  longLivedToken: async (shortLivedToken) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/oauth/access_token?grant_type=fb_exchange_token&client_id=${process.env.HELOROBO_APP_ID}&client_secret=${process.env.HELOROBO_APP_SECRET}&fb_exchange_token=${shortLivedToken}`, { ...proxy }).then(response => response.data)
  },
  longLivedTokenFromCodeWithRedirectUrl: async (code, redirectUrl) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/oauth/access_token?client_id=${process.env.HELOROBO_APP_ID}&client_secret=${process.env.HELOROBO_APP_SECRET}&redirect_uri=${redirectUrl || process.env.APP_BASE_URL}&code=${code}`, { ...proxy }).then(response => response.data)
  },

  getBusinessData: async (businessId, token) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${businessId}?fields=id,name,link,verification_status&access_token=${token}`, { ...proxy }).then(response => response.data)
  },

  getFacebookPages: async (longLivedPageAccessToken) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/me/accounts?fields=id,name,access_token&limit=100&access_token=${longLivedPageAccessToken}`, { ...proxy }).then(response => response.data)
  },

  createChildBM: async (name, pageId, token) => {
    return await axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${process.env.TSOFT_BUSINESS_ID}/owned_businesses?&name=${name}&vertical=OTHER&page_permitted_tasks=['CREATE_CONTENT', 'MODERATE', 'ADVERTISE', 'ANALYZE']&access_token=${token}&appsecret_proof=${FacebookGenerateSecretKey(token)}&timezone_id=134&shared_page_id=${pageId}`, { ...proxy }).then(response => response.data)
  },

  setUserAccessToken: async (childBMID) => {
    return await axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${childBMID}/access_token?id=${childBMID}&app_id=${process.env.TSOFT_APP_ID}&scope=catalog_management,manage_business_extension,pages_show_list,ads_management,ads_read,business_management,pages_read_engagement,pages_manage_metadata,pages_manage_ads&access_token=${process.env.TSOFT_BSP_ACCESS_TOKEN}&appsecret_proof=${FacebookGenerateSecretKey(process.env.TSOFT_BSP_ACCESS_TOKEN)}`, { ...proxy }).then(response => response.data)
  },

  getFacebookSystemUsers: async (childBMID, accessToken) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${childBMID}/system_users?access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  setCreditAllocationConfig: async (childBMID) => {
    return await axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${process.env.PARENT_BM_LINE_OF_CREDIT_ID}/owning_credit_allocation_configs?receiving_business_id=${childBMID}&access_token=${process.env.TSOFT_BSP_ACCESS_TOKEN}&appsecret_proof=${FacebookGenerateSecretKey(process.env.TSOFT_BSP_ACCESS_TOKEN)}`, { ...proxy }).then(response => response.data)
  },

  getFacebookExtendedCredits: async (childBMID, accessToken) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${childBMID}/extendedcredits?fields=id,max_balance&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  // fundingId -> getFacebookExtendedCredits den gelen id
  setFacebookAdAccount: async (pageId, fundingId, name, childBMID, accessToken) => {
    return await axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${childBMID}/adaccount?name=${name}&access_token=${accessToken}&appsecret_proof=${FacebookGenerateSecretKey(accessToken)}&currency=TRY&timezone_id=134&end_advertiser=${pageId}&media_agency=NONE&partner=NONE&funding_id=${fundingId}`, { ...proxy }).then(response => response.data)
  },

  // adminId -> getFacebookSystemUsers den gelen ADMIN id si
  setAssignedToUser: async (adaccountId, adminId, childBMID, accessToken) => {
    return await axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${adaccountId}/assigned_users?user=${adminId}&access_token=${accessToken}&appsecret_proof=${FacebookGenerateSecretKey(accessToken)}&tasks=MANAGE,ADVERTISE,ANALYZE&business=${childBMID}`, { ...proxy }).then(response => response.data)
  },

  deleteChildBM: async (childBMId) => {
    return axios.delete(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${process.env.TSOFT_BUSINESS_ID}/owned_businesses?access_token=${process.env.TSOFT_BSP_ACCESS_TOKEN}&client_id=${childBMId}`, { ...proxy })
  },

  getChildBusiness: async () => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${process.env.TSOFT_BUSINESS_ID}/owned_businesses?access_token=${process.env.TSOFT_BSP_ACCESS_TOKEN}`, { ...proxy }).then(response => response.data)
  },

  postInstagramPersistentMenu: async (token, data) => {
    return await axios.request({
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/me/messenger_profile?platform=instagram&access_token=${token}`,
      method: 'POST',
      data,
      ...proxy
    }).then(response => response.data)
  },

  deleteInstagramPersistentMenu: async (token) => {
    return axios.request({
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/me/messenger_profile?fields=["persistent_menu"]&platform=instagram&access_token=${token}`,
      method: 'DELETE',
      ...proxy
    }).then(response => response.data)
  },

  postIceBreakers: async (token, data) => {
    return axios.request({
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/me/messenger_profile?platform=instagram&access_token=${token}`,
      method: 'POST',
      data,
      ...proxy
    }).then(response => response.data)
  },

  deleteIceBreakers: async (token) => {
    return axios.request({
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/me/messenger_profile?platform=instagram&access_token=${token}`,
      method: 'DELETE',
      data: {
        fields: [
          "ice_breakers"
        ]
      },
      ...proxy
    }).then(response => response.data)
  },

  setFacebookPageSubscription: async (pageId, accessToken) => {
    return await axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${pageId}/subscribed_apps?subscribed_fields=
      messages,
      messaging_postbacks,
      messaging_optins,
      messaging_optouts,
      message_deliveries,
      message_reads,
      messaging_payments,
      messaging_pre_checkouts,
      messaging_checkout_updates,
      messaging_account_linking,
      messaging_referrals,
      message_echoes,
      messaging_game_plays,
      standby,
      messaging_handovers,
      messaging_policy_enforcement,
      message_reactions,
      inbox_labels,
      messaging_feedback,
      messaging_customer_information,
      whatsapp_messages
      &access_token=${accessToken}`, { ...proxy })
  },

  debugToken: async (accessToken) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/debug_token?input_token=${accessToken}&access_token=${process.env.FACEBOOK_APP_ACCESS_TOKEN}`, { ...proxy }).then(response => response.data)
  },

  getBusinessId: async (pageAccessToken, pageId) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${pageId}?fields=business&access_token=${pageAccessToken}`, { ...proxy }).then(response => response.data)
  },

  getSubscribedFields: async (pageAccessToken, pageId) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${pageId}/subscribed_apps?&access_token=${pageAccessToken}`, { ...proxy }).then(response => response.data)
  },

  deleteFacebookPageSubscription: async (pageId) => {
    return await axios.delete(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${pageId}/subscribed_apps?access_token=${process.env.FACEBOOK_APP_ACCESS_TOKEN}`, { ...proxy })
  },

  revokeUserPermissions: async (accessToken) => {
    const tokenData = await FacebookApiService.debugToken(accessToken)
    // sadece page tokenı ile işlem yapılabilir.
    if (tokenData.data.type === 'USER') {
      throw new Error('Token tipi USER. Bu token ile işlem yapılamaz. Lütfen Page tokenı kullanın.')
    }

    return await axios.delete(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${tokenData.data.user_id}/permissions?access_token=${process.env.FACEBOOK_APP_ACCESS_TOKEN}`, { ...proxy })
  },

  getInstagramUserName: async (accessToken) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/me?fields=username&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  getFacebookPageConnectedInstagram: async (token) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/me/accounts?fields=id,name,access_token,instagram_business_account{id,username}&access_token=${token}&limit=1000`, { ...proxy }).then(response => response.data)
  },

  sharePixelWithChildBM: async (pixelId, businessManagerId, accountId, accessToken) => {
    return await axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${pixelId}/shared_accounts?business=${businessManagerId}&account_id=${accountId}&access_token=${accessToken}`, { ...proxy })
  },

  shareCatalogWithSystemUser: async (catalogId, businessManagerId, accountId, accessToken) => {
    return await axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${catalogId}/assigned_users?business=${businessManagerId}&user=${accountId}&tasks=ADVERTISE,MANAGE&access_token=${accessToken}&appsecret_proof=${FacebookGenerateSecretKey(accessToken)}`, { ...proxy })
  },

  shareCatalogWithBM: async (catalogId, businessManagerId, accessToken) => {
    return await axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${catalogId}/agencies?business=${businessManagerId}&permitted_tasks=MANAGE&access_token=${accessToken}&appsecret_proof=${FacebookGenerateSecretKey(accessToken)}`, { ...proxy })
  },

  sendEventData: async (pageId, eventId, customerId, customData, eventName, accessToken, traceId) => {
    const time = moment().unix()

    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${eventId}/events?access_token=${accessToken}`,
      method: 'POST',
      data: {
        data: [
          {
            event_name: eventName,
            event_id: utils.CreateRandomNumbers(),
            event_time: time,
            action_source: "business_messaging",
            user_data: {
              page_id: pageId,
              page_scoped_user_id: customerId,
            },
            messaging_channel: "messenger",
            custom_data: customData,
            original_event_data: {
              event_name: eventName,
              event_time: time
            }
          }
        ]
      },
      ...proxy
    }

    return axios.request(config).then(response => response.data).catch(error => {

      delete config.httpsAgent

      pino.error({
        trace_id: traceId,
        timestamp: new Date(),
        message: 'Instagram Event Data Gönderme İşleminde Hata Oluştu',
        error: JSON.stringify(error.response?.data || { message: 'istek yapılamadı' }),
        data: JSON.stringify(config)
      })

      throw error
    })
  },
  getUserInfo: async (userAccessToken,fields=['name']) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/me?fields=${fields.join(',')}&access_token=${userAccessToken}`, { ...proxy }).then(response => response.data)
  },
  /**
   *
   * @param {string} userAccessToken
   * @returns {Promise<GetUserAdaccountsResponse>}
   */
  getUserAdaccounts: async (userAccessToken) =>{
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/me/adaccounts?fields=name,account_id,status,balance,business&limit(1000)&access_token=${userAccessToken}`, { ...proxy }).then(response => response.data)
  },
  /**
   *
   * @param {string} userAccessToken
   * @param {string} adAccountId
   * @returns {Promise<GetUserAdaccountAdsResponse>}
   */
  getUserAdAccountAds: async (userAccessToken,adAccountId) =>{ // dikkat burada act_{account_id} olması lazım. act ile başlamıyorsa sıkıntı var.
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${adAccountId}?fields=ads{name,status,campaign{name,status},creative{name,instagram_permalink_url,thumbnail_url}}&limit(1000)&access_token=${userAccessToken}`, { ...proxy }).then(response => response.data)
  }
}

module.exports = FacebookApiService

/** @typedef {object} GetUserAdaccountsResponse
 * @property {object[]} data
 * @property {string} data.name
 * @property {string} data.account_id
 * @property {string} data.balance
 * @property {object} data.business
 * @property {string} data.business.id
 * @property {string} data.business.name
 * @property {string} data.id
 * @property {object} paging
 * @property {object} paging.cursors
 * @property {string} paging.cursors.before
 * @property {string} paging.cursors.after
 */

/** @typedef {object} GetUserAdaccountAdsResponse
 * @property {string} id
 * @property {string} name
 * @property {object} ads
 * @property {'PAUSED'|'ACTIVE'} ads.status
 * @property {object[]} ads.data
 * @property {string} ads.data.name
 * @property {object} ads.data.campaign
 * @property {string} ads.data.campaign.name
 * @property {string} ads.data.campaign.id
 * @property {'PAUSED'|'ACTIVE'} ads.data.campaign.status
 * @property {object} ads.data.creative
 * @property {string} ads.data.creative.name
 * @property {string} ads.data.creative.instagram_permalink_url
 * @property {string} ads.data.creative.thumbnail_url
 * @property {string} ads.data.creative.id
 * @property {string} ads.data.id
 * @property {object} ads.paging
 * @property {object} ads.paging.cursors
 * @property {string} ads.paging.cursors.before
 * @property {string} ads.paging.cursors.after
 */


/*
Business
{
  "business": {
    "id": "***************",
    "name": "Helorobo Onboarding"
  },
  "id": "***************"
}

Get subscribed Feilds
{
    "data": [
        {
            "link": "https://www.helorobo.com/",
            "name": "Helorobo App",
            "id": "****************",
            "subscribed_fields": [
                "messages",
                "messaging_postbacks",
                "messaging_optins",
                "messaging_optouts",
                "message_deliveries",
                "message_reads",
                "messaging_payments",
                "messaging_pre_checkouts",
                "messaging_checkout_updates",
                "messaging_account_linking",
                "messaging_referrals",
                "message_echoes",
                "messaging_game_plays",
                "standby",
                "messaging_handovers",
                "messaging_policy_enforcement",
                "message_reactions",
                "inbox_labels",
                "messaging_feedback",
                "messaging_customer_information",
                "whatsapp_messages"
            ]
        }
    ]
}
 */


/*
Debug Token
{
    "data": {
        "app_id": "****************",
        "type": "USER",
        "application": "Helorobo App",
        "data_access_expires_at": **********,
        "expires_at": **********,
        "is_valid": true,
        "scopes": [
            "catalog_management",
            "pages_show_list",
            "business_management",
            "pages_messaging",
            "instagram_basic",
            "instagram_manage_comments",
            "instagram_manage_messages",
            "page_events",
            "pages_read_engagement",
            "pages_manage_metadata",
            "pages_read_user_content",
            "public_profile"
        ],
        "granular_scopes": [
            {
                "scope": "pages_show_list"
            },
            {
                "scope": "business_management"
            },
            {
                "scope": "pages_messaging"
            },
            {
                "scope": "instagram_basic"
            },
            {
                "scope": "instagram_manage_comments"
            },
            {
                "scope": "instagram_manage_messages"
            },
            {
                "scope": "page_events"
            },
            {
                "scope": "pages_read_engagement"
            },
            {
                "scope": "pages_manage_metadata"
            },
            {
                "scope": "pages_read_user_content"
            }
        ],
        "user_id": "122108096636085547"
    }
}
 */
