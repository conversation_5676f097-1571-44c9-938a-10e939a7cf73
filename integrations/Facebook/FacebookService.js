const createError = require('http-errors')

const enums = require('../../libs/enums')

const LogService = require('../../services/LogService')
const FileService = require('../../services/FileService')

const FacebookApiService = require('./FacebookApiService')

const FacebookService = {

  saveProfileImage: (user, url) => {

    return FileService.processFile(user.name + ' Facebook Profile Image', url, enums.file_ext_types.FACEBOOK_USER_IMAGE, user.ext_id, user.id, enums.ref_entity_types.USER).then(file => {

      user.profile_image_id = file._id

      return user.save()

    }).catch(error => {

      // @todo log eklenecek

      LogService.error('Facebook Profile Image kaydedilemedi: ' + error.toString(), enums.log_channels.BACKEND, {
        user_id: user.id,
        url: url
      })

      // geriye user kaydını dönelim
      return user

    })

  },

  /**
   * @param {FacebookMessageDto} dto
   *
   * @return {Promise<{type: string, content: {text: *}} | Object>}
   */
  getMessageObject: (dto) => {

    const item = dto.getItem()

    return Promise.resolve().then(() => {

      if (item.isMessageTypeQuickReply()) {

        return {
          type: enums.message_types.FACEBOOK_QUICK_REPLY,
          content: {
            text: item.getMessageText(),
            payload: item.getQuickReplyPayload()
          }
        }

      }

      if (item.isMessageTypeReplyToMessage()) {

        return {
          type: enums.message_types.FACEBOOK_REPLY_TO_MESSAGE,
          content: {
            mid: item.getMessageId(),
            text: item.getMessageText() ? item.getMessageText() : '',
            reply_to: item.getReplyToMid(),
            type: enums.message_types.TEXT
          }
        }

      }

      if (item.isMessageTypeImageAttachments()) {

        return {
          type: enums.message_types.FACEBOOK_IMAGE_URLS,
          content: {
            mid: item.getMessageId(),
            urls: item.getAttachments().map(attachment => attachment.getPayloadUrl())
          }
        }

      }

      if (item.isMessageTypeVideoAttachments()) {

        return {
          type: enums.message_types.FACEBOOK_VIDEO_URLS,
          content: {
            mid: item.getMessageId(),
            urls: item.getAttachments().map(attachment => attachment.getPayloadUrl())
          }
        }

      }

      if (item.isMessageTypeAudioAttachments()) {

        return {
          type: enums.message_types.FACEBOOK_AUDIO_URLS,
          content: {
            mid: item.getMessageId(),
            urls: item.getAttachments().map(attachment => attachment.getPayloadUrl())
          }
        }

      }

      if (item.isMessageTypeFileAttachments()) {

        return {
          type: enums.message_types.FACEBOOK_FILE_URLS,
          content: {
            mid: item.getMessageId(),
            items: item.getAttachments().map(attachment => {
              return {
                filename: attachment.getFilename(),
                url: attachment.getPayloadUrl()
              }
            })
          }
        }

      }

      if (item.isMessageTypeFallbackAttachments()) {

        return {
          type: enums.message_types.FACEBOOK_FALLBACK_URLS,
          content: {
            mid: item.getMessageId(),
            text: item.getMessageText(),
            items: item.getAttachments().map(attachment => {
              return {
                title: attachment.getPayloadTitle(),
                url: attachment.getPayloadUrl()
              }
            })
          }
        }

      }

      if (item.isMessageTypeStickerAttachments()) {

        return {
          type: enums.message_types.FACEBOOK_STICKER_URLS,
          content: {
            mid: item.getMessageId(),
            items: item.getAttachments().map(attachment => {
              return {
                url: attachment.getPayloadUrl(),
                sticker_id: attachment.getPayloadStickerId()
              }
            })
          }
        }

      }

      if (item.isMessageTypeLocationAttachments()) {

        return {
          type: enums.message_types.FACEBOOK_LOCATION_URLS,
          content: {
            mid: item.getMessageId(),
            items: item.getAttachments().map(attachment => {
              return {
                title: attachment.getPayloadTitle(),
                url: attachment.getPayloadUrl(),
                latitude: attachment.getLat(),
                longitude: attachment.getLng()
              }
            })
          }
        }

      }

      if (item.isMessageTypeProductTemplateAttachments()) {

        return {
          type: enums.message_types.FACEBOOK_PRODUCT_TEMPLATE,
          content: {
            mid: item.getMessageId(),
            text: item.getTextForIncomingMessage(),
            attachments: item.getAttachments()
          }
        }

      }

      if (item.isOpenThread()) {
        return {
          type: enums.message_types.ADS_OPEN_THREAD,
          content: {
            type: enums.message_types.ADS_OPEN_THREAD,
            mid: item.getMessageId(),
            text: item.getTextForIncomingMessage(),
            referral: item.getMessageReferral()
          }
        }
      }

      if (item.hasReferral()) {
        // bu mesajın ext_id bilgisi webhookta gelmiyor
        return {
          type: enums.message_types.ONLY_ADS_OPEN_THREAD,
          content: {
            mid: '',
            type: enums.message_types.ONLY_ADS_OPEN_THREAD,
            referral: item.getReferralData()
          }
        }
      }

      if (item.isMessageTypeTextCommand()) {
        return {
          type: enums.message_types.TEXT,
          content: {
            mid: item.getMessageId(),
            text: item.getMessageText(),
            commands: item.getMessageCommands()
          }
        }
      }

      if (item.isMessageTypeText()) {
        return {
          type: enums.message_types.TEXT,
          content: {
            mid: item.getMessageId(),
            text: item.getMessageText()
          }
        }
      }

      if (item.isIncomingMessageTypeText()) {

        return {
          type: enums.message_types.TEXT,
          content: {
            mid: item.getMessageId(),
            text: item.getTextForIncomingMessage(),
            payload: item.getTextPayloadForIncomingMessage()
          }
        }

      }

      throw new Error('IncomingMessage type not found: ' + JSON.stringify(item.getData()))

    })

  },
  /**
   * Validates scopes of specific token and facebook Page
   * @param userAccessToken User token which returned embedPopup. Either shortToken or LonglivedToken
   * @param fbPageId Faceobok Page Id
   * @param {req.t} t Translate function. required if shouldThrowError true. Default null
   * @param {boolean} hasInstagram Whether check for instagram or not
   * @param {string} igAccountId if hasInstagram true then check for granularScope for specified id
   * @returns {Promise<{hasValidToken: boolean, missingGranularScopes: string[], errorMessage: string, missingScopes: string[]}>}
   */
  validateToken: async (userAccessToken, debuggedToken, fbPageId, t, hasInstagram = false, igAccountId) => {
    const businessResponse = await FacebookApiService.getBusinessId(userAccessToken, fbPageId)

    if (!businessResponse.business) {
      throw new createError.BadRequest('You dont have any business')
    }

    if (hasInstagram && !igAccountId) {
      throw new createError.BadRequest('No IgId given')
    }

    let scopes = [
      // 'catalog_management',
      'pages_show_list',
      'business_management',
      'pages_messaging',
      'page_events',
      'pages_read_engagement',
      'pages_manage_metadata',
      'pages_read_user_content',
      'public_profile',
      'ads_management',
      'ads_read'
    ]

    let granularScopes = [
      'pages_show_list',
      'business_management',
      'pages_messaging',
      'page_events',
      'pages_read_engagement',
      'pages_manage_metadata',
      'pages_read_user_content'
    ]

    const instagramScopes = [
      'instagram_basic',
      'instagram_manage_comments',
      'instagram_manage_messages'
    ]
    if (hasInstagram) {

      scopes = [
        ...instagramScopes,
        ...scopes
      ]
      granularScopes = [
        ...instagramScopes,
        ...granularScopes
      ]
    }

    const missingScopes = []
    const missingGranularScopes = []

    scopes.forEach(scope => {
      const hasScope = debuggedToken.data.scopes.includes(scope)
      if (!hasScope) {
        missingScopes.push(scope)
      }
    })

    granularScopes.forEach(_granularScope => {

      // Check for existent
      const hasGranularScope = debuggedToken.data.granular_scopes.find(responseGranularScope => responseGranularScope.scope === _granularScope)
      if (!hasGranularScope) {
        missingGranularScopes.push(_granularScope)
        return
      }

      //# Check for specific entity id ( Page Or InstagramAccount )
      //## If instagram then check instagram permission with instagram id.
      let entityId = fbPageId
      const isInstagramScope = instagramScopes.includes(_granularScope)
      if (hasInstagram && isInstagramScope) {
        entityId = igAccountId
      }

      //## If business then check business_management
      const isBusinessScope = _granularScope === 'business_management'
      if (isBusinessScope) {
        entityId = businessResponse.business.id
      }


      const hasInCorrectGranularScopeForPageOrIg = hasGranularScope.target_ids && !hasGranularScope.target_ids.includes(entityId)
      if (hasInCorrectGranularScopeForPageOrIg) {
        missingGranularScopes.push(_granularScope)
      }

    })

    const hasInvalidToken = debuggedToken.data.is_valid === false || debuggedToken.data.error || missingScopes.length > 0 || missingGranularScopes.length > 0

    let errorMessage;
    let errorMessages = [];

    if (hasInvalidToken) {

      errorMessages.push(t(`Onboarding.errors.token_invalid_or_missing_permission`))

      if (debuggedToken.data.error?.message) {
        errorMessages.push(t(`Onboarding.errors.facebook_error_message`, { message: debuggedToken.data.error?.message }))
      }

      if (missingScopes.length > 0) {
        errorMessages.push(t(`Onboarding.errors.missing_scopes`, { scopes: missingScopes.join(',') }))
      }

      if (missingGranularScopes.length > 0) {
        errorMessages.push(t(`Onboarding.errors.missing_granular_scopes`, { granularScopes: missingGranularScopes.join(',') }))
      }
      errorMessage = errorMessages.join(' ')

    }

    return {
      hasInvalidToken,
      errorMessage,
      missingScopes,
      missingGranularScopes
    }
  },

  __handleErrors: async (err, t) => {
    const errMesages = {
      '400': { // status code
        '190': { // error code
          '460': t('Facebook.errors.190-460')// Facebook tokeni geçersiz. Kullanıcı şifresi değiştirildi ya da Facebook güvenlik sebebi ile tokeni değiştirdi. // error _subcode
        }
      }
    }
    if (err.response?.data?.error) {
      const errObject = err.response.data.error
      let errorMessage = err.response.data.error.message

      const matchedErrorMessage = errMesages[err.response.status]?.[errObject.code]?.[errObject.error_subcode]
      if (matchedErrorMessage) {
        errorMessage = matchedErrorMessage
      }
      throw new createError.BadRequest(errorMessage);
    }
  }


}

module.exports = FacebookService
