const { default: axios } = require('axios')
const pino = require('pino')()

const UrlShorterService = {

  CreateShortUrl: (url, traceId) => {
    const config = {
      url: `${process.env.URL_SHORTER_URL}/api/url-shorter`,
      method: 'POST',
      data: {
        url: url
      }
    }

    return axios.request(config).then(response => response.data.url).catch(err => {
      pino.error({
        trace_id: traceId,
        timestamp: new Date(),
        message: 'Url Kısaltma işleminde Hata Var',
        data: JSON.stringify({
          url: url
        }),
        config: JSON.stringify(config),
        error: JSON.stringify(err.response?.data || { message: 'url kısaltma işleminde domaine ulaşılamadı' })
      })

      return url
    })
  }
}

module.exports = UrlShorterService