const enums = require('../libs/enums')

const Chat = require('../models/Chat')
const Channel = require('../models/Channel')
const Message = require('../models/Message')

/**
 * @param channelType
 * @param channelProvider
 * @param channelExtId
 * @param messageExtId
 * @param chatExtId
 *
 * @return {Promise<void>}
 */
module.exports = async (channelType, channelProvider, channelExtId, chatExtId, messageExtId) => {

  const channel = await Channel.findOne({
    type: channelType,
    provider: channelProvider,
    ext_id: channelExtId,
    is_active: true,
    deleted_at: {
      $exists: false
    }
  })

  if ( ! channel) {
    return
  }

  const chat = await Chat.findOne({
    channel_id: channel._id,
    ext_id: chatExtId,
  })

  if ( ! chat) {
    return
  }

  const message = await Message.findOne({
    conversation_id: chat._id,
    ext_id: messageExtId
  })

  if ( ! message) {
    return
  }

  message.status = enums.message_send_statuses.SENT

  return message.save()

}
