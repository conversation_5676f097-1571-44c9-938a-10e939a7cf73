const moment = require('moment')

const enums = require('../libs/enums')

const Chat = require('../models/Chat')
const User = require('../models/User')
const Channel = require('../models/Channel')
const Message = require('../models/Message')

const QueueService = require('../services/QueueService')

/**
 * @param channelType
 * @param channelProvider
 * @param channelExtId
 * @param messageExtId
 * @param chatExtId
 * @param language
 *
 * @return {Promise<void>}
 */
module.exports = async (channelType, channelProvider, channelExtId, chatExtId, messageExtId, language, seenTime = undefined) => {

  const channel = await Channel.findOne({
    type: channelType,
    provider: channelProvider,
    ext_id: channelExtId,
    is_active: true,
    deleted_at: {
      $exists: false
    }
  })

  if (!channel) {
    return
  }

  const chat = await Chat.findOne({
    channel_id: channel._id,
    ext_id: chatExtId,
  })

  if (!chat) {
    return
  }

  let message = null
  if (channel.type === enums.channel_types.FACEBOOK_PAGE) {
    await Message.updateMany({
      conversation_id: chat._id,
      status: enums.message_send_statuses.SENT,
      created_at: {
        $lte: moment.unix(seenTime).toDate()
      }
    }, {
      status: enums.message_send_statuses.SEEN
    })

    message = await Message.findOne({
      conversation_id: chat._id,
    }).populate('conversation_id').populate('user_id').sort({ _id: -1 })
  } else {
    message = await Message.findOne({
      conversation_id: chat._id,
      ext_id: messageExtId
    }).populate('conversation_id').populate('user_id')

    if (!message) {
      return
    }

    message.status = enums.message_send_statuses.SEEN

    await message.save()
  }

  // chat için agent varsa bildirim göndereceğiz
  if (chat.owner_user_id) {

    const agent = await User.findById(chat.owner_user_id)

    const eventData = {
      conversation_id: chat.id,
      message_id: message.id
    }

    if (channel.type === enums.channel_types.FACEBOOK_PAGE) {
      eventData.watermark = seenTime
    }

    // Mesajın görüldüğüne dair bilgi soketten gönderiliyor
    QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.MESSAGE_SEEN,
      socket_rooms: [agent.vSocketCode],
      data: eventData
    }, language)

  }

  return message

}
