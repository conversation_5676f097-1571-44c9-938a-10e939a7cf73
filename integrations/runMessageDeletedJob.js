const enums = require('../libs/enums')

const DashPresenter = require('../presenters/Dash')

const Chat = require('../models/Chat')
const User = require('../models/User')
const Channel = require('../models/Channel')
const Message = require('../models/Message')

const QueueService = require('../services/QueueService')

/**
 * @param channelType
 * @param channelProvider
 * @param channelExtId
 * @param messageExtId
 * @param chatExtId
 * @param language
 *
 * @return {Promise<void>}
 */
module.exports = async (channelType, channelProvider, channelExtId, chatExtId, messageExtId, language) => {

  const channel = await Channel.findOne({
    type: channelType,
    provider: channelProvider,
    ext_id: channelExtId,
    is_active: true,
    deleted_at: {
      $exists: false
    }
  })

  if ( ! channel) {
    return
  }

  const chat = await Chat.findOne({
    channel_id: channel._id,
    ext_id: chatExtId,
  })

  if ( ! chat) {
    return
  }

  const message = await Message.findOne({
    conversation_id: chat._id,
    ext_id: messageExtId
  }).populate('conversation_id').populate('user_id')

  if ( ! message) {
    return
  }

  message.deleted_at = Date.now()
  message.status = enums.message_send_statuses.DELETED

  await message.save()

  // chat için agent varsa bildirim göndereceğiz
  if (chat.owner_user_id) {

    const agent = await User.findById(chat.owner_user_id)

    QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.MESSAGE_DELETED,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_id: chat.id,
        message_item: await DashPresenter.getRepliedMessage(message),
      }
    }, language)

  }

  return message
}
