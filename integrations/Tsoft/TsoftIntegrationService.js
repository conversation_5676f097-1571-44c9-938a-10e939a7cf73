const axios = require('axios')
const moment = require('moment')
const createError = require('http-errors')
const pino = require('pino')()

const enums = require('../../libs/enums')
const helpers = require('../../libs/helpers')

const getTsoftRequestConfig = (req, action, integration, chatIntegration, data, chatLangCode) => {
  try {
    let language = 'tr'

    if (chatIntegration) {
      language = chatIntegration.vData.hasIntegrationLangCode() ? chatIntegration.vData.getIntegrationLangCode() : 'tr' // daha sonra servis katmanından alınacak
      data.currency_id = chatIntegration.vData.getIntegrationCurrencyCodeId() || integration.vData.getDefaultCurrencyCode()
      data.currency_code = chatIntegration.vData.getIntegrationCurrencyCodeId() || integration.vData.getDefaultCurrencyCode()

      if (action !== enums.TSOFT_ACTIONS.ADD_CUSTOMER_ADDRESS) {
        data.language = language
      }

      if (action === enums.TSOFT_ACTIONS.GET_CARGO_OPTIONS) {
        data.currency_code = chatIntegration.vData.getIntegrationCurrency()
      }

      if (action === enums.TSOFT_ACTIONS.CREATE_ORDE2R) {
        data.currency_code = chatIntegration.vData.getIntegrationCurrency()
      }

      if ([enums.TSOFT_ACTIONS.GET_PRODUCTS, enums.TSOFT_ACTIONS.GET_PRODUCT, enums.TSOFT_ACTIONS.GET_CATALOG, enums.TSOFT_ACTIONS.GET_CART].includes(action)) {
        data.currency = chatIntegration.vData.getIntegrationCurrency()

        delete data.currency_id
        delete data.currency_code
      }
    }

    return {
      url: process.env.INTEGRATION_BASE_URL + `/process`,
      method: 'POST',
      headers: {
        "X-Lang-Code": language
      },
      data: {
        type: enums.INTEGRATION_TYPES.TSOFT,
        action: action,
        data: data,
        extra: {
          token: chatIntegration ? chatIntegration.vData.getTsoftToken() : integration.token.token,
          base_url: integration.data.base_url,
          language: language,
          trace_id: req.trace_id,
          calculate_vat0_on_international_invoices: integration.data.calculate_vat0_on_international_invoices || false
        },
        chat_lang_code: chatLangCode
      }
    }

  } catch (error) {
    pino.error({
      trace_id: req.trace_id,
      message: error.message,
      data: JSON.stringify({
        action: action,
        data: data
      })
    })

    throw error
  }

}

const isValidToken = (req, integration, chatIntegration) => {

  return Promise.resolve().then(() => {

    if (!chatIntegration?.vData?.getTsoftToken()) {
      return false
    }

    if (!chatIntegration?.vData?.getTsoftTokenExpiredAt()) {
      return false
    }

    let expiresAt = moment(chatIntegration?.vData?.getTsoftTokenExpiredAt()).subtract(6, 'hours')

    return expiresAt.isAfter(moment())

  }).then(valid => {

    if (valid) {
      return chatIntegration
    }

    return TsoftIntegrationService.login(req, integration, chatIntegration)

  })
}

const TsoftIntegrationService = {

  process: (req, integration, chatIntegration, action, data = {}, extId = undefined, chatLangCode) => {

    data = JSON.parse(JSON.stringify(typeof data === 'object' ? data : {}))

    if (!integration) {
      throw new createError.NotFound(req.t('Global.errors.integration.not_found'))
    }

    return isValidToken(req, integration, chatIntegration).then(chatIntegration => {

      return Promise.resolve().then(() => {

        if (extId) {

          // kullanıcıya ait entegrasyon ext id bilgisini sürece dahil edelim
          data.customer_id = extId

        }

      }).then(() => {

        return axios.request(getTsoftRequestConfig(req, action, integration, chatIntegration, data, chatLangCode)).then(response => {

          pino.info({
            trace_id: req.trace_id,
            timestamp: new Date(),
            data: JSON.stringify(data),
            action: action,
            message: `${extId ? extId + ' üye' : 'üyeliksiz'} tsoft cevabı`,
            response: JSON.stringify(response.data)
          })
          return response.data

        }).catch(error => {

          pino.error({
            trace_id: req.trace_id,
            timestamp: new Date(),
            data: JSON.stringify(data),
            action: action,
            message: `${extId ? extId + ' üye' : 'üyeliksiz'} tsoft hatası`,
            error: JSON.stringify(error.response?.data || { message: 'İstek Atılamadı' }),
          })

          return TsoftIntegrationService.checkTokenErrorAndReLogin(req, error, action, integration, chatIntegration, data).then(response => {

            return response.data

          })

        }).catch(error => {

          pino.error({
            trace_id: req.trace_id,
            timestamp: new Date(),
            data: JSON.stringify(data),
            action: action,
            message: `${extId ? extId + ' üye' : 'üyeliksiz'} tsoft hatası`,
            error: JSON.stringify(error.response?.data || { message: 'İstek Atılamadı' }),
          })

          return TsoftIntegrationService.handleUserNotFound(req, error, action, chatIntegration, extId)

        }).catch(error => {

          pino.error({
            trace_id: req.trace_id,
            timestamp: new Date(),
            data: JSON.stringify(data),
            action: action,
            message: `${extId ? extId + ' üye' : 'üyeliksiz'} tsoft hatası`,
            error: JSON.stringify(error.response?.data || { message: 'İstek Atılamadı' }),
          })
          return helpers.handleAxiosError(error)

        })


      })


    })

  },

  login: (req, integration, chatIntegration) => {

    let decryptedPassword = helpers.decrypt(process.env.APP_SECRET_KEY, integration.data.username, integration.data.password)

    return TsoftIntegrationService.loginRaw(req, integration.data.base_url, integration.data.username, decryptedPassword).then(async response => {

      if (chatIntegration) {
        const chatIntegrationData = chatIntegration.vData
        chatIntegrationData.setTsoftToken(response.token)
        chatIntegrationData.setTsoftTokenExpiredAt(response.expires_at)
        chatIntegration.data = chatIntegrationData.getData()
        chatIntegration.markModified('data')
        return chatIntegration.save()
      } else {
        integration.token = {
          token: response.token,
          expires_at: response.expires_at
        }

        await integration.save()
        return null
      }

    })

  },

  loginRaw: (req, baseUrl, username, password) => {

    let config = {
      url: process.env.INTEGRATION_BASE_URL + `/process`,
      method: 'POST',
      data: {
        type: enums.INTEGRATION_TYPES.TSOFT,
        action: 'LOGIN',
        data: {
          username: username,
          password: password,
        },
        extra: {
          base_url: baseUrl,
        }
      }
    }

    pino.info({
      trace_id: req.trace_id,
      timestamp: new Date(),
      data: config,
      message: 'tsoft tarafına tekrar login olunuyor',
    })

    return axios.request(config).then(response => {

      pino.info({
        trace_id: req.trace_id,
        timestamp: new Date(),
        data: JSON.stringify(response.data),
      })

      if (!response.data) {
        pino.error({
          trace_id: req.trace_id,
          config: JSON.stringify(config),
          timestamp: new Date(),
          message: 'İlgili siteye giriş yapılamadı: ' + baseUrl,
        })
        throw new createError.BadRequest('İlgili siteye giriş yapılamadı: ' + baseUrl)
      }

      return {
        token: response.data.data.data[0].token,
        expires_at: moment(response.data.data.data[0].expirationTime, 'DD-MM-YYYY HH:mm:ss').format()
      }

    }).catch(helpers.handleAxiosError)

  },

  /**
   *  Token hatasımı kontrol eder. Hata:
   *  Token Hatası => Tekrar login olup integration daki tokeni günceller, isteği yeni token ile tekrarlar.
   *  ! Token Hatası =>  handleAxois() fonksiyonu çalıştırır
   */
  checkTokenErrorAndReLogin: (req, error, action, integration, chatIntegration, data) => {

    if (error.response && error.response.data && error.response.data.code === enums.ERRORS.INVALID_TOKEN) {

      pino.info({
        trace_id: req.trace_id,
        timestamp: new Date(),
        data: data,
        action: action,
        message: `TsoftIntegrationService::checkTokenErrorAndReLogin tekrardan login olunuyor`,
        response: error.response.data
      })

      return TsoftIntegrationService.login(req, integration, chatIntegration).then(integration => {

        return axios.request(getTsoftRequestConfig(req, action, integration, chatIntegration, data)).then(response => {

          return response.data

        })

      })

    }

    throw error

  },

  handleUserNotFound: async (req, error, action, chatIntegration, extId) => {

    if (error.response?.data?.message[0]?.code === 'CUE009' && extId) {
      pino.info({
        trace_id: req.trace_id,
        timestamp: new Date(),
        action: action,
        data: JSON.stringify({
          ext_id: extId,
          chat_id: chatIntegration.chat_id.toString(),
          integration_id: chatIntegration.integration_id.toString()
        }),
        message: `TsoftIntegrationService::handleUserNotFound kullancı bulunamadı entegrasyondan silinecek.`,
        response: JSON.stringify(error.response.data)
      })

      chatIntegration.ext_id = undefined
      await chatIntegration.save()
    }

    throw error

  },

}

module.exports = TsoftIntegrationService
