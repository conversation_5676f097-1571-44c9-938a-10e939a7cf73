const pino = require('pino')()

const enums = require('../../../libs/enums')

const QueueService = require('../../../services/QueueService')
const ChatService = require('../../../services/ChatService')

const SendAddressMessageTsoftForInvoiceAddress = require('../../../integrations/Tsoft/BotAction/SendAddressMessageForInvoiceAddress')

const IsSameDeliveryAndInvoiceAddressForWhatapp = require('../../../integrations/Tsoft/BotAction/Whatsapp/IsSameDeliveryAndInvoiceAddress')
const IsSameDeliveryAndInvoiceAddressForLivechat = require('../../../integrations/Tsoft/BotAction/Livechat/IsSameDeliveryAndInvoiceAddress')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const SendCargoOptionsForTsoft = require('./SendCargoOptions')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return IsSameDeliveryAndInvoiceAddressForWhatapp(req, chat, integration, chatIntegration)
  }

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.LIVE_CHAT) {
    return IsSameDeliveryAndInvoiceAddressForLivechat(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  const agent = botData.agent

  const botMessageData = {
    message_type: '',
    message_data: ''
  }

  switch (parseInt(botData.customer_message.content.text)) {

    //Müşteri 1 e basarsa Fatura Adresini, Teslimat Adresi ile Aynı seçmek istemiştir
    case 1:

      // Stage bilgisi ve teslimat adresi bilgisi bizim tarafta kaydediliyor
      const chatIntegrationData = chatIntegration.vData

      chatIntegrationData.setInvoiceAddressId(chatIntegrationData.getDeliveryAddressId())
      chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_SELECT_ADDRESS)

      chatIntegration.data = chatIntegrationData.getData()

      chatIntegration.markModified('data')

      await chatIntegration.save()

      // Soket üzerinden fatura adresi seçildiğine dair bilgisi gönderiliyor
      await QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.INVOICE_ADDRESS_SELECTED,
        socket_rooms: [agent.vSocketCode],
        data: {
          chat_id: chat.id,
          integration_id: integration.id,
          invoice_address_id: chatIntegrationData.getDeliveryAddressId()
        }
      }, req.language)

      // Soket üzerinden stage bilgisi gönderiliyor
      await QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
        socket_rooms: [agent.vSocketCode],
        data: {
          chat_id: chat.id,
          integration_id: integration.id,
          stage: enums.ORDER_STAGES.STAGE_SELECT_ADDRESS
        }
      }, req.language)

      // Kargo seçeneklerine dair bilgi alınıyor.
      const messageData = await SendCargoOptionsForTsoft(req, chat, integration, chatIntegration, chatIntegration.ext_id)

      botMessageData.message_type = enums.message_types.TEXT
      botMessageData.message_data = {
        text: req.t('App.integration.send_cargo_message', {
          cargo_options: messageData.cargo_names,
          interpolation: { escapeValue: false }
        }),
        next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_CARGO,
        agent_id: agent.id,
        language: req.language,
        bot_data: messageData.cargo_dto,
        bb_code: true,
      }

      await ChatService.addAgentMessage(req, chat.id, botMessageData.message_type, botMessageData.message_data, agent.id, undefined, { mark_as_seen_event: true })
      break

    // Müşteri 2 seçerse fatura adresi bilgileri gönderilecek
    case 2:

      // Müşteriye address bilgilerini gönderiyoruz
      const messageAddressData = await SendAddressMessageTsoftForInvoiceAddress(req, chat, integration, chatIntegration, chatIntegration.ext_id)

      botMessageData.message_type = enums.message_types.TEXT
      botMessageData.message_data = {
        text: req.t('App.success.integration.customer_invoice_address_message', {
          interpolation: { escapeValue: false }
        }) + messageAddressData.message_data,
        next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_INVOICE_ADDRESS,
        agent_id: agent.id,
        language: req.language,
        bot_data: messageAddressData.address_dto.getBotData(),
        bb_code: true,
        hide_image: true,
      }
      await ChatService.addAgentMessage(req, chat.id, botMessageData.message_type, botMessageData.message_data, agent.id, undefined, { mark_as_seen_event: true })
      break

    default:
      return
  }

  pino.info({
    trace_id: req.trace_id,
    integration_id: integration.id,
    chat_id: chat.id,
    channel_id: chat.channel.id,
    bot_message_data: JSON.stringify(botMessageData),
    stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.IS_SAME_DELIVERY_AND_INVOICE_ADDRESS
  })
}