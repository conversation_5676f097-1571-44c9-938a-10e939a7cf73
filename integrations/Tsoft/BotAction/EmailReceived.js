const pino = require('pino')()

const enums = require('../../../libs/enums')
const helpers = require('../../../libs/helpers')

const User = require('../../../models/User')

const QueueService = require('../../../services/QueueService')
const ChatService = require('../../../services/ChatService')

const SendAddressMessageTsoftForDeliveryAddress = require('../../../integrations/Tsoft/BotAction/SendAddressMessageForDeliveryAddress')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  const agent = botData.agent

  //Email uygun mu diye Kontrol Edildi. Yoksa tekrar emailini almak için mesaj gönderdik
  const status = helpers.checkEmail(botData.customer_message.content.text.toLowerCase())
  if ( ! status) {

    const botEmailData = {
      message_type: enums.message_types.TEXT,
      message_data: {
        text: req.t('App.success.ask_form_questions.email'),
        next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.EMAIL_RECEIVED,
        agent_id: agent.id,
        language: req.language,
      }
    }
    await ChatService.addAgentMessage(req, chat.id, botEmailData.message_type, botEmailData.message_data, agent.id, undefined, { mark_as_seen_event: true })

    pino.info({
      trace_id: req.trace_id,
      integration_id: integration.id,
      chat_id: chat.id,
      channel_id: chat.channel.id,
      company_id: integration.company_id.toString(),
      bot_message_data: JSON.stringify(botEmailData),
      stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.EMAIL_RECEIVED
    })

    return
  }

  // Müşterinini bize göndermiş olduğu email i kendi tarafımıza kaydediyoruz
  chat.email = botData.customer_message.content.text.toLowerCase()
  await chat.save()

  // Müşteri bilgilerini güncellendiğine dair soketten bilgi gönderiyoruz
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CUSTOMER_DATA_UPDATED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
    }
  }, req.language)

  // Address bilgilerini alıyoruz
  const messageData = await SendAddressMessageTsoftForDeliveryAddress(req, chat, integration, chatIntegration, chatIntegration.ext_id)

  // adresleri yok demektir. burdan itibaren devam etmeyecek
  if (typeof messageData === 'boolean') {
    return
  }

  let messageType
  let messageContent

  switch (chat.channel.type) {

    case enums.channel_types.WHATSAPP_NUMBER:

      messageType = enums.message_types.WHATSAPP_INTERACTIVE
      messageContent = {
        sub_type: enums.message_types.LIST,
        text: req.t('App.success.integration.customer_address_message'),
        bb_code: true,
        buttons: [{
          title: req.t('Global.chat_message.select_address'),
          rows: messageData.whatsapp_message_data
        }]
      }

      break

    default:

      messageType = enums.message_types.TEXT
      messageContent = {
        text: req.t('App.success.integration.customer_address_message', {
          interpolation: { escapeValue: false }
        }) + messageData.message_data,
        bb_code: true,
        bot_data: messageData.address_dto,
        hide_image: true
      }

      break
  }

  messageContent.next_action = enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS
  messageContent.agent_id = agent.id
  messageContent.language = req.language

  // Müşteriye adress bilgilerini gönderiyoruz
  await ChatService.addAgentMessage(req, chat._id, messageType, messageContent, agent.id, undefined, { mark_as_seen_event: true })

  pino.info({
    trace_id: req.trace_id,
    integration_id: integration.id,
    chat_id: chat.id,
    channel_id: chat.channel.id,
    company_id: integration.company_id.toString(),
    bot_message_data: JSON.stringify({
      message_type: messageType,
      message_data: messageContent
    }),
    stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS
  })
}
