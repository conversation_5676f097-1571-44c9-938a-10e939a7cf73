const pino = require('pino')()

const enums = require('../../../libs/enums')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')

const TsoftAgentAppService = require('../../../integrations/Tsoft/AgentApp/TsoftService')

const AddToCartDto = require('../../../dtos/AddToCartDto')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const SelectVariant2ForWhatapp = require('./Whatsapp/SelectVariant2')
const SelectVariant2ForLivechat = require('./Livechat/SelectVariant2')

const getAddToCartData = (customerLastMessage, botData, item) => {

  let data = {}

  item.variant_data.variants.forEach(item => {

    if (item.option1 == botData.variant_name && item.option2 == getOption2(customerLastMessage, botData)) {

      data = {
        product_id: botData.product_id,
        variant_cid: item.variant_id,
      }

    }

  })

  return data

}

const getOption2 = (customerLastMessage, botData) => {

  let option2 = ''

  botData.variants.forEach(variant => {
    if (variant.index == customerLastMessage.vContentText) {
      option2 = variant.name
    }
  })

  return option2

}

const getItemName = (item, botData, customerLastMessage) => {
  return item.title + ' (' + botData.variant_name + ' ' + getOption2(customerLastMessage, botData) + ')'
}

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return SelectVariant2ForWhatapp(req, chat, integration, chatIntegration)
  }

  // LIVE_CHAT için burası kullanılacak
  if (chat.channel.type === enums.channel_types.LIVE_CHAT) {
    return SelectVariant2ForLivechat(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  const agent = botData.agent

  // ID bilgisine göre ürün tsoft tarafından alınıyor.
  const getProductResponse = await TsoftAgentAppService.getTsoftProduct(req, integration, chatIntegration, {
    product_id: botData.bot_data.product_id,
    fetch_product_detail: true
  }, chat.vData.getChatLangCode())

  // Mesaj içerisinden ürün ve variant bilgileri alınıyor
  const data = getAddToCartData(botData.customer_message, botData.bot_data, getProductResponse.item)

  const addToCartDto = new AddToCartDto()

  addToCartDto.setChat(chat)
  addToCartDto.setIntegration(integration)
  addToCartDto.setData(data)
  addToCartDto.setExtId(chatIntegration.ext_id)
  addToCartDto.setItemName(getItemName(getProductResponse.item, botData.bot_data, botData.customer_message))
  addToCartDto.setAgentId(agent.id)
  addToCartDto.setChatIntegration(chatIntegration)

  // Üyelikli veya üyeliksiz olarak tsofta göre sepete ürün ekleme işlemi yapılıyor
  const messageData = await TsoftAgentAppService.addToCart(req, addToCartDto, chat.vData.getChatLangCode())

  // Müşteriye sepetine ürün eklendiğine dair mesaj gönderiliyor
  await ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
    text: req.t('App.success.integration.add_to_cart', {
      item_name: messageData.item_name,
      interpolation: { escapeValue: false },
    })
  }, agent.id, undefined, { mark_as_seen_event: true })

  // Müşteri stage bilgisi güncelleniyor.
  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_ADD_TO_CART)

  chatIntegration.user_data = chatIntegrationData.getData()
  chatIntegration.markModified('chat_integration')

  await chatIntegration.save()

  // Soket üzerinden sepete ürün eklendiğine dair mesaj iletiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ITEM_ADDED_TO_CART,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      item_name: getProductResponse.item.title,
    }
  }, req.language)

  // Müşteri bilgisi soket üzerinden gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_ADD_TO_CART
    }
  }, req.language)

  pino.info({
    trace_id: req.trace_id,
    integration_id: integration.id,
    chat_id: chat.id,
    channel_id: chat.channel.id,
    company_id: integration.company_id.toString(),
    bot_message_data: JSON.stringify(messageData),
    stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.STAGE_ADD_TO_CART
  })

}
