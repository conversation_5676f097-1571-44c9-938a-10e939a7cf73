const moment = require('moment')
const pino = require('pino')()

const enums = require('../../../libs/enums')
const utils = require('../../../libs/utils')

const ChatService = require('../../../services/ChatService')
const TsoftIntegrationService = require('../../../integrations/Tsoft/TsoftIntegrationService')

const SendOrderStatusMessage = require('./Whatsapp/SendOrderStatusMessage')
const SendOrderStatusMessageLivechat = require('./Livechat/SendOrderStatusMessage')
const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

module.exports = async (req, chat, integration, chatIntegration) => {

  if (!chat.channel.is_active) {
    return
  }

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return SendOrderStatusMessage(req, chat, integration, chatIntegration)
  }

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.LIVE_CHAT) {
    return SendOrderStatusMessageLivechat(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  const integrationData = integration.vData

  let orderCode = ''
  if (chatIntegration.ext_id) {

    const getOrdersResponse = await TsoftIntegrationService.process(req, integration, chatIntegration, enums.TSOFT_ACTIONS.GET_ORDERS, {
      currency: 'USD'
    }, chatIntegration.ext_id, chat.vData.getChatLangCode())

    if (getOrdersResponse.data.data.length > 0) {
      orderCode = getOrdersResponse.data.data[0].OrderCode
    }

  } else {
    orderCode = chatIntegration.vData.getLastOrderCode()
  }

  if (!orderCode) {
    await ChatService.addSystemMessage(req, chat, enums.message_types.TEXT, { text: req.t('App.integration.order_not_found_message') }, { mark_as_seen_event: true })

    pino.info({
      trace_id: req.trace_id,
      integration_id: integration.id,
      chat_id: chat.id,
      channel_id: chat.channel.id,
      bot_message_data: JSON.stringify({
        message_type: enums.message_types.TEXT,
        message_data: { text: req.t('App.integration.order_not_found_message') }
      }),
      stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.CREATE_CART
    })
    return
  }

  const getOrderResponse = await TsoftIntegrationService.process(req, integration, chatIntegration, enums.TSOFT_ACTIONS.GET_ORDER, {
    order_code: orderCode,
    fetch_cargo_details: true,
    fetch_shipment_details: true,
  })

  const sendable = await ChatService.checkSendable(chat.id, chat.channel.type)

  if (!sendable.sendable) {
    throw req.t('App.errors.dash.conversation_active_time_expired')
  }

  let caption = req.t('App.integration.send_order_status_message', {
    customer_name: chat.title,
    order_code: getOrderResponse.order.order_code,
    order_date: moment(getOrderResponse.order.update_date).format('DD.MM.YYYY'),
    order_total_price: utils.getCurrencyForIntlNumberFormat(getOrderResponse.order.total_price, getOrderResponse.order.currency_code, integrationData.getPricePrecision()),
    order_status: getOrderResponse.order.order_status,
    cargo_option_name: getOrderResponse.order.cargo_option_name,
    cargo_tracking_code: getOrderResponse.order.cargo_tracking_code,
    cargo_tracking_url: getOrderResponse.order.cargo_tracking_url,
    interpolation: { escapeValue: false }
  })

  let messageType
  let messageContent

  switch (chat.channel.type) {

    case enums.channel_types.WHATSAPP_NUMBER:

      messageType = enums.message_types.IMAGE_URL
      messageContent = {
        caption: caption,
        url: enums.cargo_message_image_url,
        hide_image: false,
        bb_code: true,
        next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.CREATE_CART,
        agent_id: chat.owner_user_id,
        language: req.language
      }
      break

    default:

      messageType = enums.message_types.TEXT
      messageContent = {
        text: caption,
        bb_code: true
      }
      break

  }

  await ChatService.addSystemMessage(req, chat, messageType, messageContent, { mark_as_seen_event: true })

  pino.info({
    trace_id: req.trace_id,
    integration_id: integration.id,
    chat_id: chat.id,
    channel_id: chat.channel.id,
    bot_message_data: JSON.stringify({
      message_type: messageType,
      message_data: messageContent
    }),
    stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.CREATE_CART
  })

}
