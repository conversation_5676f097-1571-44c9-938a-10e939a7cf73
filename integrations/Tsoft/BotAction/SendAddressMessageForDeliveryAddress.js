const enums = require('../../../libs/enums')
const utils = require('../../../libs/utils')

const ChatService = require('./../../../services/ChatService')

const TsoftIntegrationService = require('../../../integrations/Tsoft/TsoftIntegrationService')
const SendAddressMessageDto = require('../../../dtos/BotAction/SendAddressMessageDto')

const SendAddressMessageForDeliveryAddress = async (req, chat, integration, chatIntegration, extId) => {

  const addresses = []
  const messageCaptions = []
  const messageForWhatsapp = []
  const messageForLivechat = []

  const chatIntegrationData = chatIntegration.vData

  const sendAddressMessageDto = new SendAddressMessageDto()

  // Üyeliksiz Müşteri için adressler bizim tarafımızdan mesaja göre formatlanıyor.
  if (!extId) {

    //Kullanıcının paylaşılacak adresi yoksa
    if (chatIntegrationData.getGuestAddresses().length === 0) {
      await ChatService.addSystemMessage(req, chat, enums.message_types.TEXT, {
        text: req.t('App.integration.send_empty_address_message')
      }, { mark_as_seen_event: true })

      return false
    }

    //misafir kullanıcın adres bilgilerini geziyoruz
    chatIntegrationData.getGuestAddresses().forEach(item => {

      //Botdata da kullanmak için sakladık.
      addresses.push({
        address_id: item.id,
        address_index: addresses.length + 1
      })

      let title = req.t('Global.form_field.address1')

      messageCaptions.push(req.t('App.integration.send_address_message_caption_address', {
        emoji: utils.getMessageEmoji(addresses.length, chat.channel.type),
        address: item.data.address,
      }))

      if (item.extra.city_name && item.extra.town_name) {
        messageCaptions[messageCaptions.length - 1] += req.t('App.integration.send_address_message_caption_city', {
          city: item.extra.city_name,
          town: item.extra.town_name,
        })
        title = `${item.extra.city_name} / ${item.extra.town_name}`
      }

      messageForWhatsapp.push({
        id: item.id.toString(),
        title: title.substring(0, 23),
        description: item.data.address.substring(0, 71),
      })

      messageForLivechat.push({
        id: item.id.toString(),
        text: title.substring(0, 23) + " - " + item.data.address.substring(0, 71),
      })

    })

    sendAddressMessageDto.setBotData(addresses)

    return {
      address_dto: sendAddressMessageDto.getBotData(),
      message_data: messageCaptions.join('[BR][/BR]'),
      whatsapp_message_data: messageForWhatsapp,
      livechat_message_data: messageForLivechat
    }

  }

  // Üyelikli Müşteri addresleri Tsoft tarafından alınıyor
  const getCustomerAddressesResponse = await TsoftIntegrationService.process(req, integration, chatIntegration, enums.TSOFT_ACTIONS.GET_CUSTOMER_ADDRESSES, {}, extId, chat.vData.getChatLangCode())

  if (getCustomerAddressesResponse.items.length === 0) {
    await ChatService.addSystemMessage(req, chat, enums.message_types.TEXT, {
      text: req.t('App.integration.send_empty_address_message')
    }, { mark_as_seen_event: true })

    return false
  }

  getCustomerAddressesResponse.items.forEach(item => {

    addresses.push({
      address_id: item.id,
      address_index: addresses.length + 1
    })

    messageCaptions.push(req.t('App.integration.send_address_message_caption_address', {
      emoji: utils.getMessageEmoji(addresses.length, chat.channel.type),
      address: item.address,
    }))

    let title = req.t('Global.form_field.address1')
    if (item.city_name && item.town_name) {
      messageCaptions[messageCaptions.length - 1] += req.t('App.integration.send_address_message_caption_city', {
        city: item.city_name,
        town: item.town_name,
      })

      // Whatsapp List Mesaj için data alınıyor
      title = `${item.city_name} / ${item.town_name}`
    }

    messageForWhatsapp.push({
      id: item.id.toString(),
      title: title.substring(0, 23),
      description: item.address.substring(0, 71),
    })

    messageForLivechat.push({
      id: item.id.toString(),
      text: title.substring(0, 23) + " - " + item.address.substring(0, 71),
    })

  })

  sendAddressMessageDto.setBotData(addresses)

  return {
    address_dto: sendAddressMessageDto.getBotData(),
    message_data: messageCaptions.join('[BR][/BR][BR][/BR]'),
    whatsapp_message_data: messageForWhatsapp,
    livechat_message_data: messageForLivechat
  }

}

module.exports = SendAddressMessageForDeliveryAddress
