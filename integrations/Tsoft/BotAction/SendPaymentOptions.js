const createError = require('http-errors')

const utils = require('../../../libs/utils')

const SendPaymentOptions = require('../../../dtos/BotAction/SendPaymentOptions')

const ChatService = require('../../../services/ChatService')
const IntegrationService = require('../../../modules/AgentApp/IntegrationService')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajın gönderilebilirliğini kontrol ediyor.
  const sendable = await ChatService.checkSendable(chat.id, chat.channel.type)

  if (!sendable.sendable) {
    throw new createError.BadRequest(req.t('App.errors.dash.conversation_active_time_expired'))
  }

  const getCargoOptionsResponse = await IntegrationService.getCargoOptions(req, integration, chatIntegration, undefined, chatIntegration.ext_id, chat.vData.getChatLangCode())

  const payments = []
  const paymentOptionsTexts = []
  let paymentOptionsForWhatsapp = []
  let paymentOptionsForLivechat = []

  let index = 0

  // Bizde kayıtlı olan kargo bilgisi ile tsofttan gelen arasındaki seçilen bulduruluyor
  const cargoOption = getCargoOptionsResponse.item.cargo_options.find(item => item.id == chatIntegration.vData.getCargoOptionId())

  if (!cargoOption) {
    throw new createError.BadRequest('App.errors.integration.once_cargo')
  }

  for (const paymentOption of cargoOption.payment_options) {

    const childs = []

    if (paymentOption.childs.length > 0) {

      paymentOption.childs.forEach(child => {

        paymentOptionsTexts.push(utils.getMessageEmoji(++index, chat.channel.type) + ' [B]' + paymentOption.name + ' / ' + child.name + ` (${paymentOption.amount_value} ${getCargoOptionsResponse.item.currency_code})` + '[/B]')

        childs.push({
          child_id: child.id,
          child_index: index
        })

        let title = `${paymentOption.name} / ${child.name}`

        paymentOptionsForWhatsapp.push({
          id: `${paymentOption.id}/${child.id}`,
          title: title.substring(0, 23),
          description: `${paymentOption.amount_value} ${getCargoOptionsResponse.item.currency_code}`
        })

        paymentOptionsForLivechat.push({
          id: `${paymentOption.id}/${child.id}`,
          text: title.substring(0, 23)
        })
      })

    } else {

      paymentOptionsTexts.push(utils.getMessageEmoji(++index, chat.channel.type) + ' [B]' + paymentOption.name + ` (${paymentOption.amount_value} ${getCargoOptionsResponse.item.currency_code})` + '[/B]')

      paymentOptionsForWhatsapp.push({
        id: paymentOption.id.toString(),
        title: paymentOption.name.substring(0, 23),
        description: `${paymentOption.amount_value} ${getCargoOptionsResponse.item.currency_code}`
      })

      paymentOptionsForLivechat.push({
        id: paymentOption.id.toString(),
        text: paymentOption.name.substring(0, 23),
      })
    }

    payments.push({
      payment_id: paymentOption.id,
      payment_index: index,
      childs: childs
    })
  }

  const sendPaymentOptions = new SendPaymentOptions()
  sendPaymentOptions.setBotData(payments)

  return {
    payment_dto: sendPaymentOptions.getBotData(),
    message_data: paymentOptionsTexts.join('[BR][/BR]'),
    payments_whatsapp: paymentOptionsForWhatsapp,
    payments_livechat: paymentOptionsForLivechat,
  }

}
