const createError = require('http-errors')

const utils = require('../../../libs/utils')

const SendCargoOptions = require('../../../dtos/BotAction/SendCargoOptions')

const TsoftAgentAppService = require('../../../integrations/Tsoft/AgentApp/TsoftService')

module.exports = async (req, chat, integration, chatIntegration, extId) => {

  const integrationData = integration.vData
  const sendCargoOptions = new SendCargoOptions()

  const getCargoOptionsResponse = await TsoftAgentAppService.getCargoOptions(req, integration, chatIntegration, {}, extId, chat.vData.getChatLangCode())

  if (getCargoOptionsResponse.item.cargo_options.length === 0) {
    throw new createError.NotFound(req.t('App.errors.integration.cargos_are_empty'))
  }

  let cargos = []
  let cargoNames = []
  let cargoNamesForWhatsapp = []
  let cargoNamesForLivechat = []

  getCargoOptionsResponse.item.cargo_options.forEach((cargoOption, index) => {

    cargos.push({
      cargo_option_id: cargoOption.id,
      cargo_index: index + 1
    })

    cargoNames.push(utils.getMessageEmoji(index + 1, chat.channel.type) + ' ' + cargoOption.name + ' (' + utils.getCurrencyForIntlNumberFormat(
      cargoOption.cost_price,
      getCargoOptionsResponse.item.currency_code,
      integrationData.getPricePrecision()
    ) + ')')

    cargoNamesForWhatsapp.push({
      id: cargoOption.id.toString(),
      title: cargoOption.name.substring(0, 23),
      description: utils.getCurrencyForIntlNumberFormat(cargoOption.cost_price, getCargoOptionsResponse.item.currency_code, integrationData.getPricePrecision())
    })

    cargoNamesForLivechat.push({
      id: cargoOption.id.toString(),
      text: cargoOption.name.substring(0, 23) + " - " + utils.getCurrencyForIntlNumberFormat(cargoOption.cost_price, getCargoOptionsResponse.item.currency_code, integrationData.getPricePrecision())
    })

  })

  sendCargoOptions.setBotData(cargos)

  return {
    cargo_names: cargoNames.join('[BR][/BR]'),
    cargo_dto: sendCargoOptions.getBotData(),
    cargo_names_whatsapp: cargoNamesForWhatsapp,
    cargo_names_livechat: cargoNamesForLivechat
  }

}
