const pino = require('pino')()

const enums = require('../../../libs/enums')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')

const SendOrderSummaryMessageForTsoft = require('../../../integrations/Tsoft/BotAction/SendOrderSummaryMessage')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const SelectPaymentOptionsForWhatapp = require('./Whatsapp/SelectPaymentOptions')
const SelectPaymentOptionsForLivechat = require('./Livechat/SelectPaymentOptions')
const helpers = require('../../../libs/helpers')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return SelectPaymentOptionsForWhatapp(req, chat, integration, chatIntegration)
  }

  // LIVE_CHAT için burası kullanılacak
  if (chat.channel.type === enums.channel_types.LIVE_CHAT) {
    return SelectPaymentOptionsForLivechat(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if ( ! botData) {
    return
  }

  const agent = botData.agent

  let paymentOptionId = ''
  let subPaymentOptionId = ''

  // Mesaj içerisiden seçilen ödeme yöntemi bulunuyor
  botData.bot_data.payments.forEach(item => {

    if (item.childs) {

      item.childs.forEach(itemChild => {

        if (itemChild.child_index == botData.customer_message.vContentText) {
          subPaymentOptionId = itemChild.child_id
          paymentOptionId = item.payment_id
        }

      })

    }
    if (item.payment_index == botData.customer_message.vContentText) {
      paymentOptionId = item.payment_id
    }

  })

  if ( ! paymentOptionId) {
    return
  }

  // müşteri bot üzerinden ödeme yöntemini seçti, seçtiği bu bilgileri ilgili kısma kaydedeceğiz
  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setPaymentOptionId(paymentOptionId)
  chatIntegrationData.setSubPaymentOptionId(subPaymentOptionId)
  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_SELECT_PAYMENT)

  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')

  await chatIntegration.save()

  // Soket üzerinden ödeme yöntemi seçildiğine dair bilgi veriyoruz
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.PAYMENT_OPTION_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      payment_option_id: paymentOptionId, // ek bilgi olarak paylaşıyoruz
      sub_payment_option_id: subPaymentOptionId, // ek bilgi olarak paylaşıyoruz
    }
  }, req.language)

  // Soket üzerinden müşteri stage bilgisi paylaşıyoruz
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_SELECT_PAYMENT
    }
  }, req.language)

  // Müşteri Sepet özetini alıyoruz
  const messageData = await SendOrderSummaryMessageForTsoft(req, chat, integration, chatIntegration, agent.id)

  // Sepet özeti bilgisini mesaj olarak gönderiyoruz
  for (const message of messageData) {
    await ChatService.addAgentMessage(req, chat.id, message.message_type, message.message_data, agent.id, undefined, {mark_as_seen_event: true})
    await helpers.sleepFunction(1000)
  }

  pino.info({
    trace_id: req.trace_id,
    integration_id: integration.id,
    chat_id: chat.id,
    channel_id: chat.channel.id,
    bot_message_data: JSON.stringify(messageData),
    stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.STAGE_SELECT_PAYMENT
  })
}