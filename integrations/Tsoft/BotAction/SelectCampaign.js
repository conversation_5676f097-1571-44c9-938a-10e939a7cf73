const { isBoolean } = require('lodash')
const pino = require('pino')()

const enums = require('../../../libs/enums')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')

const TsoftIntegrationService = require('../../../integrations/Tsoft/TsoftIntegrationService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const SelectCampaignForWhatapp = require('./Whatsapp/SelectCampaign')
const SelectCampaignForLivechat = require('./Livechat/SelectCampaign')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return SelectCampaignForWhatapp(req, chat, integration, chatIntegration)
  }

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.LIVE_CHAT) {
    return SelectCampaignForLivechat(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if ( ! botData) {
    return
  }

  const agent = botData.agent

  // Müşterinin seçtiği kampanya bulunuyor
  const campaign = botData.bot_data.find(item => {
    return item.index === parseInt(botData.customer_message.vContentText)
  })

  if ( ! campaign) {
    return
  }

  // Kampanya uygulanması için gereken istek tsofy tarafına yapılyıro
  const response = await TsoftIntegrationService.process(req, integration, chatIntegration, enums.TSOFT_ACTIONS.APPLY_CAMPAIGN, {
    campaign_group_id: campaign.campaign_group_id,
    campaign_id: campaign.campaign_id
  }, chatIntegration.ext_id, chat.vData.getChatLangCode()).then(() => true).catch(err => err)

  // Tsoft tarafından neden kampanya uygulauanamadığına dair mesaj geliyor.
  if ( ! isBoolean(response)) {

    const messageData = {
      message_type: enums.message_types.TEXT,
      message_data: {
        text: response.message,
        agent_id: agent.id,
        language: req.language,
      }
    }
    await ChatService.addAgentMessage(req, chat._id, messageData.message_type, messageData.message_data, agent.id, undefined, { mark_as_seen_event: true })

    pino.info({
      trace_id: req.trace_id,
      integration_id: integration.id,
      chat_id: chat.id,
      channel_id: chat.channel.id,
      bot_message_data: JSON.stringify(messageData),
      stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.CAMPAIGN_SELECTED
    })
    return
  }

  // Soket üzerinden kampnaya seçildiğine dair bilgi gönderiliyor
  return QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CAMPAIGN_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
    }
  }, req.language)
}
