const pino = require('pino')()

const enums = require('../../../libs/enums')

const QueueService = require('../../../services/QueueService')
const ChatService = require('../../../services/ChatService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const SendCargoOptionsForTsoft = require('./SendCargoOptions')

const SelectInvioceAddressForWhatapp = require('./Whatsapp/SelectInvoiceAddress')
const SelectInvioceAddressForLivechat = require('./Livechat/SelectInvoiceAddress')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return SelectInvioceAddressForWhatapp(req, chat, integration, chatIntegration)
  }

   // Whatsapp için burası kullanılacak
   if (chat.channel.type === enums.channel_types.LIVE_CHAT) {
    return SelectInvioceAddressForLivechat(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  const agent = botData.agent

  // Mesaj içerisinden hangi adresin seçilidğine bakılıyor
  const address = botData.bot_data.addresses.find(item => {
    return item.address_index === parseInt(botData.customer_message.vContentText)
  })

  if (!address) {
    return
  }

  // Stage ve fatura adress bilgisi bizim tarafımıza kaydediliyor.
  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setInvoiceAddressId(address.address_id)
  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_SELECT_ADDRESS)

  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')

  const savedChatIntegration = await chatIntegration.save()

  // Fatura adresi bilgisi soket üzerinden veriliyor.
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.INVOICE_ADDRESS_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      invoice_address_id: address.address_id
    }
  }, req.language)

  // Soket üzerinden stage bilgisi gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_SELECT_ADDRESS
    }
  }, req.language)

  // Karbo bilgileri tsoft tarafından alınıyor.
  const messageData = await SendCargoOptionsForTsoft(req, chat, integration, savedChatIntegration, chatIntegration.ext_id)

  const botMessageData = {
    message_type: enums.message_types.TEXT,
    message_data: {
      text: req.t('App.integration.send_cargo_message', {
        cargo_options: messageData.cargo_names,
        interpolation: { escapeValue: false }
      }),
      next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_CARGO,
      agent_id: agent.id,
      language: req.language,
      bot_data: messageData.cargo_dto,
      bb_code: true,
    }
  }
  // Müşteriye mesaj gönderiliyor
  await ChatService.addAgentMessage(req, chat.id, botMessageData.message_type, botMessageData.message_data, agent.id, undefined, { mark_as_seen_event: true })

  pino.info({
    trace_id: req.trace_id,
    integration_id: integration.id,
    chat_id: chat.id,
    channel_id: chat.channel.id,
    bot_message_data: JSON.stringify(botMessageData),
    stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_CARGO
  })
}