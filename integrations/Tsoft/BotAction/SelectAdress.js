const pino = require('pino')()

const enums = require('../../../libs/enums')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')

const TsoftIntegrationService = require('../../../integrations/Tsoft/TsoftIntegrationService')

const SendCargoOptionsForTsoft = require('./SendCargoOptions')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const SelectAddressForWhatapp = require('./Whatsapp/SelectAdress')
const SelectAddressForLivechat = require('./Livechat/SelectAdress')


const saveChatIntegrationData = (chatIntegration, address, withInvoice) => {

  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setDeliveryAddressId(address.address_id)

  // Eğer Addres 1 tane ise withInvoice değeri true olarak gelecek.
  if (withInvoice) {
    chatIntegrationData.setInvoiceAddressId(address.address_id)
    chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_SELECT_ADDRESS)
  }

  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')

  return chatIntegration.save()

}

const sendDataFromSocket = async (req, chat, integration, agent) => {

  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.DELIVERY_ADDRESS_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id
    }
  }, req.language)

  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.INVOICE_ADDRESS_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id
    }
  }, req.language)

  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_SELECT_ADDRESS
    }
  }, req.language)

}

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return SelectAddressForWhatapp(req, chat, integration, chatIntegration)
  }

  // LIVE_CHAT için burası kullanılacak
  if (chat.channel.type === enums.channel_types.LIVE_CHAT) {
    return SelectAddressForLivechat(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  const agent = botData.agent

  // Bot mesajı içirisindeki data alınıyor
  const address = botData.bot_data.addresses.find(item => {
    return item.address_index === parseInt(botData.customer_message.vContentText)
  })

  if (!address) {
    return
  }

  let status = true
  const botMessageData = {
    message_type: '',
    message_data: ''
  }
  // Üyelikli durum için
  if (chatIntegration.ext_id) {

    const addresses = await TsoftIntegrationService.process(req, integration, chatIntegration, enums.TSOFT_ACTIONS.GET_CUSTOMER_ADDRESSES, {}, chatIntegration.ext_id, chat.vData.getChatLangCode())

    // Eğer 1 den fazla adresi bulunuyorsa invoice adresi ve delivery adresi aynı olsun mu diye sorulur
    if (addresses.items.length > 1) {

      botMessageData.message_type = enums.message_types.TEXT
      botMessageData.message_data = {
        text: req.t('App.success.ask_form_questions.is_same_delivery_and_invoce_address'),
        next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.IS_SAME_DELIVERY_AND_INVOICE_ADDRESS,
        agent_id: agent.id,
        bb_code: true,
        language: req.language,
      }

      await ChatService.addAgentMessage(req, chat.id, botMessageData.message_type, botMessageData.message_data, agent.id)

      status = false

    }

  } else {

    // Üyeliksiz için
    const chatIntegrationData = chatIntegration.vData

    // Eğer 1 den fazla adresi bulunuyorsa invoice adresi ve delivery adresi aynı olsun mu diye sorulur
    if (chatIntegrationData.getGuestAddresses().length > 1) {

      botMessageData.message_type = enums.message_types.TEXT
      botMessageData.message_data = {
        text: req.t('App.success.ask_form_questions.is_same_delivery_and_invoce_address'),
        next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.IS_SAME_DELIVERY_AND_INVOICE_ADDRESS,
        agent_id: agent.id,
        bb_code: true,
        language: req.language,
      }

      await ChatService.addAgentMessage(req, chat.id, botMessageData.message_type, botMessageData.message_data, agent.id)

      status = false
    }

  }

  // Addres sayısı 1 adet ise işlem buradan sonra devam etmeyecek
  if (!status) {

    await saveChatIntegrationData(chatIntegration, address, false)

    pino.info({
      trace_id: req.trace_id,
      integration_id: integration.id,
      chat_id: chat.id,
      channel_id: chat.channel.id,
      company_id: integration.company_id.toString(),
      bot_message_data: JSON.stringify(botMessageData),
      stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.IS_SAME_DELIVERY_AND_INVOICE_ADDRESS
    })

    // Soket üzerinden Teslimat addresi seçildiğine dair bilgi soketten gönderiliyor
    return QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.DELIVERY_ADDRESS_SELECTED,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_id: chat.id,
        integration_id: integration.id
      }
    }, req.language)

  }

  const savedChatIntegration = await saveChatIntegrationData(chatIntegration, address, true)

  // address seçimi hem delivery hemde invoice ise soketten datalar gönderiliyor
  sendDataFromSocket(req, chat, integration, agent)

  // Kargo seçenekleri tsoft tarafından alınıyor
  const messageData = await SendCargoOptionsForTsoft(req, chat, integration, savedChatIntegration, chatIntegration.ext_id)

  botMessageData.message_type = enums.message_types.TEXT
  botMessageData.message_data = {
    text: req.t('App.integration.send_cargo_message', {
      cargo_options: messageData.cargo_names,
      interpolation: { escapeValue: false }
    }),
    next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_CARGO,
    agent_id: agent.id,
    language: req.language,
    bot_data: messageData.cargo_dto,
    bb_code: true,
  }

  // Kargo seçenekleri mesaj olarak gönderiliyor
  await ChatService.addAgentMessage(req, chat.id, botMessageData.message_type, botMessageData.message_data, agent.id, undefined, { mark_as_seen_event: true })

  pino.info({
    trace_id: req.trace_id,
    integration_id: integration.id,
    chat_id: chat.id,
    channel_id: chat.channel.id,
    company_id: integration.company_id.toString(),
    bot_message_data: JSON.stringify(botMessageData),
    stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.IS_SAME_DELIVERY_AND_INVOICE_ADDRESS
  })
}
