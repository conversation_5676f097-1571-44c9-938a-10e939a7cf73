const enums = require('../../../libs/enums')

const QueueService = require('../../../services/QueueService')

const SelectMemberForWhatapp = require('./Whatsapp/SelectMember')
const SelectMemberForLivechat = require('./Livechat/SelectMember')

module.exports = async (req, chat, channel, chatIntegration, system_message, customer_message) => {

  // Whatsapp için burası kullanılacak
  if (channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return SelectMemberForWhatapp(req, chat, channel, chatIntegration, system_message, customer_message)
  }

  // Whatsapp için burası kullanılacak
  if (channel.type === enums.channel_types.LIVE_CHAT) {
    return SelectMemberForLivechat(req, chat, channel, chatIntegration, system_message, customer_message)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor

  if (!system_message.content.bot_data) {
    return
  }

  const botmessage = system_message.content.bot_data

  // Mesaj içerisinden email bilgisi alınıyor
  const selectedUser = botmessage.find(item => item.index == customer_message.content.text)

  chat.email = selectedUser.email?.toLowerCase()
  await chat.save()

  // Müşteri Üye olduğuna dair bilgiler bizim tarafımıza kaydediliyor
  chatIntegration.ext_id = selectedUser.customer_id
  await chatIntegration.save()

  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CUSTOMER_PAIRED,
    socket_rooms: [channel.id],
    data: {
      chat_id: chat.id,
    }
  }, req.language)

}
