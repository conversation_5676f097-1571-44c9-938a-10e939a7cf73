const createError = require('http-errors')

const enums = require('../../../libs/enums')
const utils = require('../../../libs/utils')
const helpers = require('../../../libs/helpers')

const SendOrderSummaryMessagePageService = require('../../../services/SendOrderSummaryMessagePageService')

const ChatService = require('../../../services/ChatService')

module.exports = async (req, chat, integration, chatIntegration, agentId) => {

  // Mesajın gönderilebilirliğini kontrol ediyor.
  const sendable = await ChatService.checkSendable(chat.id, chat.channel.type)

  if (!sendable.sendable) {
    throw new createError.BadRequest(req.t('App.errors.dash.conversation_active_time_expired'))
  }

  // Sepet özeti alınıyor
  const getMessageValues = await new SendOrderSummaryMessagePageService(req, chat, integration, chatIntegration, chatIntegration.ext_id).getMessageValues()

  getMessageValues.interpolation = { escapeValue: false }

  const messages = []
  let messageType
  let messageContent

  let messageCaption = req.t('App.integration.whatsapp_order_summary_message_cart_content', { ...getMessageValues, cart_content: getMessageValues.cart_content.carts.join('') + getMessageValues.cart_content.cart_content, lng: chat.vData.getChatLangCode() })

  if (getMessageValues.invoice_district && getMessageValues.invoice_city) {
    messageCaption += req.t('App.integration.whatsapp_order_summary_message_invoice', { ...getMessageValues, lng: chat.vData.getChatLangCode() })
  }

  messageCaption += req.t('App.integration.whatsapp_order_summary_message_delivery_address', { ...getMessageValues, lng: chat.vData.getChatLangCode() })

  if (getMessageValues.delivery_district && getMessageValues.delivery_city) {
    messageCaption += req.t('App.integration.whatsapp_order_summary_message_delivery', { ...getMessageValues, lng: chat.vData.getChatLangCode() })
  }

  messageCaption += req.t('App.integration.whatsapp_order_summary_message_approve', { ...getMessageValues, lng: chat.vData.getChatLangCode() })

  const encoder = new TextEncoder()
  const encodedBytes = encoder.encode(messageCaption)

  if (encodedBytes.length < 1000) {

    switch (chat.channel.type) {

      case enums.channel_types.WHATSAPP_NUMBER:
        messageType = enums.message_types.WHATSAPP_INTERACTIVE
        messageContent = {
          caption: messageCaption,
          sub_type: enums.message_types.BUTTON,
          header: {
            type: 'image',
            image: {
              link: integration.vData.getBasketSummaryMessageImageUrl() || enums.basket_summary_message_image_url
            }
          },
          buttons: [{
            type: 'reply',
            reply: {
              id: utils.generateHash(15),
              title: req.t('Global.chat_message.confirm', { lng: chat.vData.getChatLangCode() })
            }
          }]
        }
        break

      case enums.channel_types.INSTAGRAM_ACCOUNT:
        messageType = enums.message_types.INSTAGRAM_GENERIC_BUTTON
        messageContent = {
          bb_code: true,
          image_url: integration.vData.getBasketSummaryMessageImageUrl() || enums.basket_summary_message_image_url,
          default_action: {
            type: "web_url",
            url: integration.vData.getBasketSummaryMessageImageUrl() || enums.basket_summary_message_image_url,
          },
          title: req.t('Global.chat_message.confirm', { lng: chat.vData.getChatLangCode() }),
          subtitle: messageCaption,
          buttons: [
            {
              type: "postback",
              title: req.t('Global.chat_message.confirm', { lng: chat.vData.getChatLangCode() }),
              payload: req.t('Global.chat_message.confirm', { lng: chat.vData.getChatLangCode() })
            }
          ]
        }
        break

      case enums.channel_types.LIVE_CHAT:
        messageType = enums.message_types.LIVECHAT_BUTTON
        messageContent = {
          url: integration.vData.getBasketSummaryMessageImageUrl() || enums.basket_summary_message_image_url,
          bb_code: true,
          buttons: [
            {
              id: req.t('Global.chat_message.confirm', { lng: chat.vData.getChatLangCode() }),
              text: req.t('Global.chat_message.confirm', { lng: chat.vData.getChatLangCode() })
            }
          ],
          sub_type: enums.message_types.IMAGE_URL,
          caption: helpers.getBbCodeProviderParser(messageCaption)
        }
        break

      default:
        messageType = enums.message_types.TEXT
        messageContent = {
          text: req.t('App.integration.order_summary_message', { ...getMessageValues, lng: chat.vData.getChatLangCode() }),
          bb_code: true
        }
        break
    }

    // lazım olan ek bilgileri dahil edelim
    messageContent.next_action = enums.TSOFT_BOT_MESSAGE_ACTIONS.CREATE_ORDER
    messageContent.agent_id = agentId
    messageContent.language = req.language
    messageContent.bb_code = true

    messages.push({
      message_data: messageContent,
      message_type: messageType
    })

  } else {

    const addressCargo = {
      additional_cost: getMessageValues.additional_cost,
      cargo_option_name: getMessageValues.cargo_option_name,
      cargo_option_fee: getMessageValues.cargo_option_fee,
      total_amount: getMessageValues.total_amount,
      general_order_note: getMessageValues.general_order_note,
      invoice_address: getMessageValues.invoice_address,
      invoice_district: getMessageValues.invoice_district,
      invoice_city: getMessageValues.invoice_city,
      delivery_address: getMessageValues.delivery_address,
      delivery_district: getMessageValues.delivery_district,
      delivery_city: getMessageValues.delivery_city,
      payment_type: getMessageValues.payment_type,
      interpolation: { escapeValue: false },
      lng: chat.vData.getChatLangCode()
    }

    switch (chat.channel.type) {
      case enums.channel_types.WHATSAPP_NUMBER:
        for (const cartItem of getMessageValues.cart_content.carts) {
          messages.push({
            message_type: enums.message_types.IMAGE_URL,
            message_data: {
              caption: req.t('App.integration.whatsapp_order_summary_message_partition', {
                username: getMessageValues.username,
                cart_content: cartItem.join(''),
                remittance_discount: getMessageValues.remittance_discount,
                interpolation: { escapeValue: false },
                lng: chat.vData.getChatLangCode()
              }),
              url: integration.vData.getBasketSummaryMessageImageUrl() || enums.basket_summary_message_image_url,
              hide_image: false,
              bb_code: true,
              agent_id: agentId,
              language: req.language
            },
          })
        }

        messages.push({
          message_type: enums.message_types.WHATSAPP_INTERACTIVE,
          message_data: {
            caption: getMessageValues.cart_content.cart_content + req.t('App.integration.whatsapp_order_summary_message_partition_2', addressCargo),
            sub_type: enums.message_types.BUTTON,
            header: {
              type: 'text',
              text: req.t('Global.chat_message.confirm_order', { lng: chat.vData.getChatLangCode() })
            },
            buttons: [{
              type: 'reply',
              reply: {
                id: utils.generateHash(15),
                title: req.t('Global.chat_message.confirm', { lng: chat.vData.getChatLangCode() })
              }
            }],
            next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.CREATE_ORDER,
            agent_id: agentId,
            language: req.language,
            bb_code: true,
          },
        })
        break

      default:
        messages.push({
          message_data: {
            text: getMessageValues.cart_content.carts.join(''),
            bb_code: true,
            agent_id: agentId,
            language: req.language,
          },
          message_type: enums.message_types.TEXT
        })

        messages.push({
          message_data: {
            text: req.t('App.integration.order_summary_message_partition_2', { ...addressCargo, lng: chat.vData.getChatLangCode() }),
            bb_code: true,
            next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.CREATE_ORDER,
            agent_id: agentId,
            language: req.language,
            bb_code: true,
          },
          message_type: enums.message_types.TEXT
        })
        break
    }

  }

  return messages
}
