const utils = require('./../../libs/utils')

const __getCartContentData = (req, getCartResponse, pricePrecision, isRequiresApprove, multiCart = false, chat) => {
  const subTotalContent = req.t('App.integration.send_cart_message.sub_total', {
    total: utils.getCurrencyForIntlNumberFormat(getCartResponse.meta.sub_total, getCartResponse.meta.currency_code, pricePrecision || 2),
    interpolation: { escapeValue: false },
    lng: chat.vData.getChatLangCode()
  })

  let contents = []

  if (!multiCart) {
    // sepetteki ürünlerin altına uzun bir çizgi çekiyoruz
    contents.push('[BR][/BR][UNDERLINE]16[/UNDERLINE][BR][/BR]')
  }

  // genel toplam gösterilecekse gösterilen bilgilerde farklılıklar oluşacak
  if (getCartResponse.meta.show_general_total) {

    if (!multiCart) {
      contents.push('[BR][/BR]')
    }

    // sepete ait alt toplamı gösteriyoruz
    contents.push(subTotalContent)
    contents.push('[BR][/BR]')

    // kupon indirimi varsa göstereceğiz
    if (getCartResponse.meta.has_coupon_price) {
      contents.push(req.t('App.integration.send_cart_message.coupon_total', {
        total: utils.getCurrencyForIntlNumberFormat(getCartResponse.meta.coupon_price, getCartResponse.meta.currency_code, pricePrecision || 2),
        interpolation: { escapeValue: false },
        lng: chat.vData.getChatLangCode()
      }))
      contents.push('[BR][/BR]')
    }

    if (getCartResponse.meta.has_campaign_price) {
      contents.push(req.t('App.integration.send_cart_message.campaign_total', {
        total: utils.getCurrencyForIntlNumberFormat(getCartResponse.meta.campaign_price, getCartResponse.meta.currency_code, pricePrecision || 2),
        interpolation: { escapeValue: false },
        lng: chat.vData.getChatLangCode()
      }))
      contents.push('[BR][/BR]')
    }

    // genel toplam'ı göstereceğiz
    contents.push(req.t('App.integration.send_cart_message.general_total', {
      total: utils.getCurrencyForIntlNumberFormat(getCartResponse.meta.general_total, getCartResponse.meta.currency_code, pricePrecision || 2),
      interpolation: { escapeValue: false },
      lng: chat.vData.getChatLangCode()
    }))
    contents.push('[BR][/BR]')
  } else {
    // sepet alt toplamı göstereceğiz
    contents.push(subTotalContent)
    contents.push('[BR][/BR]')
  }

  if (isRequiresApprove) {
    contents.push(req.t('App.integration.send_cart_message.approve_message', { lng: chat.vData.getChatLangCode() }))
  }
  return contents.join('')
}

const __getGuestCartContentsData = (req, pricePrecision, isRequiresApprove, multiCart = false, totalAmount, currecyCode, chat) => {
  const subTotalContent = req.t('App.integration.send_cart_message.sub_total', {
    total: utils.getCurrencyForIntlNumberFormat(totalAmount, currecyCode, pricePrecision || 2),
    interpolation: { escapeValue: false },
    lng: chat.vData.getChatLangCode()
  })

  let contents = []

  // genel toplam gösterilecekse gösterilen bilgilerde farklılıklar oluşacak
  if (totalAmount) {

    if (!multiCart) {
      contents.push('[BR][/BR][UNDERLINE]16[/UNDERLINE][BR][/BR]')
    }

    // sepete ait alt toplamı gösteriyoruz
    contents.push(subTotalContent)
    contents.push('[BR][/BR]')

    // genel toplam'ı göstereceğiz
    contents.push(req.t('App.integration.send_cart_message.general_total', {
      total: utils.getCurrencyForIntlNumberFormat(totalAmount, currecyCode, pricePrecision || 2),
      interpolation: { escapeValue: false },
      lng: chat.vData.getChatLangCode()
    }))
    contents.push('[BR][/BR]')
  } else {
    // sepet alt toplamı göstereceğiz
    contents.push(subTotalContent)
    contents.push('[BR][/BR]')
  }

  if (isRequiresApprove) {
    contents.push(req.t('App.integration.send_cart_message.approve_message', { lng: chat.vData.getChatLangCode() }))
  }
  return contents.join('')
}

const TsoftPresentation = {

  /**
   *
   * @param req
   * @param getCartResponse
   * @param pricePrecision
   * @param isRequiresApprove
   * @returns {string}
   */
  getCartContents: (req, getCartResponse, pricePrecision, isRequiresApprove = true, chat) => {

    const dataCart = getCartResponse.items.map(cartItem => {

      let variantMessage = ''

      if (cartItem.variant_name) {
        variantMessage = ` (${cartItem.variant_name.trim()})`
      }

      return req.t('App.integration.send_cart_message.item', {
        emoji: "➡️",
        variant_message: variantMessage,
        title: cartItem.title,
        count: cartItem.count,
        price: utils.getCurrencyForIntlNumberFormat(
          cartItem.sell_price,
          cartItem.currency_code,
          pricePrecision || 2
        ),
        amount: utils.getCurrencyForIntlNumberFormat(cartItem.amount, cartItem.currency_code, pricePrecision || 2),
        stock_unit: cartItem.stock_unit,
        interpolation: { escapeValue: false },
        lng: chat.vData.getChatLangCode()
      }) + '[BR][/BR]'

    })

    const seperatedCart = {
      carts: [],
      cart_content: ''
    }
    if (dataCart.length > 4) {
      for (let index = 0; index < dataCart.length; index += 4) {
        seperatedCart.carts.push(dataCart.slice(index, index + 4))
        seperatedCart.cart_content = __getCartContentData(req, getCartResponse, pricePrecision, isRequiresApprove, true, chat)
      }
    } else {
      seperatedCart.carts.push(dataCart)
      seperatedCart.cart_content = __getCartContentData(req, getCartResponse, pricePrecision, isRequiresApprove, false, chat)
    }
    return seperatedCart
  },
  /**
   *
   * @param req
   * @param {GuestCartItems} guestCartItems
   * @param pricePrecision
   * @param isRequiresApprove
   * @returns {string}
   */
  getGuestCartContents: (req, guestCartItems, pricePrecision, isRequiresApprove = true, chat) => {

    let dataCart = []
    let totalAmount = 0
    let currrency_code = ''

    guestCartItems.getItems().forEach(cartItem => {

      let variantMessage = ''

      if (cartItem.getVariantName().length > 0) {
        variantMessage = ` (${cartItem.getVariantName().trim()})`
      }

      dataCart.push(
        req.t('App.integration.send_cart_message.item', {
          emoji: "➡️",
          variant_message: variantMessage,
          title: cartItem.getTitle(),
          count: cartItem.getCount(),
          price: utils.getCurrencyForIntlNumberFormat(
            cartItem.getSellPrice(),
            cartItem.getCurrencyCode(),
            pricePrecision || 2,
          ),
          amount: TsoftPresentation.getCartCaptionAmount(cartItem.getCount(), cartItem.getSellPrice(), cartItem.getCurrencyCode(), pricePrecision),
          stock_unit: cartItem.getStockUnit(),
          interpolation: { escapeValue: false },
          lng: chat.vData.getChatLangCode()
        }) + '[BR][/BR]'
      )

      //ürünlerin toplam fiyatını hesaplıyoruz bunu ürün sayısı * fiyatı olarak alıyoruz.
      totalAmount = totalAmount + cartItem.getSellPrice() * cartItem.getCount()
      currrency_code = cartItem.getCurrencyCode()

    })
    const seperatedCart = {
      carts: [],
      cart_content: ''
    }
    if (dataCart.length > 4) {
      for (let index = 0; index < dataCart.length; index += 4) {
        seperatedCart.carts.push(dataCart.slice(index, index + 4))
        seperatedCart.cart_content = __getGuestCartContentsData(req, pricePrecision, isRequiresApprove, true, totalAmount, currrency_code, chat)
      }
    } else {
      seperatedCart.carts.push(dataCart)
      seperatedCart.cart_content = __getGuestCartContentsData(req, pricePrecision, isRequiresApprove, false, totalAmount, currrency_code, chat)
    }

    return seperatedCart

  },
  /**
   *
   * @param count
   * @param price
   * @param currrency_code
   * @param pricePrecision
   * @returns {string}
   */
  getCartCaptionAmount: (count, price, currrency_code, pricePrecision) => {
    return utils.getCurrencyForIntlNumberFormat((parseFloat(count) * parseFloat(price)), currrency_code, pricePrecision || 2)
  }
}

module.exports = TsoftPresentation
