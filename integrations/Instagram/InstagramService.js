const { default: axios } = require('axios')

const enums = require('../../libs/enums')

const LogService = require('../../services/LogService')
const FileService = require('../../services/FileService')

const InstagramApiService = require('./InstagramApiService')

const InstagramService = {

  saveProfileImage: (user, url) => {

    return FileService.processFile(user.name + ' Instagram Profile Image', url, enums.file_ext_types.INSTAGRAM_USER_IMAGE, user.ext_id, user.id, enums.ref_entity_types.USER).then(file => {

      user.profile_image_id = file._id

      return user.save()

    }).catch(error => {

      // @todo log eklenecek

      LogService.error('Instagram Profile Image kaydedilemedi: ' + error.toString(), enums.log_channels.BACKEND, {
        user_id: user.id,
        url: url
      })

      // geriye user kaydını dönelim
      return user

    })

  },

  /**
   * @param req
   * @param {InstagramMessageDto} dto
   * @param channel
   *
   * @return {Promise<{type: string, content: {text: *}} | Object>}
   */
  getMessageObject: async (req, dto, channel) => {

    const item = dto.getItem()

    if (item.isProductMessage()) {
      const product = await InstagramApiService.getProductFromMessage(dto.data.message.referral.product.id, channel.settings.access_token)

      return {
        type: enums.message_types.INSTAGRAM_PRODUCT_MESSAGE,
        content: {
          product: {
            description: product.description,
            currency: product.currency,
            url: product.url,
            name: product.name,
            price: product.price,
            image_url: product.image_url,
            id: product.id
          },
          text: item.data.text
        }
      }
    }

    if (item.isMessageTypeImageAttachments()) {

      return {
        type: enums.message_types.INSTAGRAM_IMAGE_URLS,
        content: {
          mid: item.getMessageId(),
          urls: item.getPayloadUrls()
        }
      }

    }

    if (item.isMessageTypeVideoAttachments()) {

      return {
        type: enums.message_types.INSTAGRAM_VIDEO_URLS,
        content: {
          mid: item.getMessageId(),
          urls: item.getPayloadUrls()
        }
      }

    }

    if (item.isMessageTypeAudioAttachments()) {

      return {
        type: enums.message_types.INSTAGRAM_AUDIO_URLS,
        content: {
          mid: item.getMessageId(),
          urls: item.getPayloadUrls()
        }
      }

    }

    if (item.isMessageTypeFileAttachments()) {

      return {
        type: enums.message_types.INSTAGRAM_FILE_URLS,
        content: {
          mid: item.getMessageId(),
          urls: item.getPayloadUrls()
        }
      }

    }

    if (item.isMessageTypeShareAttachments()) {

      return {
        type: enums.message_types.INSTAGRAM_SHARE_URLS,
        content: {
          mid: item.getMessageId(),
          urls: item.getPayloadUrls()
        }
      }

    }

    if (item.isMessageTypeStoryMentionAttachments()) {

      return {
        type: enums.message_types.INSTAGRAM_STORY_MENTION_URLS,
        content: {
          mid: item.getMessageId(),
          urls: item.getPayloadUrls()
        }
      }

    }

    if (item.isMessageTypeReels()) {
      return {
        type: enums.message_types.INSTAGRAM_REELS,
        content: {
          mid: item.getMessageId(),
          attachments: item.getReelsPayloads()
        }
      }
    }

    if (item.isMessageTypeStory()) {
      const mediaType = await axios.head(item.getStoryPayloads()[0].payload.story_media_url).catch(() => false)
      if (mediaType) {
        item.getStoryPayloads()[0].payload.media_type = mediaType.headers['content-type']
      }

      return {
        type: enums.message_types.INSTAGRAM_STORY,
        content: {
          mid: item.getMessageId(),
          attachments: item.getStoryPayloads()
        }
      }
    }

    if (item.isIncomingMessageTypeQuickReply()) {

      return {
        type: enums.message_types.INSTAGRAM_QUICK_REPLY_ANSWER,
        content: {
          mid: item.getMessageId(),
          text: item.getTextForIncomingMessage(),
          quick_reply: {
            payload: item.getQuickReplyPayload()
          }
        }
      }

    }

    if (item.isIncomingMessageTypeReplyToMessage()) {

      return {
        type: enums.message_types.INSTAGRAM_REPLY_TO_MESSAGE,
        content: {
          mid: item.getMessageId(),
          text: item.getTextForIncomingMessage() ? item.getTextForIncomingMessage() : '',
          reply_to: item.getReplyToMid(),
          type: enums.message_types.TEXT
        }
      }

    }

    if (item.isIncomingMessageTypeReplyToStory()) {
      const story = await InstagramApiService.getStory(item.data.message.reply_to.story.id, channel.settings.access_token).catch(() => false)

      if (!story) {
        const mediaType = await axios.head(item.getReplyToStoryUrl()).catch(() => false)

        return {
          type: enums.message_types.INSTAGRAM_REPLY_TO_STORY,
          content: {
            mid: item.getMessageId(),
            referral: item.data.message.referral,
            text: item.getTextForIncomingMessage(),
            story: {
              id: item.getReplyToStoryId(),
              media_url: item.getReplyToStoryUrl(),
              media_type: mediaType ? mediaType.headers['content-type'] : undefined
            }
          }
        }
      }

      return {
        type: enums.message_types.INSTAGRAM_REPLY_TO_STORY,
        content: {
          mid: item.getMessageId(),
          referral: item.data.message.referral,
          text: item.data.message.text,
          story: {
            permalink: story.permalink,
            media_url: story.media_url,
            id: story.id,
            ig_id: story.ig_id,
            media_type: story.media_type,
          }
        }
      }

    }

    if (item.isOpenThread()) {
      return {
        type: enums.message_types.ADS_OPEN_THREAD,
        content: {
          type: enums.message_types.ADS_OPEN_THREAD,
          mid: item.getMessageId(),
          text: item.getTextForIncomingMessage(),
          referral: item.getMessageReferral()
        }
      }
    }

    if (item.isIncomingMessageTypeText()) {

      return {
        type: enums.message_types.TEXT,
        content: {
          mid: item.getMessageId(),
          text: item.getTextForIncomingMessage(),
          payload: item.getTextPayloadForIncomingMessage()
        }
      }

    }

    if (item.isMessageUnsupported()) {

      return {
        type: enums.message_types.UNSUPPORTED_MESSAGE,
        content: {
          mid: item.getMessageId(),
          is_unsupported: item.isMessageUnsupported(),
        }
      }

    }

    req.app.services.LogService.emergency('InstagramService::createMessage messageObject elde edilemedi.', enums.log_channels.BACKEND, {
      dto: dto.to()
    })

    throw new Error('InstagramService messageObject elde edilemedi: ' + dto.getId())

  }

}

module.exports = InstagramService
