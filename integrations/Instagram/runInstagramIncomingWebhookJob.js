const async = require('async')
const pino = require('pino')()

const MessageSeenJob = require('../../jobs/MessageSeenJob')
const MessageReactedJob = require('../../jobs/MessageReactedJob')
const MessageDeletedJob = require('../../jobs/MessageDeletedJob')
const IncomingMessageJob = require('../../jobs/IncomingMessageJob')
const MessageDeliveredJob = require('../../jobs/MessageDeliveredJob')

const InstagramMessageDto = require('./SDK/InstagramMessageDto')
const InstagramWebhookBody = require('./SDK/InstagramWebhookBody')

const IncomingWebhookJobResultDto = require('../../dtos/IncomingWebhookJobResultDto')
const Channel = require("../../models/Channel");

/**
 * @param req
 * @param {object} body req.body job üzerinden dolaylı olarak geliyor
 *
 * @return {Promise<IncomingWebhookJobResultDto>}
 */
module.exports = (req, body) => {

  const incomingWebhookJobResultDto = new IncomingWebhookJobResultDto()
  const instagramWebhookBody = new InstagramWebhookBody(body)

  if (!instagramWebhookBody.isInstagram()) {
    pino.info({
      message: 'Instagram webhook tarafında yeni bir type geldi',
      trace_id: req.trace_id,
      data: JSON.stringify(body),
    })
    throw new Error('Instagram webhook body not supported.')
  }

  return async.series(instagramWebhookBody.getInstagramWebhookEntries().map(instagramWebhookEntry => async () => {

    // instagram hesabına gelen mesajlar development amaçlı kullanılabilir olacak
    pino.info({
      trace_id: req.trace_id,
      message: "instagramwebhookentry " + instagramWebhookEntry.getId(),
      timestamp: new Date(),
      data: JSON.stringify(instagramWebhookEntry.getData()),
      channel_id: instagramWebhookEntry.getId(),
      chat_id: instagramWebhookEntry.getFrom()
    })

    if (['17841432680579382', '17841449121801351', '17841450173939436', '17841456612367663', '17841457083801411', '17841462412053373'].includes(instagramWebhookEntry.getId())) {
      incomingWebhookJobResultDto.setHasDevelopmentContent(true)
      if (instagramWebhookEntry.getFrom() !== instagramWebhookEntry.getId()) {
        incomingWebhookJobResultDto.setSenderId(instagramWebhookEntry.getFrom())
      } else {
        incomingWebhookJobResultDto.setSenderId(instagramWebhookEntry.getRecipientId())
      }
    }

    const channel = await Channel.findOne({
      ext_id: instagramWebhookEntry.getId(),
      is_active: true,
      deleted_at: {
        $exists: false
      }
    }).populate('company_id')
    if (!channel) {
      throw new Error('Kanal Bulunamadı')
    }

    if (channel.company?.is_active === false) {
      throw new Error('Şirket Pasif Olduğu için Mesaj Alınmadı')
    }

    if (channel.company_id.vData.getUsingAsBridge()) {
      incomingWebhookJobResultDto.setUsingAsBridge(channel.company_id.vData.getUsingAsBridge())
      incomingWebhookJobResultDto.setWebhookUrl(channel.company_id.vData.getDataWebhookUrl())
      incomingWebhookJobResultDto.setWebhookUrlHash(channel.company_id.vData.getDataWebhookUrlHash())
    }

    if (!instagramWebhookEntry.isMessagingEntry()) {
      pino.info({
        message: 'runInstagramIncomingWebhookJob beklenmeyen InstagramWebhookEntry tipi',
        trace_id: req.trace_id,
        data: JSON.stringify(instagramWebhookEntry.getData()),
        channel_id: instagramWebhookEntry.getId(),
        chat_id: instagramWebhookEntry.getFrom()
      })
      return false
    }

    return async.series(instagramWebhookEntry.getMessagingItems().map(item => async () => {

      if (item.hasMessageReferral()) {
        return req.app.services.JobService.addIncomingMessageJob(
          req,
          IncomingMessageJob.TYPE_INSTAGRAM,
          InstagramMessageDto.createFromInstagram(instagramWebhookEntry.getId(), item.getData()).to()
        ).then(() => true)
      }

      if (item.isMessageUnsupported()) {
        // instagram için desteklenmeyen mesaj işlemi yapılacak
        return req.app.services.JobService.addIncomingMessageJob(
          req,
          IncomingMessageJob.TYPE_INSTAGRAM,
          InstagramMessageDto.createFromInstagram(instagramWebhookEntry.getId(), item.getData()).to()
        ).then(() => true)
      }

      if (item.isMessageDeleted()) {

        // instagram için mesaj silme işlemi yapılacak
        return req.app.services.JobService.addMessageDeletedJob(
          req,
          MessageDeletedJob.TYPE_INSTAGRAM,
          InstagramMessageDto.createFromInstagram(instagramWebhookEntry.getId(), item.getData()).to()
        ).then(() => true)

      }

      if (item.isMessageDelivered()) {

        if (instagramWebhookEntry.getId() === item.getRecipientId()) {
          // instagram için mesaj ulaşma işlemi yapılacak
          return req.app.services.JobService.addMessageDeliveredJob(
            req,
            MessageDeliveredJob.TYPE_INSTAGRAM,
            InstagramMessageDto.createFromInstagram(instagramWebhookEntry.getId(), item.getData()).to()
          ).then(() => true)
        } else {
          // instagram için gelen mesaj için işlem yapılacak
          return req.app.services.JobService.addIncomingMessageJob(
            req,
            IncomingMessageJob.TYPE_INSTAGRAM,
            InstagramMessageDto.createFromInstagram(instagramWebhookEntry.getId(), item.getData()).to()
          ).then(() => true)
        }

      }

      if (item.isMessageSeen()) {

        // instagram için mesaj görüldü işlemi yapılacak
        return req.app.services.JobService.addMessageSeenJob(
          req,
          MessageSeenJob.TYPE_INSTAGRAM,
          InstagramMessageDto.createFromInstagram(instagramWebhookEntry.getId(), item.getData()).to()
        ).then(() => true)

      }

      if (item.isMessageReacted()) {

        // instagram için mesaja tepki verildi işlemi yapılacak
        return req.app.services.JobService.addMessageReactedJob(
          req,
          MessageReactedJob.TYPE_INSTAGRAM,
          InstagramMessageDto.createFromInstagram(instagramWebhookEntry.getId(), item.getData()).to()
        ).then(() => true)

      }


      if (item.isIncomingMessage()) {

        // instagram için gelen mesaj için işlem yapılacak
        return req.app.services.JobService.addIncomingMessageJob(
          req,
          IncomingMessageJob.TYPE_INSTAGRAM,
          InstagramMessageDto.createFromInstagram(instagramWebhookEntry.getId(), item.getData()).to()
        ).then(() => true)

      }

      pino.info({
        message: 'InstagramIncomingWebhookJob içinde unknown messaging type',
        trace_id: req.trace_id,
        data: JSON.stringify(instagramWebhookEntry.getData()),
        channel_id: instagramWebhookEntry.getId(),
        chat_id: instagramWebhookEntry.getFrom()
      })

      return false

    })).then(results => !results.includes(false))

  })).then(results => {

    if (results.includes(false)) {
      // yolunda gitmeyen bir şeyler oldu
      pino.info({
        message: 'Instagram webhook tarafında yolunda gitmeyen bir şeyler olmuş olabilir',
        trace_id: req.trace_id,
        data: JSON.stringify(body)
      })
    }

    return incomingWebhookJobResultDto

  })

}
