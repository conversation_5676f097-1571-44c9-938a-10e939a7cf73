const pino = require('pino')()

const enums = require('../../libs/enums')
const helpers = require('../../libs/helpers')

const InstagramService = require('./InstagramService')

const IncomingMessageJobResultDto = require('./../../dtos/IncomingMessageJobResultDto')

const Chat = require('../../models/Chat')
const User = require('../../models/User')
const Message = require('../../models/Message')
const Channel = require('./../../models/Channel')
const CompanyHasPackage = require('./../../models/CompanyHasPackage')
const ChatReferral = require('./../../models/ChatReferral')

const CreatedChatMessage = require('../../services/Chat/CreatedChatMessage')
const ChatService = require('../../services/ChatService')
const MetaEventService = require('../../services/MetaEventService')

const MessageRepo = require('../../repos/MessageRepo')

const IntegrationService = require('../../modules/AgentApp/IntegrationService')

const InstagramApiService = require('../../integrations/Instagram/InstagramApiService')

async function SaveReferralClId(messageObject, chat, channel, messageId, traceId) {
  let referral;
  if (messageObject.type === enums.message_types.ADS_OPEN_THREAD) {
    referral = await ChatReferral.findOne({
      chat_id: chat._id,
      channel_id: channel._id,
      ad_id: messageObject.content.referral.ad_id,
      deleted_at: {
        $exists: false
      }
    })
    if (!referral) {
      referral = await new ChatReferral({
        chat_id: chat._id,
        channel_id: channel._id,
        ad_id: messageObject.content.referral.ad_id,
        message_id: messageId
      }).save()

      MetaEventService.SendEvent(channel, chat, enums.data_set_events.LeadSubmitted, traceId)
    }
    chat.chat_referral_id = referral._id
    await chat.save()
  }
}

/**
 * @param req
 * @param {InstagramMessageDto} dto
 *
 * @return {Promise<IncomingMessageJobResultDto>}
 */
module.exports = async (req, dto) => {

  const incomingMessageJobResultDto = new IncomingMessageJobResultDto()

  const channel = await Channel.findOne({
    type: enums.channel_types.INSTAGRAM_ACCOUNT,
    provider: enums.channel_providers.FACEBOOK,
    ext_id: dto.getId(),
    is_active: true,
    deleted_at: { $exists: false }
  }).populate('company_id').populate('integration_id')

  if (!channel) {
    throw new Error('InstagramIncomingMessageJob channel not found: ' + dto.getId())
  }

  const companyHasPackage = await CompanyHasPackage.findOne({
    company_id: channel.company_id,
    deleted_at: {
      $exists: false
    }
  }).sort({ _id: 1 })
  if (companyHasPackage && companyHasPackage.data.instagram) {
    if (helpers.isModuleTimeOut(companyHasPackage.data.instagram) !== false) {
      channel.is_active = false
      await channel.save()

      pino.info({
        trace_id: req.trace_id,
        timestamp: new Date(),
        message: 'InstagramIncomingMessageJob channel süresi dolduğu için pasif edildi ve mesaj işlenmedi',
        data: JSON.stringify({
          channel_id: channel.id,
          channel_ext_id: channel.ext_id
        })
      })

      throw new Error('InstagramIncomingMessageJob channel süresi dolduğu için pasif edildi ve mesaj işlenmedi')
    }
  }

  const isMessage = await Message.findOne({
    channel_id: channel._id,
    ext_id: dto.getItem().getMessageId()
  })
  if (isMessage) {
    throw new Error('Bu Mesaj Zaten Kaydedilmiş')
  }

  const messageObject = await InstagramService.getMessageObject(req, dto, channel)

  let chat = null
  let user = null
  // instagram üzerinden mesaj atılmış demektir. mesajı agent atmış.
  if (dto.getId() !== dto.getItem().getRecipientId()) {
    chat = await Chat.findOne({ ext_id: dto.getItem().getRecipientId(), channel_id: channel._id })
    if (!chat) {
      const userData = await InstagramApiService.getInstagramUser(dto.getItem().getRecipientId(), dto.getItem().getMessageId(), channel.vSettings.getAccessToken())
      if (userData.data.to) {
        chat = await ChatService.getOrCreateConversation(userData.getToName(), channel._id, userData.getToId(), '')
        if (!chat.username) {
          chat.username = userData.getToName()
        }
      } else {
        chat = await ChatService.getOrCreateConversation(userData.getName(), channel._id, dto.getItem().getRecipientId(), userData.getProfilePicUrl())
        if (!chat.username) {
          chat.username = userData.getUserName()
        }
      }
    }

    user = await User.findOne({
      company_id: channel.company._id,
      type: enums.acl_roles.COMPANY_OWNER,
      tester: { $ne: true },
      deleted_at: {
        $exists: false
      }
    }).sort({ _id: 1 })
  } else {
    const userData = await InstagramApiService.getInstagramUser(dto.getCustomerId(), dto.getItem().getMessageId(), channel.vSettings.getAccessToken())

    chat = await ChatService.getOrCreateConversation(userData.getName(), channel._id, dto.getCustomerId(), userData.getProfilePicUrl())

    if (!chat.username) {
      chat.username = userData.getUserName()
    }

    if (chat.is_blocked == true) {
      throw new Error('Bu Chat Engelli olduğu için mesaj alınamıyor.')
    }
  }

  const message = await MessageRepo.create({
    type: messageObject.type,
    content: messageObject.content,
    chatId: chat.id,
    userId: user ? user._id : undefined,
    extId: dto.getItem().getMessageId(),
    fromType: dto.getId() !== dto.getItem().getRecipientId() ? enums.message_from_types.AGENT : enums.message_from_types.CUSTOMER,
    sendStatus: enums.message_send_statuses.SENT,
    time: dto.getItem().getTimestamp(),
    platform: dto.getId() !== dto.getItem().getRecipientId() ? enums.platforms.META : undefined
  }, channel.company_id.id, channel.id)

  // reklam mesajı ise reklam bilgisi kaydediliyor
  await SaveReferralClId(messageObject, chat, channel, message._id, req.trace_id)

  message.conversation_id = chat

  // // hızlı cevaplara doğrudan cevap gönderilir
  if (messageObject.type === enums.message_types.INSTAGRAM_QUICK_REPLY_ANSWER && dto.getId() === dto.getItem().getRecipientId()) {
    if (!messageObject.content.quick_reply.payload.includes('IB_FIRST_PARTY')) {
      await ChatService.addSystemMessage(req, chat, enums.message_types.TEXT, { text: messageObject.content.quick_reply.payload }, { mark_as_seen_event: true })
    } else {
      throw new Error('IB_FIRST_PARTY tipinde mesaj gelmiştir. message: ' + messageObject.content.quick_reply.payload)
    }
  }

  // ice breaker ve persistens menu mesajı olup olmadığı kontrol ediliyor
  if ((channel.vSettings.getInstagramIceBreakersStatus() || channel.vSettings.getInstagramPersistentMenuStatus()) && messageObject.content.payload && dto.getId() === dto.getItem().getRecipientId()) {
    if (!["Sepeti Onayla", "Approve Cart", "Approuver le panier", "تأكيد العربة", "Onayla", "Approve", "Approuver", "يوافق", "Sipariş Durumu", "Order Status", "Statut de la commande", "حالة الطلب"].includes(messageObject.content.payload)) {
      if (!Number.isInteger(Number(messageObject.content.payload))) {
        const lastMessage = await Message.findOne({
          conversation_id: chat._id,
          from_type: enums.message_from_types.AGENT
        }).sort({ _id: -1 })

        if (lastMessage) {
          if (!lastMessage.content.next_action) {
            await ChatService.addInstagramSystemMessage(req, chat._id, enums.message_types.TEXT, { text: messageObject.content.payload }, { mark_as_seen_event: true })
          }
        }
      }
    }
  }

  chat = await ChatService.newMessageAdded(chat, message, dto.getId() === dto.getItem().getRecipientId())

  let chatIntegration
  if (channel.integration) {
    chatIntegration = await IntegrationService.getOrCreateChatIntegration(chat, channel.integration)
  }

  incomingMessageJobResultDto.setCreatedChatMessage(new CreatedChatMessage(channel.company, channel, chat, channel.integration, message, chatIntegration))
  incomingMessageJobResultDto.setIsCustomerMessage(dto.getId() === dto.getItem().getRecipientId())

  return incomingMessageJobResultDto
}
