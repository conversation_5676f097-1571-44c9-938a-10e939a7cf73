const InstagramWebhookEntry = require('./InstagramWebhookEntry')

class InstagramWebhookBody {

  constructor(data) {
    this.data = data
  }

  /**
   * @return {boolean}
   */
  isInstagram() {
    return this.data.object === 'instagram'
  }

  /**
   * @return {InstagramWebhookEntry[]}
   */
  getInstagramWebhookEntries() {
    return this.data.entry.map(entry => {
      return new InstagramWebhookEntry(entry)
    })
  }

}

module.exports = InstagramWebhookBody
