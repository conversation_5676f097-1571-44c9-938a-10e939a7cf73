const InstagramMessagingItem = require('./InstagramMessagingItem')

class InstagramWebhookEntry {

  constructor(data) {
    this.data = data
  }

  /**
   * @return {object}
   */
  getData() {
    return this.data
  }

  /**
   * Instagram account id bilgisi
   *
   * @return {string}
   */
  getId() {
    return this.data.id
  }

  /**
   * Mesajlaşma amaçlı gönderilen bir entry olup olmadığını anlamamıza yarıyor
   *
   * @return {boolean}
   */
  isMessagingEntry() {

    if ('messaging' in this.data) {
      return Array.isArray(this.data.messaging)
    }

    if ('standby' in this.data) {
      return Array.isArray(this.data.standby)
    }

    return false

  }

  /**
   * @return {InstagramMessagingItem[]}
   */
  getMessagingItems() {
    if ('messaging' in this.data) {
      return this.data.messaging.map(item => new InstagramMessagingItem(item))
    }

    if ('standby' in this.data) {
      return this.data.standby.map(item => new InstagramMessagingItem(item))
    }

    return []
  }

  getFrom() {
    if ('messaging' in this.data) {
      return this.data.messaging[0].sender.id
    }

    if ('standby' in this.data) {
      return this.data.standby[0].sender.id
    }

    return ''
  }

  getRecipientId() {
    if ('messaging' in this.data) {
      return this.data.messaging[0].recipient.id
    }

    if ('standby' in this.data) {
      return this.data.standby[0].recipient.id
    }

    return ''
  }

}

module.exports = InstagramWebhookEntry

