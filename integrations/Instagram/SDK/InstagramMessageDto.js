const InstagramMessagingItem = require('./InstagramMessagingItem')

class InstagramMessageDto {

  /**
   * @param id
   * @param data
   *
   * @private
   */
  constructor(id, data) {

    // instagram account id
    this.id = id

    // InstagramMessagingItem için kullanılmak üzere data
    this.data = data


  }

  /**
   * @param {string} id
   * @param {object} data
   *
   * @return {InstagramMessageDto}
   */
  static createFromInstagram(id, data) {
    return new InstagramMessageDto(id, data)
  }

  static createFromData(data) {
    return new InstagramMessageDto(data.id, data.data)
  }

  to() {

    return {
      id: this.id,
      data: this.data
    }

  }

  /**
   * @return {string}
   */
  getId() {
    return this.id
  }

  /**
   * @return {InstagramMessagingItem}
   */
  getItem() {
    return new InstagramMessagingItem(this.data)
  }

  /**
   * @return {string}
   */
  getCustomerId() {
    return this.getItem().getCustomerId(this.id)
  }

  isSenderPage() {
    return this.id === this.data.sender.id
  }

}

module.exports = InstagramMessageDto
