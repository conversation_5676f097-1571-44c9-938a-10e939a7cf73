const { nanoid } = require('nanoid')
const Types = require('./Types')

const IncomingAttachment = require('./IncomingAttachment')

class InstagramMessagingItem {

  constructor(data) {
    this.data = data
  }

  /**
   * @return {object}
   */
  getData() {
    return this.data
  }

  /**
   * @return {string}
   */
  getSenderId() {

    if ('sender' in this.data) {

      if ('id' in this.data.sender) {

        return this.data.sender.id

      }

    }

    throw new Error('sender.id bilgisi yok.')

  }

  /**
   * @return {string}
   */
  getRecipientId() {

    if ('recipient' in this.data) {

      if ('id' in this.data.recipient) {

        return this.data.recipient.id

      }

    }

    throw new Error('recipient.id bilgisi yok.')

  }

  /**
   * @param {string} accountId
   *
   * @return {string}
   */
  getCustomerId(accountId) {

    if (accountId === this.getSenderId()) {
      return this.getRecipientId()
    }

    return this.getSenderId()

  }

  getMessageId() {
    if ('message' in this.data) {
      return this.data.message.mid
    }
    // generic mesaj durumu için kendimiz bir id üretiyoruz
    if ('postback' in this.data) {
      return nanoid(60).toLowerCase()
    }
    return ''
  }

  getReactionMid() {
    return this.data.reaction.mid
  }

  getSeenMid() {
    return this.data.read.mid
  }

  getReactionReaction() {
    return this.data.reaction.reaction
  }

  getReactionEmoji() {
    return this.data.reaction.emoji
  }

  getReactionAction() {
    return this.data.reaction.action
  }

  /**
   * @return {boolean}
   */
  isMessageDeleted() {

    if ('message' in this.data) {

      if ('is_deleted' in this.data.message) {

        return Boolean(this.data.message.is_deleted)

      }

    }

    return false

  }

  /**
   * @return {boolean}
   */
  isMessageDelivered() {
    if ('message' in this.data) {
      if ('is_echo' in this.data.message) {
        return Boolean(this.data.message.is_echo)
      }
    }
    return false
  }

  /**
   * @return {boolean}
   */
  isMessageSeen() {

    return 'read' in this.data

  }

  /**
   * @return {boolean}
   */
  isMessageReacted() {

    return 'reaction' in this.data

  }

  /**
   * @return {boolean}
   */
  isIncomingMessage() {
    if ('message' in this.data) {
      return true
    }
    // generic mesaja verilmiş bir cevap demek oluyor
    if ('postback' in this.data) {
      return true
    }
    // quick_reply a cevap olarak gelen webhook
    if ('quick_reply' in this.data) {
      return true
    }
    return false
  }

  /**
   * @return {boolean}
   */
  isMessageUnsupported() {

    if ('message' in this.data) {

      if ('is_unsupported' in this.data.message) {

        return Boolean(this.data.message.is_unsupported)

      }

    }

    return false

  }

  isProductMessage() {
    if ('message' in this.data) {
      if ('referral' in this.data.message) {
        if ('product' in this.data.message.referral) {
          if ('id' in this.data.message.referral.product) {
            return this.data.message.referral.product.id
          }
        }
      }
    }
  }

  getMessageReferralId() {
    if ('message' in this.data) {
      if ('referral' in this.data.message) {
        return this.data.message.referral.ad_id
      }
    }
  }

  /**
   * @return {string}
   */
  isOpenThread() {
    if ('message' in this.data) {
      if ('referral' in this.data.message) {
        if ('type' in this.data.message.referral) {
          return this.data.message.referral.type
        }
      }
    }
  }

  /**
   * @return {boolean}
   */
  hasMessageReferral() {

    if ('message' in this.data) {

      return 'referral' in this.data.message

    }

    return false

  }

  /**
   * @return {string}
   */
  getTextForIncomingMessage() {
    if ('message' in this.data) {
      return this.data.message.text
    }
    if ('postback' in this.data) {
      return this.data.postback.title
    }
    return ''
  }

  /**
   * @return {object}
   */
  getMessageReferral() {
    if ('message' in this.data) {
      if ('referral' in this.data.message) {
        return this.data.message.referral
      }
      return false
    }
  }

  /**
   * @return {string}
   */
  getTextPayloadForIncomingMessage() {
    if ('postback' in this.data) {
      return this.data.postback.payload
    }
    return undefined
  }

  /**
   * @return {string}
   */
  getReplyToStoryId() {
    return this.data.message.reply_to.story.id
  }

  /**
   * @return {string}
   */
  getReplyToStoryUrl() {
    return this.data.message.reply_to.story.url
  }

  /**
   * @return {string}
   */
  getReplyToMid() {
    return this.data.message.reply_to.mid
  }

  /**
   * @return {string}
   */
  getQuickReplyPayload() {
    return this.data.message.quick_reply.payload
  }

  /**
   * @return {IncomingAttachment[]}
   */
  getInstagramAttachments() {
    return this.data.message.attachments.map(attachment => new IncomingAttachment(attachment))
  }

  /**
   * @return {string[]}
   */
  getPayloadUrls() {
    return this.getInstagramAttachments().map(item => item.getPayloadUrl())
  }

  /**
   * @return {[]}
   */
  getReelsPayloads() {
    return this.data.message.attachments
  }

  /**
   * @return {[]}
   */
  getStoryPayloads() {
    return this.data.message.attachments
  }

  /**
   * @return {string|undefined}
   */
  getIncomingMessageType() {

    if (this.__isIncomingMessageTypeAttachment()) {
      return Types.message.ATTACHMENT
    }

    if (this.isIncomingMessageTypeReplyToStory()) {
      return Types.message.STORY_REPLY
    }

    if (this.isIncomingMessageTypeReplyToMessage()) {
      return Types.message.MESSAGE_REPLY
    }

    if (this.isIncomingMessageTypeQuickReply()) {
      return Types.message.QUICK_REPLY
    }

    if (this.isIncomingMessageTypeText()) {
      return Types.message.TEXT
    }

    return undefined

  }

  // birden fazla işlem yapılacak, story_mention, image, video, audio, file, share
  __isIncomingMessageTypeAttachment() {

    if ('message' in this.data) {

      if ('attachments' in this.data.message) {

        return Array.isArray(this.data.message.attachments)

      }

    }

    return false

  }

  /**
   * @return {boolean}
   */
  isMessageTypeFileAttachments() {

    if (!this.__isIncomingMessageTypeAttachment()) {
      return false
    }

    for (const attachment of this.getInstagramAttachments()) {

      if (!attachment.isFile()) {
        return false
      }

    }

    return true

  }

  /**
   * @return {boolean}
   */
  isMessageTypeImageAttachments() {

    if (!this.__isIncomingMessageTypeAttachment()) {
      return false
    }

    for (const attachment of this.getInstagramAttachments()) {

      if (!attachment.isImage()) {
        return false
      }

    }

    return true

  }

  /**
   * @return {boolean}
   */
  isMessageTypeVideoAttachments() {

    if (!this.__isIncomingMessageTypeAttachment()) {
      return false
    }

    for (const attachment of this.getInstagramAttachments()) {

      if (!attachment.isVideo()) {
        return false
      }

    }

    return true

  }

  /**
   * @return {boolean}
   */
  isMessageTypeAudioAttachments() {

    if (!this.__isIncomingMessageTypeAttachment()) {
      return false
    }

    for (const attachment of this.getInstagramAttachments()) {

      if (!attachment.isAudio()) {
        return false
      }

    }

    return true

  }

  /**
   * @return {boolean}
   */
  isMessageTypeShareAttachments() {

    if (!this.__isIncomingMessageTypeAttachment()) {
      return false
    }

    for (const attachment of this.getInstagramAttachments()) {

      if (!attachment.isShare()) {
        return false
      }

    }

    return true

  }

  isMessageTypeReels() {
    if (!this.__isIncomingMessageTypeAttachment()) {
      return false
    }

    for (const attachment of this.getInstagramAttachments()) {

      if (attachment.isReels()) {
        return true
      }

    }

    return false
  }

  isMessageTypeStory() {
    if (!this.__isIncomingMessageTypeAttachment()) {
      return false
    }

    for (const attachment of this.getInstagramAttachments()) {

      if (attachment.isStory()) {
        return true
      }

    }

    return false
  }

  /**
   * @return {boolean}
   */
  isMessageTypeStoryMentionAttachments() {

    if (!this.__isIncomingMessageTypeAttachment()) {
      return false
    }

    for (const attachment of this.getInstagramAttachments()) {

      if (!attachment.isStoryMention()) {
        return false
      }

    }

    return true

  }

  // birden fazla işlem yapılacak: story_reply, message_reply
  isIncomingMessageTypeReplyToStory() {

    if ('message' in this.data) {

      if ('reply_to' in this.data.message) {

        return 'story' in this.data.message.reply_to

      }

    }

    return false

  }

  isIncomingMessageTypeReplyToMessage() {

    if ('message' in this.data) {

      if ('reply_to' in this.data.message) {

        return 'mid' in this.data.message.reply_to

      }

    }

    return false

  }

  isIncomingMessageTypeQuickReply() {

    if ('message' in this.data) {

      return 'quick_reply' in this.data.message

    }

    return false

  }

  isIncomingMessageTypeText() {

    if ('message' in this.data) {
      return 'text' in this.data.message
    }

    // generic mesaja verilmiş bir cevap demek oluyor
    if ('postback' in this.data) {
      return 'title' in this.data.postback
    }

    return false

  }

  getTimestamp() {
    return this.data.timestamp
  }

}

module.exports = InstagramMessagingItem
