class FromUser {

  constructor(data) {
    this.data = data
  }

  getId() {
    return this.data.from.id
  }

  getName(req) {
    return this.data.from.username || req.t('UnNameUser')
  }

  getUserName() {
    return this.data.from.username
  }

  getToName() {
    return this.data.to?.data[0]?.username
  }

  getToId() {
    return this.data.to?.data[0]?.id
  }

  getProfilePicUrl() {

    if ('profile_pic' in this.data) {
      return this.data.profile_pic
    }

    if ('profile_pic' in this.data.from) {
      return this.data.from.profile_pic
    }

    return ''
  }

}

module.exports = FromUser
