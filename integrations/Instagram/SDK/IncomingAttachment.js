const Types = require('./Types')

class IncomingAttachment {

  constructor(data) {
    this.data = data
  }

  getType() {
    return this.data.type
  }

  /**
   * @return {string}
   */
  getPayloadUrl() {
    return this.data.payload.url
  }

  /**
   * @return {boolean}
   */
  isImage() {
    return this.getType() === Types.attachment.IMAGE
  }

  /**
   * @return {boolean}
   */
  isVideo() {
    return this.getType() === Types.attachment.VIDEO
  }

  /**
   * @return {boolean}
   */
  isAudio() {
    return this.getType() === Types.attachment.AUDIO
  }

  /**
   * @return {boolean}
   */
  isFile() {
    return this.getType() === Types.attachment.FILE
  }

  /**
   * @return {boolean}
   */
  isShare() {
    return this.getType() === Types.attachment.SHARE
  }

  /**
   * @return {boolean}
   */
  isStoryMention() {
    return this.getType() === Types.attachment.STORY_MENTION
  }

  /**
   * @return {boolean}
   */
  isReels() {
    return this.getType() === Types.attachment.IG_REEL
  }

  /**
   * @return {boolean}
   */
  isStory() {
    return this.getType() === Types.attachment.IG_STORY
  }

}

module.exports = IncomingAttachment
