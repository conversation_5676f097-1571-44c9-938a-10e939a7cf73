const { default: axios } = require('axios')
const { HttpsProxyAgent } = require('https-proxy-agent')
const pino = require('pino')()
const moment = require('moment')

const enums = require('./../../libs/enums')
const utils = require('./../../libs/utils')
const helpers = require('./../../libs/helpers')

const InstagramUser = require('./SDK/User')
const InstagramFromUser = require('./SDK/FromUser')

const proxy = {
  httpsAgent: process.env.META_PROXY_REQUEST_STATUS !== "true" ? undefined : new HttpsProxyAgent(process.env.META_PROXY_REQUEST_URL)
}

const InstagramApiService = {

  /**
   * @param {string} userPsId
   * @param {string} accessToken
   *
   * @return {Promise<InstagramUser>}
   */
  getInstagramUser: (userPsId, messageExtId, accessToken) => {

    return InstagramApiService.__get('/' + userPsId, accessToken, {
      fields: 'id,name,profile_pic,username'
    }).then(data => {

      return new InstagramUser(data)

    }).catch(error => {

      return InstagramApiService.__get(`/${messageExtId}`, accessToken, { fields: 'from,to' }).then(data => new InstagramFromUser(data))

    })

  },

  sendMessage: (recipientId, message, accessToken, traceId) => {

    return Promise.resolve().then(() => {

      switch (message.type) {

        case enums.message_types.TEXT:

          let text = message.vContentText

          if (message.vContentBbCode) {
            text = helpers.getBbCodeProviderParser(message.vContentText)
          }

          return {
            recipient: {
              id: recipientId
            },
            message: {
              text: text
            }
          }

        case enums.message_types.IMAGE_URL:

          return {
            recipient: {
              id: recipientId
            },
            message: {
              attachment: {
                type: 'image',
                payload: {
                  url: message.vContentUrl
                }
              }
            }
          }

        case enums.message_types.INSTAGRAM_QUICK_REPLY:

          return {
            recipient: {
              id: recipientId
            },
            messaging_type: 'RESPONSE',
            message: message.vContent
          }

        case enums.message_types.INSTAGRAM_GENERIC_BUTTON:

          return {
            recipient: {
              id: recipientId
            },
            message: {
              attachment: {
                type: 'template',
                payload: {
                  template_type: 'generic',
                  elements: [
                    {
                      buttons: message.vContent.buttons,
                      image_url: message.vContent.image_url,
                      default_action: message.vContent.default_action,
                      subtitle: message.vContentBbCode === true ? helpers.getBbCodeProviderParser(message.vContent.subtitle) : message.vContent.subtitle,
                      title: message.vContent.title
                    }
                  ]
                }
              }
            }
          }

        case enums.message_types.VIDEO_URL:

          return {
            recipient: {
              id: recipientId
            },
            message: {
              attachment: {
                type: 'video',
                payload: {
                  url: message.vContentUrl
                }
              }
            }
          }

        case enums.message_types.AUDIO_URL:

          return {
            recipient: {
              id: recipientId
            },
            message: {
              attachment: {
                type: 'audio',
                payload: {
                  url: message.vContentUrl
                }
              }
            }
          }

        case enums.message_types.INSTAGRAM_MEDIA_REPLY:

          return {
            recipient: {
              comment_id: message.vContent.comment.id
            },
            message: {
              text: message.vContentText
            }
          }

        default:
          throw 'Message Type Error'
      }

    }).then(async data => {
      try {
        if (message.data?.tag === enums.message_types.HUMAN_AGENT) {
          data.messaging_type = 'MESSAGE_TAG'
          data.tag = enums.message_types.HUMAN_AGENT
        }

        const response = await InstagramApiService.__post('/me/messages', accessToken, data)

        pino.info({
          trace_id: traceId,
          timestamp: new Date(),
          message: 'instagram send message',
          data: JSON.stringify(response.data),
          chat_ext_id: recipientId,
          access_token: accessToken,
          config: JSON.stringify(data)
        })

        return response.message_id
      } catch (error) {
        pino.error({
          trace_id: traceId,
          timestamp: new Date(),
          message: 'facebook send message failed',
          error: JSON.stringify(error.response?.data || { message: 'facebook send message failed' }),
          chat_ext_id: recipientId,
          access_token: accessToken,
          config: JSON.stringify(data)
        })

        throw error
      }
    })

  },

  markAsSeen: (req, recipientId, accessToken) => {

    return InstagramApiService.__post('/me/messages', accessToken, {
      'recipient': {
        'id': recipientId
      },
      'sender_action': 'mark_seen'
    }).then(response => {
      pino.info({
        trace_id: req.trace_id,
        timestamp: new Date(),
        message: 'marked seen',
        data: JSON.stringify(response)
      })
    }).catch(error => {
      pino.error({
        trace_id: req.trace_id,
        timestamp: new Date(),
        message: 'Services\\ConversationService ConversationService::markAsSeen markAsSeen yapılamadı.',
        error: JSON.stringify(error.response?.data || { message: 'facebook seen message failed' })
      })
    })

  },

  __get: (endpoint, accessToken, params = {}) => {

    params.access_token = accessToken

    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}` + endpoint,
      method: 'get',
      params: params,
      ...proxy
    }

    return axios.request(config).then((response) => {
      pino.info({
        config: JSON.stringify(config),
        timestamp: new Date(),
        message: 'get graph response',
        data: JSON.stringify(response.data)
      })

      return response.data

    }).catch(error => {
      pino.error({
        config: JSON.stringify(config),
        timestamp: new Date(),
        message: 'get instagram graph error',
        error: JSON.stringify(error.response?.data || { message: 'facebook get failed' }),
      })
      throw new Error(error)
    })

  },

  __post: (endpoint, accessToken, json = {}) => {

    json.access_token = accessToken

    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}` + endpoint,
      method: 'post',
      data: json,
      ...proxy
    }

    return axios.request(config).then((response) => {
      delete config.httpsAgent

      pino.info({
        config: JSON.stringify(config),
        timestamp: new Date(),
        message: 'post graph response',
        data: JSON.stringify(response.data)
      })

      return response.data

    })

  },

  getMedias: (igUserId, accessToken, paginationInfo) => {

    let url = `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${igUserId}/media?fields=
      caption,
      comments_count,
      media_url,
      like_count,
      thumbnail_url,
      permalink,
      timestamp&access_token=${accessToken}&limit=20`

    if (paginationInfo) {
      url += `&${paginationInfo.process}=${paginationInfo.key}`
    }

    return axios.get(url, { ...proxy }).then(response => response.data)

  },

  getMediaComments: (mediaId, accessToken, afterKey) => {

    let url = `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${mediaId}/comments?fields=
      id,
      text,
      from{
        id,
        username
      },
      timestamp,
      like_count,
      hidden,
      replies{
        id,
        text,
        from{
          id,
          username
        },
        timestamp,
        like_count,
        hidden
      }&access_token=${accessToken}`

    if (afterKey) {
      url += `&after=${afterKey}`
    }

    return axios.get(url, { ...proxy }).then(response => response.data)


  },

  sendCommentToPost: (postId, comment, accessToken) => {
    return axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${postId}/comments?message=${encodeURI(comment)}&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  sendReplyToComment: (commentId, comment, accessToken) => {
    return axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${commentId}/replies?message=${encodeURI(comment)}&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  hideComment: (commentId, accessToken, status = true) => {
    return axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${commentId}?hide=${status}&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  deleteComment: (commentId, accessToken) => {
    return axios.delete(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${commentId}?access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  getCommentReplies: (commentId, accessToken, after) => {

    let url = `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${commentId}/replies?fields=
      id,
      text,
      timestamp,
      like_count,
      username&access_token=${accessToken}`

    if (after) {
      url += `&after=${after}`
    }

    return axios.get(url, { ...proxy }).then(response => response.data)
  },

  getProfilePicture: (accountId, accessToken) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${accountId}?fields=profile_picture_url&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  getUserProfilePicture: (accountId, accessToken) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${accountId}?&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  getMediaAsId: (mediaId, accessToken) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${mediaId}?fields=id,media_url,like_count,comments_count,caption,timestamp,thumbnail_url,permalink,media_type&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  getCommentDetail: (commentId, accessToken) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${commentId}?fields=like_count&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  getInstagramPages: async (longLivedPageAccessToken) => {
    return await axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/me/accounts?fields=
      id,
      name,
      category,
      category_list,
      tasks,
      access_token,
      connected_instagram_account{
        id,
        name,
        username,
        profile_picture_url
      }&limit=100&access_token=${longLivedPageAccessToken}`, { ...proxy }).then(response => response.data)
  },

  getInstagramUsername: async (accountId, accessToken) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${accountId}?fields=username&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  getStory: async (storyId, accessToken) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${storyId}?fields=permalink,caption,media_url,id,ig_id,media_type&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  getProductFromMessage: async (productId, accessToken) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${productId}?fields=description,currency,url,name,price,image_url&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  getMediaIdFromCommentId: async (commentId, accessToken) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${commentId}?fields=media,from,text,timestamp&access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  reactToMessage: async (to, messageId, accessToken) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/me/messages?access_token=${accessToken}`,
      method: 'POST',
      data: {
        recipient: {
          id: to
        },
        sender_action: 'react',
        payload: {
          message_id: messageId,
          reaction: 'love'
        }
      },
      ...proxy
    }

    return axios.request(config).then(response => response.data)
  },

  unReactToMessage: async (to, messageId, accessToken) => {
    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/me/messages?access_token=${accessToken}`,
      method: 'POST',
      data: {
        recipient: {
          id: to
        },
        sender_action: 'unreact',
        payload: {
          message_id: messageId
        }
      },
      ...proxy
    }

    return axios.request(config).then(response => response.data)
  },

  getEventDataSet: async (pageId, accessToken) => {
    return axios.get(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${pageId}/dataset?fields=id,name&access_token=${accessToken}`, { ...proxy }).then(response => response.data?.data)
  },

  addDataSet: async (pageId, accessToken) => {
    return axios.post(`https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${pageId}/dataset?access_token=${accessToken}`, { ...proxy }).then(response => response.data)
  },

  sendEventData: async (pageId, eventId, customerId, customData, eventName, accessToken, traceId) => {
    const time = moment().unix()

    const config = {
      url: `https://graph.facebook.com/${process.env.FACEBOOK_GRAPH_API_VERSION}/${eventId}/events?access_token=${accessToken}`,
      method: 'POST',
      data: {
        data: [
          {
            event_name: eventName,
            event_id: utils.CreateRandomNumbers(),
            event_time: time,
            action_source: "business_messaging",
            user_data: {
              ig_account_id: pageId,
              ig_sid: customerId
            },
            messaging_channel: "instagram",
            custom_data: customData,
            original_event_data: {
              event_name: eventName,
              event_time: time
            }
          }
        ]
      },
      ...proxy
    }

    return axios.request(config).then(response => response.data).catch(error => {

      delete config.httpsAgent

      pino.error({
        trace_id: traceId,
        timestamp: new Date(),
        message: 'Instagram Event Data Gönderme İşleminde Hata Oluştu',
        error: JSON.stringify(error.response?.data || { message: 'istek yapılamadı' }),
        data: JSON.stringify(config)
      })

      throw error
    })
  }

}

module.exports = InstagramApiService
