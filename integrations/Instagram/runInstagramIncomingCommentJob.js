const pino = require('pino')()
const enums = require('../../libs/enums')

const Channel = require('../../models/Channel')
const Comment = require('../../models/Comment')
const Reviewer = require('../../models/Reviewer')

const InstagramCommentDto = require('../../dtos/Marketing/InstagramCommentDto')

const InstagramApiService = require('../Instagram/InstagramApiService')

const QueueService = require('../../services/QueueService')

// Kullanıcı oluşturulur veya bulunur
const createReviewer = async (commentData, companyId, profilePictureUrl, selfComment = false) => {

  const newReviewer = new Reviewer()

  newReviewer.type = enums.acl_roles.INSTAGRAM_COMMENT_USER
  newReviewer.username = commentData.getUserName()
  newReviewer.ext_id = commentData.getUserId()
  newReviewer.company_id = companyId
  newReviewer.profile_picture_url = profilePictureUrl
  newReviewer.self_user = selfComment

  return newReviewer.save()
}

const saveComment = (postData, commentId, commentData, field, reviewerId, channelId, selfComment = false) => {

  const getPostId = postData.id

  const comment = new Comment()

  comment.reviewer_id = reviewerId
  comment.post_id = getPostId
  comment.ext_id = commentId
  comment.comment = {
    ...commentData,
    field: field
  }
  comment.readed = selfComment
  comment.self_comment = selfComment
  comment.channel_id = channelId
  comment.post_data = {
    image: postData.media_url,
    text: postData.caption,
    media_type: postData.media_type
  }

  return comment.save()
}

module.exports = async (body, traceId) => {

  const instagramCommentDto = new InstagramCommentDto(body)

  const comments = []

  for (const entry of instagramCommentDto.getEntries()) {

    for (const commentData of entry.getChanges()) {

      // Yorum dışında bir type gelirse şimdlik loglanacak
      if (!commentData.isComment()) {
        pino.info({
          message: 'InstagramIncomingCommentJob Yorum dışında bir bilgi geldi',
          trace_id: traceId,
          data: JSON.stringify(commentData.getData()),
          channel_id: entry.getChannelId(),
          chat_id: commentData.getUserId()
        })
        continue
      }

      let selfComment = false

      // agentin kendi yorumu gelmiş demektir.
      if (commentData.getUserId() === entry.getChannelId()) {
        selfComment = true
      }

      const channel = await Channel.findOne({
        type: enums.channel_types.INSTAGRAM_ACCOUNT,
        provider: enums.channel_providers.FACEBOOK,
        ext_id: entry.getChannelId(),
        is_active: true,
        deleted_at: {
          $exists: false
        }
      }).populate('company_id').populate('integration_id')

      if (!channel) {

        pino.info({
          message: 'InstagramIncomingCommentJob channel not found.',
          trace_id: traceId,
          data: JSON.stringify(commentData.getData()),
          channel_id: entry.getChannelId(),
          chat_id: commentData.getUserId()
        })

        throw new Error('InstagramIncomingCommentJob channel not found: ' + entry.getChannelId())
      }

      if (channel.company?.is_active === false) {
        throw new Error('Şirket Pasif Olduğu için Mesaj Alınmadı')
      }

      // agentin kendi yorumu gelmiş demektir.
      if (commentData.getUserName() === channel.vSettings.getUsername()) {
        selfComment = true
      }

      let reviewer = await Reviewer.findOne({
        type: enums.acl_roles.INSTAGRAM_COMMENT_USER,
        ext_id: commentData.getUserId()
      })

      let getProfilePicture

      // Kullanıcı yoksa yeni bir kullanıcı oluşturulur. profile resimleri gibi diğer bilgiler kaydedilir
      if (!reviewer) {
        // Yorum yapan kullanıcnın profile resmi Alınıyor.
        const userInfo = await InstagramApiService.getUserProfilePicture(commentData.getUserId(), channel.vSettings.getAccessToken()).catch(err => {
          pino.info({
            message: 'InstagramIncomingCommentJob profile pic cannot take.',
            trace_id: traceId,
            data: JSON.stringify(commentData.getData()),
            channel_id: entry.getChannelId(),
            chat_id: commentData.getUserId(),
            error: JSON.stringify(err.response?.data || { message: 'istek yapılamadı' })
          })
          return {
            profile_pic: ''
          }
        })

        getProfilePicture = userInfo.profile_pic
        // Reviewer kullanıcı oluşturuluor veya alınıyor
        reviewer = await createReviewer(commentData, channel.company._id, getProfilePicture, selfComment)
      } else {
        getProfilePicture = reviewer.profile_picture_url
      }

      // Commentin bulunduğu postun bilgileri alınıyor.
      const postData = await InstagramApiService.getMediaAsId(commentData.getMediaId(), channel.vSettings.getAccessToken()).catch(err => {
        pino.info({
          message: 'InstagramIncomingCommentJob -> post data cannot take.',
          trace_id: traceId,
          data: JSON.stringify(commentData.getData()),
          channel_id: entry.getChannelId(),
          chat_id: commentData.getUserId(),
          error: JSON.stringify(err.response?.data || { message: 'istek yapılamadı' })
        })
        throw new Error('InstagramIncomingCommentJob -> post data cannot take: ' + entry.getChannelId())
      })

      // Yorum bilgileri kaydediliyor
      const comment = await saveComment(postData, commentData.getCommentId(), commentData.getCommentData(), commentData.getField(), reviewer._id, channel._id, selfComment)

      if (selfComment) {
        const hasComment = await Comment.findOne({ ext_id: commentData.getParentId(), channel_id: channel._id, readed: false })
        if (hasComment) {
          hasComment.readed = true
          await hasComment.save()

          await QueueService.publishToAppSocket({
            event: enums.agent_app_socket_events.MARK_AS_SEEN_COMMENT,
            socket_rooms: [channel.id],
            data: {
              comment_id: hasComment.id
            }
          }, 'tr')
        }
      }

      comments.push({
        id: comment.id,
        self_comment: selfComment,
        chat_id: commentData.getUserId(),
        company_socket_code: channel.id,
        channel: channel,
        comment_id: commentData.getCommentId(),
        post_id: commentData.getMediaId(),
        post_data: {
          image: postData.media_url,
          text: postData.caption
        },
        channel_type: enums.channel_types.INSTAGRAM_ACCOUNT,
        channel_id: entry.getChannelId(),
        user: {
          id: reviewer.id,
          name: commentData.getUserName(),
          image_url: getProfilePicture
        },
        created_at: Date.now(),
        comment: {
          ...commentData.getCommentData(),
          field: commentData.getField()
        },
        is_development_data: {
          status: ["*****************", "*****************", "*****************", "*****************"].includes(entry.getChannelId()),
          body: body,
          type: enums.channel_types.INSTAGRAM_ACCOUNT
        },
      })

    }

  }

  // kuyruk için data dönüldü
  return comments

}
