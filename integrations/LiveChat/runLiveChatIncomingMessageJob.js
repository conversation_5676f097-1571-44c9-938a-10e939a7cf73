const enums = require('../../libs/enums')

const IncomingMessageJobResultDto = require('../../dtos/IncomingMessageJobResultDto')

const Chat = require('../../models/Chat')
const Message = require('../../models/Message')
const Company = require('../../models/Company')

const QueueService = require('../../services/QueueService')

const CreatedChatMessage = require('../../services/Chat/CreatedChatMessage')

const DashPresenter = require('../../presenters/Dash')
const IntegrationService = require('../../modules/AgentApp/IntegrationService')

module.exports = async (req, messageId) => {

  const incomingMessageJobResultDto = new IncomingMessageJobResultDto()

  const message = await Message.findById(messageId).populate('conversation_id').populate('user_id')

  if (!message) {
    return
  }

  const chat = await Chat.findById(message.conversation._id).populate({
    path: 'channel_id',
    populate: {
      path: 'integration_id',
    }
  })

  if (!chat) {
    return
  }

  if (!chat.channel.is_active) {
    return
  }

  if (chat.is_blocked == true) {
    return
  }

  const company = await Company.findById(chat.channel.company_id)

  if (!company) {
    return
  }

  QueueService.publishToLiveChatSocket({
    event: enums.live_chat_socket_events.MESSAGE_SENT,
    socket_rooms: [chat.ext_id],
    data: {
      message_item: await DashPresenter.getRepliedMessage(message),
    }
  }, req.i18n.language)

  let chatIntegration
  if (chat.channel.integration) {
    chatIntegration = await IntegrationService.getOrCreateChatIntegration(chat, chat.channel.integration)
  }

  incomingMessageJobResultDto.setCreatedChatMessage(new CreatedChatMessage(company, chat.channel, chat, chat.channel.integration, message, chatIntegration))

  return incomingMessageJobResultDto

}
