const axios = require('axios')
// const google = require('googleapis').google

const helpers = require('./../../libs/helpers')

function getAccessToken() {

  return new Promise(function (resolve, reject) {

    const SCOPES = ['https://www.googleapis.com/auth/firebase.messaging']

    return resolve(true)
    // new google.auth.JWT(
    //   "<EMAIL>",
    //   null,
    //   "-----B<PERSON>IN PRIVATE KEY-----\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCrsSvSEPAOLhzy\nNWI0JxJaLBTnFMcNNusEhKW5+dluuk0rgmPVZ2pFfXdqOeHEFZWA57RdmJSFqenF\nlyuJ7kWbTsaOknoeXZCg5KskDcnkHswqr7/WESG2pMQ53EWn9ZCZutesohs1o9Oq\nFcKOabzuwLuFpdLRADbQgKOokhvONzBQlDm98vJ8VxQG6w/cwUVzSDhCO3RVX1k6\n2MVwv0m5G2QBB/TORzPayS6R9oQH52tLazVEF9sqa8YZWOJ06k0XsppdQInKRyFL\nrCNUC84tseMJAXoX3PylvAnzn7yKr4MtDo5LLmcOEQVSUm0cwH+rrLxD4Qzln96P\n+hKe3gZtAgMBAAECggEAI11D9GnuuE0BQ1aBmtJtcktgJtTdxzgHoo+b/zmacrdH\nC+44mA1ME3IrdaLkgNodSEbxnNG/zeRYtY1Ut31vsvS/6z/6jWSiVLGlGrEIY8eA\nME6xCevPcOpxTKW1Z0oqda/JmtJtHlhXUdJXl7Pp6ZejSH0Gdmw/3iSRETi/J3NZ\ngRR+ACXSL2+s/yZMzdx5Boh+dIkk35v2BakBOUGfNh0r74c3rUjQCqgAjelhH2Lm\nrOyYzFWG5k+hudluaHdr40Mt7kgtb4VjjUyYwbPpg/wC4Yu4VFPXiBkkKI/iPxNu\nYLmyAgwW2UNL6vwM/Wl20MvUcA2rdv9wgtnWpGZpVQKBgQDwUK6vbwioz3uhJHqV\nfB/lchF/VGa/NVzg3xft5ipfsN+U0aBVmNAej1FQokb42hxaCLKi27O/Y5RR0hQq\n2mND1w0k9kYpoXJLvtjJMh+04eL3jCMKyG64LlVcv7SsPYbhsASsCLZfDh6R8+rs\nXCoPTOJDOhI+OyzhwfVIG07TOwKBgQC25eWFD9yvHFnzrMO7hFqfARcSMZkh91pq\nN9ptVf8WOWHLQ+cAbf0ZIrtgR65//o1zdiDTBUvKsCtLg8y5ijirLSF2gy0Nw9l1\nyTSP93eibe8klDPfJGqScvNeTDX1JStCHRizNsQZa97bZuaj+rRvrsBcQKtyqDG4\nsr3HdMQidwKBgGpz4x3VeqmE14gHZ8HmLR7vi66yzZzv34fBa5E3vxeKe1AqF/My\n8SZIaEXmzLeZrqt3ZOJdnl8ullo0TDi7avN3RNxVt7jjSoUhMljVulPfKDJuQmuZ\nY+p0o92lEjymOKSq2zb12Qt8brBKs4smPH9wmF4dUE1+2x/aUnQxnJZXAoGAekCF\nAEIwAqRyw9wxIQBBCpNR9olMkFWkC+e1iuu2jAZjBdknaTQbv1dl499GFqvxpMHL\nAwJBPQQwjFNW3pYzdse93FemH9NPYdnR1oFVaC8PcGXXnknbKnTHk7phPrFPjuuf\n/PqYbV2bIxBPDkCfpx8p2QApGYSvZ4OiMtYr29UCgYAyERZHFWa6w1sf7SsrYhTt\nsTJE20MEDIePSUNVpBxMXk7RjAm9I+COL9hHFaZZfR2cZXyHw1tGaUaELeROlnlv\nnmyy58QIVH70yRM68TAjjD1rE/6hZCmRAdqwd7TgOMgYkPHrlHGCP6JgTqKTLf7u\n7Sxxgl5/kL1V8Ie3Aik/3g==\n-----END PRIVATE KEY-----\n",
    //   SCOPES,
    //   null
    // ).authorize(function (err, tokens) {

    //   if (err) {
    //     return reject(err)
    //   }

    //   return resolve(tokens.access_token)

    // })

  })

}

const FirebaseService = {

  sendMessage: (deviceToken, title, content) => {

    return getAccessToken().then(token => {

      let config = {
        url: 'https://fcm.googleapis.com/v1/projects/helorobo-521de/messages:send',
        method: 'POST',
        headers: {
          Authorization: 'Bearer ' + token
        },
        data: {
          message: {
            token: deviceToken,
            notification: {
              title: title,
              body: content
            },
            webpush: {
              fcm_options: {
                link: "https://app.helorobo.com/dash"
              }
            }
          }
        }
      }

      return axios.request(config).then(response => {

        console.log('success')

      }).catch(error => {

        if (!(error.response.data.error.details && error.response.data.error.details[0] && error.response.data.error.details[0].errorCode === 'UNREGISTERED')) {

          // @todo loglanacak

        } else {

          helpers.handleAxiosError(error)

        }


      })

    })

  }

}

module.exports = FirebaseService
