const pino = require('pino')()
const { default: axios } = require('axios')
const moment = require('moment')
const { Auth } = require('googleapis')
const enums = require('../../libs/enums')
const helpers = require('../../libs/helpers')

let tokenExpireDate = null
let accessToken = null
const SCOPES = ['https://www.googleapis.com/auth/firebase.messaging']

const renewToken = async () => {
  const diff = moment().diff(moment(tokenExpireDate), 'minutes')
  if (diff >= 0 || tokenExpireDate === null) {
    const jwtData = new Auth.JWT(
      process.env.FIREBASE_CLIENT_EMAIL,
      null,
      process.env.FIREBASE_PRIVATE_KEY,
      SCOPES,
      null
    )

    const token = await jwtData.authorize().catch(err => {
      pino.error({
        timestamp: new Date(),
        message: 'FCM Token Yenilemede Hata Oluştu -> ' + err.message
      })

      throw err
    })

    tokenExpireDate = token.expiry_date
    accessToken = token.access_token

    pino.info({
      timestamp: new Date(),
      message: 'FCM Token Yenilemesi Yapıldı',
      data: JSON.stringify({
        token: token.access_token,
        expiry_date: token.expiry_date
      })
    })
  }
}


const MobileService = {
  SendMessage: async (event, channelSocketCode, chat, message, has_agent, agent_token) => {
    const status = await renewToken().catch(() => false)
    if (status === false) {
      return
    }

    const notification = MobileService.getNotificationBody(message)
    if (!notification) {
      pino.info({
        timestamp: new Date(),
        message: 'Bildirim Gönderilecek Tip Bulunamadı',
        data: JSON.stringify({
          message_id: message.id,
          chat_id: chat.id.toString()
        })
      })
      return
    }

    notification.title = helpers.getName(chat)

    const messageData = {
      notification: notification,
      data: {
        event: event,
        chat_id: chat.id,
      }
    }

    if (has_agent) {
      messageData.token = agent_token
    } else {
      messageData.topic = channelSocketCode
    }

    const config = {
      url: 'https://fcm.googleapis.com/v1/projects/helorobo-acdb8/messages:send',
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + accessToken
      },
      data: {
        message: {
          ...messageData
        }
      }
    }

    await axios.request(config).then(response => {
      pino.error({
        timestamp: new Date(),
        message: 'Mobile Bildirim Gönderildi',
        data: JSON.stringify(config),
        response: JSON.stringify(typeof response.data === 'object' ? response.data : { message: 'Response Yok' })
      })

      // response.name // "projects/helorobo-acdb8/messages/0:16996601548%71b0196f71f"
    }).catch(err => {
      pino.error({
        timestamp: new Date(),
        message: 'Mobile Bildirim Gönderiminde Hata Oluştu -> ' + err.message,
        data: JSON.stringify(config),
        response: JSON.stringify(err.response?.data || { message: 'İstek Yapılamadı' })
      })
    })
  },

  getNotificationBody: (message) => {
    switch (message.type) {
      case enums.message_types.TEXT:
        if (message.vContent.text) {
          return {
            body: message.vContent.bb_code ?
              helpers.getHtmlBbCodeParser().parseString(message.vContent.text) :
              message.vContent.text
          }
        }
        return {
          body: message.vContent.bb_code ?
            helpers.getHtmlBbCodeParser().parseString(message.content) :
            message.content
        }
      case enums.message_types.IMAGE_URL:
        return {
          body: message.vContent.bb_code ?
            helpers.getHtmlBbCodeParser().parseString(message.vContent.caption) :
            message.vContent.caption,
          image: message.content.url,
        }
      case enums.message_types.FACEBOOK_IMAGE_URL:
        return {
          body: [message.vContent.url]
        }
      case enums.message_types.FACEBOOK_VIDEO_URL:
        return {
          body: [message.vContent.url]
        }

      case enums.message_types.FACEBOOK_AUDIO_URL:
        return {
          body: message.vContent.url
        }
      case enums.message_types.FACEBOOK_AUDIO_URLS:
        return {
          body: message.vContent.urls
        }
      case enums.message_types.FACEBOOK_STICKER_URL:
        return {
          body: [message.vContent.url]
        }
      case enums.message_types.FACEBOOK_FILE_URL:
        return {
          body: [message.vContent.url]
        }
      case enums.message_types.FACEBOOK_IMAGE_URLS:
        return {
          body: message.vContent.urls
        }
      case enums.message_types.FACEBOOK_VIDEO_URLS:
        return {
          body: message.vContent.urls?.[0]
        }
      case enums.message_types.FACEBOOK_FILE_URLS:
        return {
          body: message.vContent.items
        }
      case enums.message_types.FACEBOOK_LOCATION:
        return {
          body: `https://maps.google.com/maps?q=${message.vContentLatitude},${message.vContentLongitude}&z=15&output=embed`
        }
      case enums.message_types.FACEBOOK_FALLBACK:
        return {
          body: message.vContent.title + ': ' + message.vContent.content + ', image: ' + message.vContent.url
        }
      case enums.message_types.FACEBOOK_FALLBACK_URLS:
        return {
          body: message.vContent.items
        }
      case enums.message_types.FACEBOOK_REPLY_TO_MESSAGE:
        return {
          body: message.vContent.bb_code ?
            helpers.getHtmlBbCodeParser().parseString(message.vContent.text) :
            message.vContent.text,
        }
      case enums.message_types.FACEBOOK_QUICK_REPLY:
        return {
          body: message.vContent.text
        }
      case enums.message_types.WHATSAPP_IMAGE_URL:
        return {
          body: message.vContent.caption,
          image: message.vContent.url || process.env.BASE_URL + '/message-document/' + message.id + '/' + message.hash
        }

      case enums.message_types.WHATSAPP_VIDEO_URL:
        return {
          body: message.vContent.caption,
          image: message.vContent.url || process.env.BASE_URL + '/message-document/' + message.id + '/' + message.hash
        }
      case enums.message_types.WHATSAPP_VOICE_URL:
        return {
          image: message.vContent.url || process.env.BASE_URL + '/message-document/' + message.id + '/' + message.hash
        }
      case enums.message_types.WHATSAPP_DOCUMENT_URL:
        return {
          body: message.vContent.filename || message.vContent.file_name,
          image: message.vContent.url || process.env.BASE_URL + '/message-document/' + message.id + '/' + message.hash
        }
      case enums.message_types.WHATSAPP_AUDIO_URL:
        return {
          body: message.vContent.url || process.env.BASE_URL + '/message-document/' + message.id + '/' + message.hash
        }
      case enums.message_types.WHATSAPP_CONTACTS:
        return {
          body: message.vContent.contacts
        }
      case enums.message_types.WHATSAPP_LOCATION:
        return {
          image: `https://maps.google.com/maps?q=${message.vContent.latitude},${message.vContent.longitude}&z=15&output=embed`,
        }

      case enums.message_types.WHATSAPP_INTERACTIVE:
        return {
          body: message.vContent.text || message.vContent.caption || ''
        }
      case enums.message_types.WHATSAPP_REPLY_TO_MESSAGE:
        return {
          body: message.vContent.text || message.vContent.caption || ''
        }
      case enums.message_types.INSTAGRAM_VIDEO_URLS:
        return {
          type: enums.app_message_types.VIDEO_URLS,
          image: message.vContent.urls?.[0]
        }
      case enums.message_types.INSTAGRAM_AUDIO_URLS:
        return {
          image: message.vContent.urls?.[0]
        }
      case enums.message_types.INSTAGRAM_FILE_URLS:
        return {
          image: message.vContent.urls?.[0]
        }
      case enums.message_types.INSTAGRAM_IMAGE_URLS:
        return {
          image: message.vContent.urls?.[0]
        }
      case enums.message_types.INSTAGRAM_SHARE_URLS:
        return {
          image: message.vContent.urls?.[0]
        }
      case enums.message_types.INSTAGRAM_STORY_MENTION_URLS:
        return {
          image: message.vContent.urls?.[0]
        }
      case enums.message_types.INSTAGRAM_REPLY_TO_STORY:
        return {
          body: message.content.story.media_url + ' (' + message.content.story.id + ') ' + message.content.text
        }
      case enums.message_types.INSTAGRAM_REPLY_TO_MESSAGE:
        return {
          body: message.vContentBbCode ?
            helpers.getHtmlBbCodeParser().parseString(message.vContent.text) :
            message.vContent.text
        }
      case enums.message_types.INSTAGRAM_QUICK_REPLY:
        return {
          body: message.vContent.text
        }
      // case 'URL':
      //   let text = ''
      //   if (message.content.title && message.content.title.length > 0) {
      //     text += message.content.title + ': '
      //   }
      //   text += process.env.BASE_URL + '/message-document/' + message.id + '/' + message.hash
      //   return {
      //     type: enums.app_message_types.TEXT,
      //     content: {
      //       mid: message.vContent.mid,
      //       text: text
      //     }
      //   }
    }
  }

}

module.exports = MobileService
