const { default: axios } = require('axios')

const handleError = (err) => {
  console.log(err)
  if (!err.success) {
    return 'Advermind: ' + err.errors[0]
  }
}

const AdvermindApiService = {

  getProductsCatalog: async token => {

    let config = {
      url: `${process.env.ADVERMIND_URL}/awa/products/catalog`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }

    return axios.request(config).then(response => {
      if (!response.data.success) {
        throw handleError(response.data)
      }
      return response.data
    })

  },

  getAvaliableFeatures: async token => {

    let config = {
      url: `${process.env.ADVERMIND_URL}/awa/available-features`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }

    return axios.request(config).then(response => {
      if (!response.data.success) {
        throw handleError(response.data)
      }
      return response.data
    })

  },

  getCampaigns: async token => {

    let config = {
      url: `${process.env.ADVERMIND_URL}/awa/campaigns`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }

    return axios.request(config).then(response => {
      if (!response.data.success) {
        throw handleError(response.data)
      }
      return response.data
    })

  },

  adPreview: async (data, token) => {

    let config = {
      url: `${process.env.ADVERMIND_URL}/awa/ad-preview`,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      data: data
    }

    return axios.request(config).then(response => {
      if (!response.data.success) {
        throw handleError(response.data)
      }
      return response.data
    })

  },

  reachEstimate: async (data, token) => {

    let config = {
      url: `${process.env.ADVERMIND_URL}/awa/reach-estimate`,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      data: data
    }

    return axios.request(config).then(response => {
      if (!response.data.success) {
        throw handleError(response.data)
      }
      return response.data
    })

  },

  targetFilters: async token => {

    let config = {
      url: `${process.env.ADVERMIND_URL}/awa/targeting-filters`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }

    return axios.request(config).then(response => {
      if (!response.data.success) {
        throw handleError(response.data)
      }
      return response.data
    })

  },

  accountBalance: async token => {

    let config = {
      url: `${process.env.ADVERMIND_URL}/awa/account-balance`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }

    return axios.request(config).then(response => {
      if (!response.data.success) {
        throw handleError(response.data)
      }
      return response.data
    })

  },

  getTransactions: async token => {

    let config = {
      url: `${process.env.ADVERMIND_URL}/awa/transactions`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }

    return axios.request(config).then(response => {
      if (!response.data.success) {
        throw handleError(response.data)
      }
      return response.data
    })

  },

  getProductsSet: async token => {

    let config = {
      url: `${process.env.ADVERMIND_URL}/awa/products/sets`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }

    return axios.request(config).then(response => {
      if (!response.data.success) {
        throw handleError(response.data)
      }
      return response.data
    })

  },

  creditCart: async (data, token) => {

    let config = {
      url: `${process.env.ADVERMIND_URL}/awa/credit-card`,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      data: data
    }

    return axios.request(config).then(response => {
      if (!response.data.success) {
        throw handleError(response.data)
      }
      return response.data
    })

  },

  createCampaign: async (data, token) => {

    let config = {
      url: `${process.env.ADVERMIND_URL}/awa/create-campaign`,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      data: data
    }

    return axios.request(config).then(response => {
      if (!response.data.success) {
        throw handleError(response.data)
      }
      return response.data
    })

  },

  setStatusCampaign: async (id, status, token) => {

    let config = {
      url: `${process.env.ADVERMIND_URL}/awa/campaigns/${id}/${status}`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }

    return axios.request(config).then(response => {
      if (!response.data.success) {
        throw handleError(response.data)
      }
      return response.data
    })

  },

  getCampaignDetail: async (id, token) => {

    let config = {
      url: `${process.env.ADVERMIND_URL}/awa/campaigns/${id}`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }

    return axios.request(config).then(response => {
      if (!response.data.success) {
        throw handleError(response.data)
      }
      return response.data
    })

  },

  createSession: async ({
    sellerId,
    adAccountId,
    facebookPageId,
    catalogId,
    businessManagerId,
    facebookAccessToken,
    productFeedUrl,
    facebookUserId,
    websiteUrl = undefined,
    facebookPixelId
  }) => {

    let config = {
      url: `${process.env.ADVERMIND_URL}/assa/session`,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.ADVERMIND_SESSION_KEY}`
      },
      data: {
        sellerId: sellerId,
        adAccountId: adAccountId,
        facebookPageId: facebookPageId,
        catalogId: catalogId,
        businessManagerId: businessManagerId,
        facebookAccessToken: facebookAccessToken,
        productFeedUrl: productFeedUrl,
        facebookUserId: facebookUserId,
        websiteUrl: websiteUrl,
        facebookPixelId: facebookPixelId
      }
    }

    return axios.request(config).then(response => {
      if (!response.data.success) {
        throw handleError(response.data)
      }
      return response.data
    })

  },

}

module.exports = AdvermindApiService
