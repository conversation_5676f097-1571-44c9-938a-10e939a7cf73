const axios = require('axios')

const enums = require('../../libs/enums')
const helpers = require('../../libs/helpers')

const getShopifyRequestConfig = (req, action, integration, data) => {

  return {
    url: process.env.INTEGRATION_BASE_URL + `/process`,
    method: 'POST',
    headers: {
      "X-Lang-Code": req.language || req.i18n.language
    },
    data: {
      type: enums.INTEGRATION_TYPES.SHOPIFY,
      action: action,
      data: data,
      extra: {
        store_name: integration.data.store_name,
        admin_access_token: integration.data.admin_access_token || '',
        store_front_key: integration.data.store_front_key,
        version: integration.data.api_version,
        trace_id: req.trace_id
      },
    }
  }

}

const ShopifyIntegrationService = {

  process: (req, integration, action, data = {}) => {

    data = JSON.parse(JSON.stringify(typeof data === 'object' ? data : {}))

    return axios.request(getShopifyRequestConfig(req, action, integration, data)).then(response => response.data).catch(error => {

      return helpers.handleAxiosError(error)

    })

  },

}

module.exports = ShopifyIntegrationService
