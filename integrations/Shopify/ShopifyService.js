const createError = require('http-errors')
const moment = require('moment')
const { default: axios } = require('axios')

const enums = require('./../../libs/enums')
const utils = require('./../../libs/utils')
const helpers = require('./../../libs/helpers')

const ShopifyPayment = require('../../models/ShopifyPayment')

const ShopifyIntegrationService = require('../../integrations/Shopify/ShopifyIntegrationService')

const IntegrationService = require('../../modules/AgentApp/IntegrationService')

const WhatsappApiService = require('../Whatsapp/WhatsappApiService')
const WhatsappService = require('../Whatsapp/WhatsappService')

const ChannelService = require('../../services/ChannelService')

const UrlShorterService = require('../UrlShorter/UrlShorterService')

const __saveCustomerAccesData = (chatIntegration, accessData) => {

  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setCustomerAccessToken(accessData.access_token)
  chatIntegrationData.setCustomerAccessTokenExpiresAt(accessData.expires_at)

  chatIntegration.data = chatIntegrationData.getData()

  chatIntegration.markModified('data')
  return chatIntegration.save()
}

const ShopifyService = {
  __getCartContentData: (req, getCartData, isRequiresApprove, pricePrecision, multiCart = false, chat) => {

    const subTotalContent = req.t('App.integration.send_cart_message.sub_total', {
      total: utils.getCurrencyForIntlNumberFormat(getCartData.total_price, getCartData.currency_code, pricePrecision || 2),
      interpolation: { escapeValue: false },
      lng: chat.vData.getChatLangCode()
    })

    let contents = []

    if (getCartData.total_price) {

      if (!multiCart) {
        contents.push('[BR][/BR]')
      }

      // sepete ait alt toplamı gösteriyoruz
      contents.push(subTotalContent)
      contents.push('[BR][/BR]')

      // genel toplam'ı göstereceğiz
      contents.push(req.t('App.integration.send_cart_message.general_total', {
        total: utils.getCurrencyForIntlNumberFormat(getCartData.total_price, getCartData.currency_code, pricePrecision || 2),
        interpolation: { escapeValue: false },
        lng: chat.vData.getChatLangCode()
      }))
      contents.push('[BR][/BR]')
      contents.push('[BR][/BR]')
    } else {
      // sepet alt toplamı göstereceğiz
      contents.push(subTotalContent)
      contents.push('[BR][/BR]')
    }

    if (isRequiresApprove) {
      contents.push(req.t('App.integration.send_cart_message.approve_message', { lng: chat.vData.getChatLangCode() }))
    }
    return contents.join('')
  },

  getAddressById: (chatIntegration, addressId) => {

    const getAddress = chatIntegration.vData.getIsAddressById(addressId)

    return {
      id: addressId,
      address: getAddress.address1,
      city: getAddress.city,
      town: getAddress.province,
    }
    // return {
    //   text: req.t('App.integration.shopify_send_address_message_caption', {
    //     address: getAddress.address1,
    //     country: getAddress.country,
    //     city: getAddress.city,
    //     province: getAddress.province,
    //     interpolation: {escapeValue: false}
    //   })
    // }

  },

  getAddress: (req, chatIntegration, addressId) => {

    const getAddress = chatIntegration.vData.getIsAddressById(addressId)

    const formData = ShopifyService.getAddAddressFormFields(req)

    formData.fields.forEach(item => {

      switch (item.field_key) {
        case 'address1':
          item.default = getAddress.address1
          break
        case 'city':
          item.default = getAddress.city
          break
        case 'country':
          item.default = getAddress.country
          break
        case 'firstName':
          item.default = getAddress.firstName
          break
        case 'lastName':
          item.default = getAddress.lastName
          break
        case 'phone':
          item.default = getAddress.phone
          break
        case 'province':
          item.default = getAddress.province
          break
        case 'zip':
          item.default = getAddress.zip
          break

        default:
          break
      }
    })

    return {
      fields: formData.fields,
      countries: formData.countries
    }
  },

  deleteAddress: async (chatIntegration, addressId) => {

    const chatIntegrationData = chatIntegration.vData

    chatIntegrationData.deleteGuestAddress(addressId)

    if (chatIntegrationData.getDeliveryAddressId() == addressId || chatIntegrationData.getInvoiceAddressId() == addressId) {
      chatIntegrationData.deleteSelectedAddress()
    }

    chatIntegration.data = chatIntegrationData.getData()
    chatIntegration.markModified('data')
    await chatIntegration.save()

  },

  updateAddress: async (req, integration, chatIntegration, addressId) => {

    const chatIntegrationData = chatIntegration.vData

    const getAddress = chatIntegrationData.getIsAddressById(addressId)

    // Address shopify tarafında güncelleniyor
    const newAddressId = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.UPDATE_ADDRESS, {
      checkoutId: chatIntegrationData.getCheckoutId(),
      input: getAddress
    })

    // Adress bizim tarafta güncelleniyor
    chatIntegrationData.editGuestAddress(newAddressId, getAddress)

    chatIntegration.data = chatIntegrationData.getData()
    chatIntegration.markModified('data')
    await chatIntegration.save()

  },

  editAddress: async (req, integration, chatIntegration, addressId, addressData) => {

    const chatIntegrationData = chatIntegration.vData

    // Seçilen address shopify tarafı ile aynı ise shopify tarafına da iletiliyor
    if (addressId == chatIntegrationData.getDeliveryAddressId()) {
      // Address shopify tarafında güncelleniyor
      const newAddressId = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.UPDATE_ADDRESS, {
        checkoutId: chatIntegrationData.getCheckoutId(),
        input: addressData
      })

      chatIntegrationData.editGuestAddress(newAddressId, addressData)
    } else {
      chatIntegrationData.editGuestAddress(addressId, addressData)
    }

    chatIntegration.data = chatIntegrationData.getData()
    chatIntegration.markModified('data')
    await chatIntegration.save()
  },

  getSummaryMessage: async (req, getCartData, channelType, title, agentId, chat) => {

    // mesaj bilgileri toplanıyor
    const messageData = {
      cart_content: getCartData.carts.join('[BR][/BR]'),
      username: title,
      address: getCartData.cart_data.address.address1 + ', ' + getCartData.cart_data.address.city + ', ' + getCartData.cart_data.address.country,
      total_amount: `${getCartData.cart_data.total_price} ${getCartData.cart_data.currency_code}`,
      tax: getCartData.cart_data.tax,
      cargo_option_fee: `0 ${getCartData.cart_data.currency_code}`,
      interpolation: { escapeValue: false }
    }

    // kargo fiyatı mesaja ekleniyor
    if (getCartData.cart_data.availableShippingRates.shippingRates) {
      if (getCartData.cart_data.availableShippingRates.shippingRates.length > 0) {
        let price = 0
        let currencyCode = 'TRY'

        for (const item of getCartData.cart_data.availableShippingRates.shippingRates) {
          price += Number(item.price.amount)
          currencyCode = item.price.currencyCode
        }

        messageData.cargo_option_fee = `${price} ${currencyCode}`
      }
    }

    // kanal tipine göre mesaj gönderme formtı ayarlanıyor
    let messageType
    let messageContent

    switch (channelType) {

      case enums.channel_types.WHATSAPP_NUMBER:

        messageType = enums.message_types.WHATSAPP_INTERACTIVE
        messageContent = {
          caption: req.t('App.integration.shopify_whatsapp_order_summary_message', messageData),
          sub_type: enums.message_types.BUTTON,
          header: {
            type: 'image',
            image: {
              link: enums.basket_summary_message_image_url
            }
          },
          buttons: [{
            type: 'reply',
            reply: {
              id: utils.generateHash(15),
              title: req.t('Global.chat_message.confirm')
            }
          }]
        }
        break

      case enums.channel_types.INSTAGRAM_ACCOUNT:

        messageType = enums.message_types.INSTAGRAM_GENERIC_BUTTON
        messageContent = {
          image_url: enums.basket_summary_message_image_url,
          default_action: {
            type: "web_url",
            url: enums.basket_summary_message_image_url,
          },
          title: req.t('Global.chat_message.cart'),
          subtitle: req.t('App.integration.shopify_whatsapp_order_summary_message', messageData),
          buttons: [
            {
              type: "postback",
              title: req.t('Global.chat_message.confirm'),
              payload: req.t('Global.chat_message.confirm')
            }
          ]
        }
        break

      case enums.channel_types.LIVE_CHAT:
        messageType = enums.message_types.LIVECHAT_BUTTON
        messageContent = {
          url: enums.basket_summary_message_image_url,
          bb_code: true,
          buttons: [
            {
              id: req.t('Global.chat_message.confirm', { lng: chat.vData.getChatLangCode() }),
              text: req.t('Global.chat_message.confirm', { lng: chat.vData.getChatLangCode() })
            }
          ],
          sub_type: enums.message_types.IMAGE_URL,
          caption: helpers.getBbCodeProviderParser(messageData)
        }
        break

      default:

        messageType = enums.message_types.TEXT
        messageContent = {
          text: req.t('App.integration.shopify_order_summary_message', messageData),
          bb_code: true
        }

        break

    }

    // lazım olan ek bilgileri dahil edelim
    messageContent.next_action = enums.SHOPIFY_BOT_MESSAGE_ACTIONS.CREATE_ORDER
    messageContent.agent_id = agentId
    messageContent.language = req.language
    messageContent.bb_code = true

    return [{
      message_data: messageContent,
      message_type: messageType
    }]
  },

  getCartCaption: async (req, integration, chatIntegration, isRequiresApprove = true, chat) => {

    if (!chatIntegration.vData.getCheckoutId()) {
      throw new createError.BadRequest(req.t('App.errors.integration.empty_cart'))
    }

    // Shopify tarafından checkout bilgileri alınıyor
    const getCartData = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.GET_CART, { id: chatIntegration.vData.getCheckoutId() })

    // sepet bilgileri mesajda gösterilmesi için formatlanıyor
    const cartMessage = getCartData.items.map(item => {
      return req.t('App.integration.send_cart_message.item', {
        emoji: "➡️",
        variant_message: item.subtitle === 'Default Title' ? 'Default' : item.subtitle,
        title: item.title,
        count: item.count,
        price: `${item.list_price} ${getCartData.currency_code}`,
        amount: `${item.sell_price} ${getCartData.currency_code}`,
        interpolation: { escapeValue: false },
        lng: chat.vData.getChatLangCode()
      })
    })

    const seperatedCart = {
      carts: [],
      cart_content: '',
      cart_data: getCartData,
    }
    if (cartMessage.length > 4) {
      for (let index = 0; index < cartMessage.length; index += 4) {
        seperatedCart.carts.push(cartMessage.slice(index, index + 4))
        seperatedCart.cart_content = ShopifyService.__getCartContentData(req, getCartData, isRequiresApprove, 2, true, chat)
      }
    } else {
      seperatedCart.carts.push(cartMessage)
      seperatedCart.cart_content = ShopifyService.__getCartContentData(req, getCartData, isRequiresApprove, 2, false, chat)
    }
    return seperatedCart
  },

  getSelectVariant1: (req, integration, data) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.GET_VARIANT1, { id: data.id })
  },

  getSelectVariant2: (req, integration, data) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.GET_VARIANT2, {
      id: data.id,
      variant1: data.variant1
    })
  },

  getSelectVariant3: (req, integration, data) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.GET_VARIANT3, {
      id: data.id,
      variant1: data.variant1,
      variant2: data.variant2
    })
  },

  updateItemFromCart: async (req, integration, chatIntegration, data) => {

    // Sepetteki ürün güncelleniyor
    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.UPDATE_CART_ITEM, {
      checkoutId: chatIntegration.vData.getCheckoutId(),
      lineId: data.line_id,
      quantity: data.quantity
    })

    // sepetten ürün bilgisi alındı
    const cartItem = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.GET_CART_ITEM, { id: data.line_id })

    return {
      item_count_in_cart: data.quantity,
      product_name: `${cartItem.title} (${cartItem.variant_title})`
    }

  },

  productShareFromCart: async (req, integration, chatIntegration, lineId) => {

    // sepetten ürün bilgisi alındı
    const cartItem = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.GET_CART_ITEM, { id: lineId })

    // Mesaj gönderimi için gereli datalar hazırlandı
    return {
      caption: {
        product_title: cartItem.title,
        base_url: cartItem.store_url,
        product_variant: IntegrationService.getVariantForShopify(cartItem.variant_data),
        product_share_counter: chatIntegration.vData.getProductShareCounter(),
        interpolation: { escapeValue: false },
      },
      productItem: cartItem,
      product_id: cartItem.product_id,
      image: cartItem.image_url
    }

  },

  removeItemFromCart: async (req, integration, chatIntegration, data) => {

    // Ürün Shopify tarafındaki sepetten siliniyor.
    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.REMOVE_ITEM_FROM_CART, {
      checkoutId: chatIntegration.vData.getCheckoutId(),
      lineItemIds: [data.line_id]
    })

    // Seçilen Ürün Shopify tarafından alınıyor.
    const getProduct = await ShopifyService.getProduct(req, integration, { id: data.product_id })

    // Ürünün variant bilgisi alınıyor.
    const variantData = await ShopifyService.getProductVariant(req, integration, { id: data.variant_id })

    return {
      item_count_in_cart: 0,
      product_name: `${getProduct.title} (${variantData.title})`
    }
  },

  createCustomerAccessToken: (req, integration, customerData) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.CREATE_CUSTOMER_ACCESS_TOKEN, customerData)
  },

  getProductVariant: (req, integration, data) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.GET_VARIANT, { id: data.id })
  },

  createCustomer: async (req, chat, integration, chatIntegration, customerData) => {

    const response = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.CREATE_CUSTOMER, customerData)

    const getAccessToken = await ShopifyService.createCustomerAccessToken(req, integration, {
      input: {
        email: customerData.email,
        password: customerData.password
      }
    })

    chat.first_name = customerData.firstName
    chat.last_name = customerData.lastName
    chat.email = customerData.email

    // Shopify da telefon numarası kaydında + bulunmalı
    if (customerData.phone) {
      customerData.phone = customerData.phone[0] == '+' ? customerData.phone : `+${customerData.phone}`
    }

    chat.phone_number = customerData.phone
    await chat.save()

    // Customer Access Token bilgisi bizim tarafımızda kayedeiliyor.
    await __saveCustomerAccesData(chatIntegration, getAccessToken)

    // Customer shopifyda üye olduğu için bizim taraftada bu kayıt yapılıyor
    chatIntegration.ext_id = response.customer.id
    chatIntegration.membership_date = Date.now()
    // chatIntegration.password = helpers.encryptPassword(customerData.password, process.env.APP_SECRET_KEY) // Müşteri şifresi bizim tarafımızda şifrelenerek kaydediliyor.

    return chatIntegration.save()
  },

  getAddCustomerFormFields: async (req, chat) => {

    return {
      fields: [
        {
          title: req.t('Global.form_field.account_info'),
          fields: [
            {
              title: req.t('Global.form_field.first_name'),
              field_type: 'text',
              field_key: 'firstName',
              field_name: 'firstName',
              required: true,
              default: chat.first_name || chat.title
            },
            {
              title: req.t('Global.form_field.last_name'),
              field_type: 'text',
              field_key: 'lastName',
              field_name: 'lastName',
              required: true,
              default: ''
            },
            {
              title: req.t('Global.form_field.email'),
              field_type: 'text',
              field_key: 'email',
              field_name: 'email',
              required: true,
              default: chat.email || ''
            },
            {
              title: req.t('Global.form_field.password'),
              field_type: 'text',
              field_key: 'password',
              field_name: 'password',
              required: true,
              default: ''
            },
            {
              title: req.t('Global.form_field.phone'),
              field_type: 'text',
              field_key: 'phone',
              field_name: 'phone',
              required: false,
              default: chat.phone_number || ''
            }
          ]
        },
      ]
    }
  },

  getAddAddressFormFields: (req) => {
    return {
      fields: [
        {
          title: req.t('Global.form_field.first_name'),
          field_type: 'text',
          field_key: 'firstName',
          field_name: 'firstName',
          required: true,
          default: ''
        },
        {
          title: req.t('Global.form_field.last_name'),
          field_type: 'text',
          field_key: 'lastName',
          field_name: 'lastName',
          required: true,
          default: ''
        },
        {
          title: req.t('Global.form_field.address1'),
          field_type: 'textarea',
          field_key: 'address1',
          field_name: 'address1',
          required: true,
          default: ''
        },
        {
          title: req.t('Global.form_field.phone'),
          field_type: 'text',
          field_key: 'phone',
          field_name: 'phone',
          required: true,
          default: ''
        },
        {
          title: req.t('Global.form_field.country'),
          field_type: 'select',
          field_key: 'country',
          field_name: 'country',
          required: true,
          default: []
        },
        {
          title: req.t('Global.form_field.city'),
          field_type: 'text',
          field_key: 'city',
          field_name: 'city',
          required: true,
          default: ''
        },
        {
          title: req.t('Global.form_field.province'),
          field_type: 'text',
          field_key: 'province',
          field_name: 'province',
          required: true,
          default: ''
        },
        {
          title: req.t('Global.form_field.zip'),
          field_type: 'text',
          field_key: 'zip',
          field_name: 'zip',
          required: false,
          default: ''
        },
      ],
      countries: require('../../libs/json/countries.json')
    }
  },

  createAddress: async (req, integration, chatIntegration, data, email) => {

    const chatIntegrationData = chatIntegration.vData

    if (!chatIntegrationData.getCheckoutId()) {

      const checkoutResponse = await ShopifyService.createCheckOut(req, integration, {
        input: {
          email: email
        }
      })

      chatIntegrationData.setCheckoutId(checkoutResponse.checkout_id)

      chatIntegration.data = chatIntegrationData.getData()
      chatIntegration.markModified('data')

      await chatIntegration.save()
    }

    // Address shopify tarafında güncelleniyor
    const newAddressId = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.UPDATE_ADDRESS, {
      checkoutId: chatIntegrationData.getCheckoutId(),
      input: data.data
    })

    const guestAddresses = chatIntegrationData.getGuestAddresses()

    chatIntegrationData.removeGuestAddresses()

    guestAddresses.forEach((item, index) => {
      item.id = index

      chatIntegrationData.setGuestAddresses(item)
    })

    // Adress bilgisine shopifydan gelen adress id bilgisi de eklendi
    data.data.id = newAddressId.id

    // Bizim tarafta addres kayıtları tutuluyor
    chatIntegrationData.setGuestAddresses(data.data)

    chatIntegration.data = chatIntegrationData.getData()
    chatIntegration.markModified('data')

    await chatIntegration.save()

  },

  addToCart: async (req, integration, chatIntegration, data, email) => {

    const chatIntegrationData = chatIntegration.vData

    // Checkout önceden yapılmış mı diye bakılıyor. yoksa yeni bir checkout alınıyor.
    if (!chatIntegrationData.getCheckoutId()) {

      const checkoutResponse = await ShopifyService.createCheckOut(req, integration, {
        input: {
          email: email
        }
      })

      chatIntegrationData.setCheckoutId(checkoutResponse.checkout_id)

      chatIntegration.data = chatIntegrationData.getData()
      chatIntegration.markModified('data')

      await chatIntegration.save()

    }

    // Üye ise shopify tarafında access_token kontrolü yapılır
    // if (extId) {

    //   const accessTokenExpiresAt = chatIntegrationData.getCustomerAccessTokenExpiresAt()

    //   // Token süresi geçmiş mi kontrol ediliyor
    //   if (moment().isAfter(accessTokenExpiresAt)) {

    //     const getAccessToken = await ShopifyService.createCustomerAccessToken(req, integration, {
    //       input: {
    //         email: customerData.email,
    //         password: customerData.password
    //       }
    //     })

    //     await __saveCustomerAccesData(chatIntegration, getAccessToken)

    //   }

    //   // Customer ve Checkout eşleşmiş mi kontrolü yapılıyor
    //   if ( ! chatIntegrationData.getCustomerAssociate()) {

    //     // Customer ve Checkout eşleştirmesi yapıldı
    //     await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.CREATE_CUSTOMER_ASSOCIATE, {
    //       checkoutId: chatIntegrationData.getCheckoutId(),
    //       customerAccessToken: chatIntegrationData.getCustomerAccessToken()
    //     })

    //     // customer ve checkout eşleşirse bunun kaydını tutuyoruz
    //     chatIntegrationData.setCustomerAssociate(true)

    //     chatIntegration.data = chatIntegrationData.getData()
    //     chatIntegration.markModified('data')

    //     await chatIntegration.save()
    //   }

    // }

    // Seçilen Ürün Shopify tarafından alınıyor.
    const getProduct = await ShopifyService.getProduct(req, integration, { id: data.product_id })

    // Variant bilgisi alınıyor.
    const getSelectedVariant = await ShopifyService.getProductVariant(req, integration, { id: data.variant_id })

    // Checkout içerisine ürün ekleniyor
    const getQuantity = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADD_TO_CART, {
      checkoutId: chatIntegration.vData.getCheckoutId(),
      lineItems: {
        variantId: data.variant_id,
        quantity: data.quantity
      }
    })

    // Kullanıcıya mesaj olarak kart bilgisi ve miktar dönüyor
    return {
      item_count_in_cart: getQuantity.quantity,
      item_name: `${getProduct.title} ` + (getSelectedVariant.title === 'Default Title' ? '' : `(${getSelectedVariant.title})`)
    }

  },

  createCheckOut: (req, integration, data) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.CREATE_CHECKOUT, data)
  },

  getCheckoutUrl: (req, integration, data) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.SEND_CHECKOUT_LINK, data)
  },

  getCartProcess: (req, integration, chatIntegration) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.GET_CART, { id: chatIntegration.vData.getCheckoutId() })
  },

  getCart: async (req, integration, chatIntegration) => {

    const chatIntegrationData = chatIntegration.vData

    let getCartData
    let response = {
      currency_code: '',
      payment_option_id: 0,
      cargo_option_id: 0,
      selected_delivery_address: {},
      selected_invoice_address: {},
      addresses: chatIntegrationData.getGuestAddresses().map(item => {
        return {
          id: item.id,
          title: item.firstName + ' ' + item.lastName,
          content: item.country + ', ' + item.city + ', ' + item.address1
        }
      }),
      cart: {
        additional_cost_fee: 0.00,
        additional_cost_name: '',
        remittance_discount_amount: 0.00,
        remittance_discount_name: '',
        cargo_price: 0.00,
        discount_percentage: 0,
        discount_price: 0.00,
        has_coupon_price: false,
        coupon_price: 0.00,
        has_campaign_price: false,
        campaign_price: 0.00,
        order_note: '',
        items: [],
        campaigns: [],
        sub_total: 0.00,
        total_amount: 0.00
      },
      cargo_options: [],
      discountApplications: [],
      availableShippingRates: {},
      discountCodes: chatIntegrationData.getCheckoutDiscountCode()
    }

    // CheckoutId bilgisi varmı kontrol ediliyor
    if (chatIntegrationData.getCheckoutId()) {

      getCartData = await ShopifyService.getCartProcess(req, integration, chatIntegration)

      response.selected_delivery_address = Object.keys(getCartData.address).length ? {
        id: getCartData.address.id,
        header: getCartData.address.country + ', ' + getCartData.address.city + ', ' + getCartData.address.address1
      } : {}

      response.selected_invoice_address = Object.keys(getCartData.address).length ? {
        id: getCartData.address.id,
        header: getCartData.address.country + ', ' + getCartData.address.city + ', ' + getCartData.address.address1
      } : {}

      response.cart.items = getCartData.items.map(item => {
        return {
          line_id: item.line_id,
          product_id: item.product_id,
          title: item.title,
          subtitle: item.subtitle,
          list_price: item.list_price,
          sell_price: item.sell_price,
          count: item.count,
          stock_increment: 1,
          min_order_count: 1,
          image_url: item.image_url,
          stock_unit: item.stock_unit,
          discounted: '',
          variant_id: item.variant_id,
          discountAllocations: item.discountAllocations
        }
      })

      response.cart.additional_cost_fee = getCartData.tax
      response.cart.additional_cost_name = req.t('App.integration.tax')
      response.cart.sub_total = getCartData.sub_total_price
      response.cart.total_amount = getCartData.total_price
      response.currency_code = getCartData.currency_code
      response.discountApplications = getCartData.discountApplications
      response.availableShippingRates = getCartData.availableShippingRates

    }

    return response

  },

  getProducts: (req, integration, data = {}) => {
    if (!data.after) {
      delete data.after
    }

    if (data.q) {
      data.query = data.q
    }

    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.GET_PRODUCTS, data)
  },

  getProductsByIds: (req, integration, data = {}) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.GET_PRODUCTS_BY_IDS, data)
  },

  getCatalog: async (req, integration, data = {}) => {

    const catalogs = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.GET_CATALOG, {})

    let getProducts
    if (data.category_id) {
      getProducts = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.GET_PRODUCTS_FOR_CATALOG_FILTER, { id: data.category_id })
    } else {
      getProducts = await ShopifyService.getProducts(req, integration, { query: data.query, after: data.after })
    }

    return {
      filter_options: catalogs.data,
      items: getProducts.items,
      pagination_info: getProducts.pagination_info
    }

  },

  getProduct: (req, integration, data = {}) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.GET_PRODUCT, data)
  },

  getCurrencyCode: async (req, integration) => {
    const currencies = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.GET_CURRENCY_CODE)

    return currencies.data
  },

  emptyCart: async (req, integration, chatIntegration) => {

    // Sepetteki ürünler alınıyor
    const getCartData = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.GET_CART, { id: chatIntegration.vData.getCheckoutId() })

    const lineIds = getCartData.items.map(item => item.line_id)

    // Ürün Shopify tarafındaki sepetten siliniyor.
    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.REMOVE_ITEM_FROM_CART, {
      checkoutId: chatIntegration.vData.getCheckoutId(),
      lineItemIds: lineIds
    })

    return { success: true }
  },

  createOrder: async (req, channelType, integration, chatIntegration) => {

    const getCheckoutUrl = await ShopifyService.getCheckoutUrl(req, integration, { id: chatIntegration.vData.getCheckoutId() })

    const shortUrl = await UrlShorterService.CreateShortUrl(getCheckoutUrl.web_url, req.trace_id)

    let messageType
    let messageContent
    switch (channelType) {

      case enums.channel_types.WHATSAPP_NUMBER:
        messageType = enums.message_types.IMAGE_URL
        messageContent = WhatsappApiService.getSendMessageContent({
          caption: req.t('App.success.integration.shopify_order_message', {
            url: shortUrl,
            interpolation: { escapeValue: false }
          }),
          url: enums.order_message_image_url
        })
        break

      default:
        messageType = enums.message_types.TEXT
        messageContent = {
          text: req.t('App.success.integration.shopify_order_message', {
            url: shortUrl,
            interpolation: { escapeValue: false }
          }),
          bb_code: true
        }
        break
    }

    // Müşteri stage bilgisi güncelleniyor
    const chatIntegrationData = chatIntegration.vData

    chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_APPROVE_CART)
    // Müşterinin checkout bilgisini siliyoruz
    chatIntegrationData.removeCheckOutId()
    chatIntegrationData.removeGuestAddresses()
    chatIntegrationData.setProductShareCounter(1)
    chatIntegrationData.setNewOrderData({})
    chatIntegration.data = chatIntegrationData.getData()
    chatIntegration.markModified('data')

    await chatIntegration.save()

    return {
      message_type: messageType,
      message_content: messageContent
    }

  },

  setOauthPaymentMethods: (shop, token, hash) => {

    const config = {
      url: `https://${shop}/admin/api/2022-04/graphql.json`,
      method: 'POST',
      headers: {
        'X-Shopify-Access-Token': token
      },
      data: {
        query: `mutation AppSubscriptionCreate($name: String!, $lineItems: [AppSubscriptionLineItemInput!]!, $returnUrl: URL!, $trialDays: Int!) {
          appSubscriptionCreate(
            name: $name
            returnUrl: $returnUrl
            lineItems: $lineItems
            trialDays: $trialDays
            test: true
          ) {
            userErrors {
              field
              message
            }
            appSubscription {
              id
            }
            confirmationUrl
          }
        }
        `,
        variables: {
          name: shop,
          returnUrl: `${process.env.BASE_URL}/auth/redirect-corporate-shopify?helorobo_hash=${hash}&shop=${shop}`,
          trialDays: 15,
          lineItems: [
            {
              plan: {
                appUsagePricingDetails: {
                  terms: "https://www.helorobo.com/en/Pricing",
                  cappedAmount: {
                    amount: 10.00,
                    currencyCode: "USD"
                  }
                }
              }
            },
            {
              plan: {
                appRecurringPricingDetails: {
                  price: {
                    amount: 99.00,
                    currencyCode: "USD"
                  },
                  interval: "EVERY_30_DAYS"
                }
              }
            }
          ]
        }
      }


    }

    return axios.request(config).then(response => response.data)
  },

  getStoreAccessToken: (shop, code) => {

    const config = {
      url: `https://${shop}/admin/oauth/access_token?client_id=${process.env.SHOPIFY_CLIENT_ID}&client_secret=${process.env.SHOPIFY_SHARED_SECRET_KEY}&code=${code}`,
      method: 'POST'
    }

    return axios.request(config).then(response => response.data)
  },

  appOneTimePurchaseCreate: async (req, integration, data) => {
    const purchase = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_APP_PURCHASE_ONE_TIME_CREATE, data)

    await new ShopifyPayment({
      company_id: integration.company_id,
      app_purchase_one_time_id: purchase.data.appPurchaseOneTime.id,
      app_purchase_one_time_status: purchase.data.appPurchaseOneTime.status,
      app_purchase_one_time_data: purchase.data,
      is_test: data.test || false,
    }).save()

    return purchase.data.confirmationUrl
  },

  getAppOneTimePurchaseStatus: async (req, integration, id) => {
    const purchase = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_APP_PURCHASE_ONE_TIME_STATUS, {
      id: id
    })

    return purchase.data
    // purchase.id
    // purchase.status
    // purchase.createdAt
    // purchase.price.amount
    // purchase.price.currencyCode
  },

  deleteSubscription: async (req, integration, data) => {
    return await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_DELETE_SUBSCRIPTION, data)
  },

  createSubscription: async (req, integration, data) => {
    return await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_CREATE_SUBSCRIPTION, data)
  },

  webhookSub: async (shop, token, topic) => {
    return axios.request({
      url: `https://${shop}/admin/api/2024-07/graphql.json`,
      method: 'POST',
      headers: {
        'X-Shopify-Access-Token': token,
        'Content-Type': 'application/json'
      },
      data: JSON.stringify({
        query: `mutation {
        webhookSubscriptionCreate(
          topic: ${topic},
          webhookSubscription: {
            callbackUrl: "${process.env.WEBHOOK_URL}/webhook/shopify",
            format: JSON
          }
        ) {
          webhookSubscription {
            id
            callbackUrl
          }
          userErrors {
            field
            message
          }
        }
      }`,
        variables: {}
      })
    })
  },

  CheckCreditStatus: async (traceId, company, wabaIdsArray) => {
    let total_cost = 0
    let message_count = 0
    let free_message_count = 0
    let total_payment_cost = 0
    let status = false

    const query = {
      company_id: company._id,
      app_purchase_one_time_id: { $exists: true },
      app_purchase_one_time_status: enums.shopify_app_purchase_one_time_status.ACTIVE,
      deleted_at: { $exists: false }
    }

    if (process.env.PRODUCTION === 'true') {
      query.is_test = false
    }

    const hasPayments = await ShopifyPayment.find(query).sort({ _id: 1 })

    if (hasPayments.length > 0) {
      for (const item of hasPayments) {
        total_payment_cost += Number(item.app_purchase_one_time_data.appPurchaseOneTime.price.amount)
      }

      const analytic = await WhatsappService.GetMessageAnalytics(wabaIdsArray, moment(hasPayments[0].created_at).startOf('days').utc(false).unix(), moment().startOf('days').add(1, 'days').utc(false).unix(), traceId)

      message_count = analytic.message_count
      total_cost = analytic.cost
      free_message_count = analytic.free_message_count

      status = (total_payment_cost - total_cost) <= 0
      if (status) {
        // eğer kredi yoksa otomatik ödeme yöntemi silinmesi gerek
        process.nextTick(() => {
          ChannelService.removeCreditChannels(traceId, company.id)
        })
      } else {
        // eğer kredi varsa otomatik ödeme yöntemi eklemesi yapılması gerek
        process.nextTick(() => {
          ChannelService.addCreditChannels(traceId, company.id)
        })
      }
    } else {
      const analytic = await WhatsappService.GetMessageAnalytics(wabaIdsArray, moment().startOf('months').utc(false).unix(), moment().startOf('months').add(1, 'months').utc(false).unix(), traceId)

      message_count = analytic.message_count
      total_cost = analytic.cost
      free_message_count = analytic.free_message_count
    }

    return {
      total_cost: total_cost * company.vData.getWhatsappProfitMultiplierForCost(),
      message_count: message_count,
      free_message_count: free_message_count,
      total_payment_cost: total_payment_cost,
      usable: !status
    }
  },

  CheckoutDiscountCodeApplyV2: async (req, integration, data) => {
    return await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.SET_CHECKOUT_DISCOUNT, data)
  },

  CheckoutDiscountCodeRemove: async (req, integration, data) => {
    return await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.REMOVE_CHECKOUT_DISCOUNT, data)
  }

}

module.exports = ShopifyService
