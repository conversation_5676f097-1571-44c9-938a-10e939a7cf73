const pino = require('pino')()

const enums = require('../../../../libs/enums')

const AdminShopifyService = require('../../AdminShopifyService')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if (!botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_CARGO) {
    return
  }

  const agent = botData.agent

  const cargo = botData.agent_message.vContent.buttons[0].rows.find(item => item.id == botData.customer_message.vContent.interactive[`${botData.message_type}`].id)
  if (!cargo) {
    return
  }

  const cargoData = await AdminShopifyService.GetSelectedCargo(req, integration, cargo.id)
  if (!cargoData) {
    return
  }

  // DraftOrder içerisine kargo ekleniyor
  await AdminShopifyService.SetCargoOption(req, integration, chatIntegration, {
    title: cargoData.name,
    price: cargoData.rateProvider.price.amount
  })

  const chatIntegrationData = chatIntegration.vData
  chatIntegrationData.setCargoOptionId(cargo.id)
  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')
  await chatIntegration.save()

  // Soket üzerinden agent tarafına sepete kargo eklendiğine dair bilgi gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CARGO_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      cargo_option_id: cargo.id
    }
  }, req.language)

  // Soket üzerinden agenta müşterinin stage bilgisi değiştiğine dair bilgi gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_SELECT_CARGO
    }
  }, req.language)

  const messageData = await AdminShopifyService.GetPaymentOptionsForMessage(req, integration, chat.channel.type)

  for (let index = 0; index < messageData.paymentsForWhatsapp.length; index += 10) {

    let messages = messageData.paymentsForWhatsapp.slice(index, index + 10)

    const botMessageData = {
      message_type: enums.message_types.WHATSAPP_INTERACTIVE,
      message_data: {
        text: index == 0 ? req.t('App.success.integration.whatsapp_payment_type') : req.t('App.success.integration.whatsapp_payment_type_continue'),
        bb_code: true,
        next_action: enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_PAYMENT_OPTION,
        agent_id: agent.id,
        language: req.language,
        buttons: [{
          title: req.t('Global.chat_message.select_payment'),
          rows: messages
        }],
        sub_type: enums.message_types.LIST
      }
    }

    // Ödeme yöntemleri mesaj olarak gönderiliyor.
    await ChatService.addAgentMessage(req, chat.id, botMessageData.message_type, botMessageData.message_data, agent.id, undefined, { mark_as_seen_event: true })

    pino.info({
      trace_id: req.trace_id,
      integration_id: integration.id,
      chat_id: chat.id,
      channel_id: chat.channel.id,
      bot_message_data: JSON.stringify(botMessageData),
      stage: enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_PAYMENT_OPTION
    })
  }
}