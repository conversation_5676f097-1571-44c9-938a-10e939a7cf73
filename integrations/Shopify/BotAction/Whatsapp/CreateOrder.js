const enums = require('../../../../libs/enums')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const AdminShopifyService = require('../../../../integrations/Shopify/AdminShopifyService')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if (!botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.SHOPIFY_BOT_MESSAGE_ACTIONS.CREATE_ORDER) {
    return
  }

  // Agentın gönderdiği mesajda buton id si ile müşterinin butona bastığı id ile aynı mı kontrol ediliyor
  const selectedButton = botData.agent_message.vContent.buttons.find(item => item.reply.id === botData.customer_message.vContent.interactive[`${botData.message_type}`].id)

  if (!selectedButton) {
    return
  }

  const agent = botData.agent

  const isCreditCartId = chatIntegration.vData.getPaymentOptionId() === enums.shopify_credit_cart_id

  const url = await AdminShopifyService.CreateOrder(req, chat, integration, chatIntegration)

  // Soket üzerinden sipariş olduşturulduğuna dair bilgi gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_CREATED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      web_url: url,
    }
  })

  // Soket üzerinden müşteri stage bilgsii değiştiğine dair bilgi gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_APPROVE_CART
    }
  }, req.language)

  // Sipariş oluşturulduğna dair mesaj gönderiliyor
  if (isCreditCartId) {
    return ChatService.addAgentMessage(req, chat.id, enums.message_types.IMAGE_URL, {
      caption: req.t('App.success.integration.shopify_order_credit_cart_message', { url: url, interpolation: { escapeValue: false } }),
      url: integration.vData.getOrderMessageImageUrl() || enums.order_message_image_url,
      bb_code: true
    }, agent.id, undefined, { mark_as_seen_event: true })
  }

  return ChatService.addAgentMessage(req, chat.id, enums.message_types.IMAGE_URL, {
    caption: req.t('App.success.integration.shopify_order_message', { url: url, interpolation: { escapeValue: false } }),
    url: integration.vData.getOrderMessageImageUrl() || enums.order_message_image_url,
    bb_code: true
  }, agent.id, undefined, { mark_as_seen_event: true })
}