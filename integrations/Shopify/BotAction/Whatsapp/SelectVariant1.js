const enums = require('../../../../libs/enums')

const AdminShopifyService = require('../../AdminShopifyService')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if (!botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_VARIANT1) {
    return
  }

  const agent = botData.agent

  // Ürün ID si alındı
  const productId = botData.agent_message.content.bot_data.product_id
  const variantCount = botData.agent_message.content.bot_data.variant_count
  const productName = botData.agent_message.content.bot_data.product_name

  // hangi variantı seçtiğini buluyoruz
  let firstVariantItem = botData.agent_message.vContent.buttons[0].rows.find(item => item.id == botData.customer_message.vContent.interactive[`${botData.message_type}`].id)

  if (!firstVariantItem) {
    return
  }

  // Ürün variant sayısı 1 ise sepete ekleme işlemi yapılmalı
  if (variantCount == 1) {

    const variantData = botData.agent_message.content.bot_data.variants.find(item => item.title == firstVariantItem.title)

    // DraftOrder içerisine ürün ekleniyor
    await AdminShopifyService.AddToCart(req, chat, chatIntegration, integration, {
      product_id: productId,
      variant_id: variantData.variant_id,
      quantity: 1
    })

    // Müşteriye sepetine ürün eklendiğine dair mesaj gönderiliyor ve süreç sonlanıyor
    await ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
      text: req.t('App.success.integration.add_to_cart', {
        item_name: `${productName} (${firstVariantItem.title})`,
        interpolation: { escapeValue: false },
      })
    }, agent.id, undefined, { mark_as_seen_event: true })

    // Soket üzerinden agent tarafına sepete ürün eklendiğine dair bilgi gönderiliyor
    QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.ITEM_ADDED_TO_CART,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_id: chat.id,
        integration_id: integration.id,
        item_name: productName,
      }
    }, req.language)

    // Soket üzerinden agenta müşterinin stage bilgisi değiştiğine dair bilgi gönderiliyor
    return QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_id: chat.id,
        integration_id: integration.id,
        stage: enums.ORDER_STAGES.STAGE_ADD_TO_CART
      }
    }, req.language)
  }

  // variant bilgileri ilk seçilene göre alınıyor
  const getVariant2 = await AdminShopifyService.GetSelectVariant2(req, integration, { id: productId, variant1: firstVariantItem.title })

  if (getVariant2.values.length === 0) {
    return
  }

  for (let index = 0; index < getVariant2.values.length; index += 10) {

    let values = getVariant2.values.slice(index, index + 10)

    await ChatService.addAgentMessage(req, chat.id, enums.message_types.WHATSAPP_INTERACTIVE, {
      text: req.t('App.success.integration.whatsapp_add_to_cart_variant', {
        variant_title: getVariant2.name,
        interpolation: { escapeValue: false }
      }),
      language: req.language,
      agent_id: agent._id,
      next_action: enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_VARIANT2,
      bb_code: true,
      bot_data: {
        product_id: productId,
        variant_count: variantCount,
        variant1: firstVariantItem.title,
        product_name: productName,
        variants: values
      },
      buttons: [{
        title: getVariant2.name,
        rows: values.map((item, index) => {
          return {
            id: index.toString(),
            title: item.title.substring(0, 24),
            description: item.price ? `${item.price} ${item.currency_code}`.substring(0, 71) : ''
          }
        })
      }],
      sub_type: enums.message_types.LIST
    }, agent.id, undefined, { mark_as_seen_event: true })

  }
}