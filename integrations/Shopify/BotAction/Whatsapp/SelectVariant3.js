const enums = require('../../../../libs/enums')

const AdminShopifyService = require('../../AdminShopifyService')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if (!botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_VARIANT3) {
    return
  }

  const agent = botData.agent

  const productId = botData.agent_message.content.bot_data.product_id
  const productName = botData.agent_message.content.bot_data.product_name
  const variant1 = botData.agent_message.content.bot_data.variant1
  const variant2 = botData.agent_message.content.bot_data.variant2

  // hangi variantı seçtiğini buluyoruz
  let thirdVariantItem = botData.agent_message.vContent.buttons[0].rows.find(item => item.id == botData.customer_message.vContent.interactive[`${botData.message_type}`].id)

  if (!thirdVariantItem) {
    return
  }

  const variantData = botData.agent_message.content.bot_data.variants.find(item => item.title == thirdVariantItem.title)

  // DraftOrder içerisine ürün ekleniyor
  await AdminShopifyService.AddToCart(req, chat, chatIntegration, integration, {
    product_id: productId,
    variant_id: variantData.variant_id,
    quantity: 1
  })

  // Müşteriye sepetine ürün eklendiğine dair mesaj gönderiliyor ve süreç sonlanıyor
  await ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
    text: req.t('App.success.integration.add_to_cart', {
      item_name: `${productName} (${variant1}/${variant2}/${thirdVariantItem.title})`,
      interpolation: { escapeValue: false },
    })
  }, agent.id, undefined, { mark_as_seen_event: true })

  // Soket üzerinden agent tarafına sepete ürün eklendiğine dair bilgi gönderiliyor
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ITEM_ADDED_TO_CART,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      item_name: productName,
    }
  }, req.language)

  // Soket üzerinden agenta müşterinin stage bilgisi değiştiğine dair bilgi gönderiliyor
  return QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_ADD_TO_CART
    }
  }, req.language)

}