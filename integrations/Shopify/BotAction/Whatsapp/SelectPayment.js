const enums = require('../../../../libs/enums')
const helpers = require('../../../../libs/helpers')

const AdminShopifyService = require('../../AdminShopifyService')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if (!botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_PAYMENT_OPTION) {
    return
  }

  const agent = botData.agent

  const payment = botData.agent_message.vContent.buttons[0].rows.find(item => item.id == botData.customer_message.vContent.interactive[`${botData.message_type}`].id)
  if (!payment) {
    return
  }

  const chatIntegrationData = chatIntegration.vData
  chatIntegrationData.setPaymentOptionId(payment.id)
  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')
  await chatIntegration.save()

  // Soket üzerinden agent tarafına sepete kargo eklendiğine dair bilgi gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.PAYMENT_OPTION_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      cargo_option_id: payment.id
    }
  }, req.language)

  // Soket üzerinden agenta müşterinin stage bilgisi değiştiğine dair bilgi gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_SELECT_PAYMENT
    }
  }, req.language)

  // Sepet Özetini göndermek için sepet bilgilerini alıyoruz
  const getCartData = await AdminShopifyService.GetCartCaption(req, integration, chatIntegration, chat.channel.type !== enums.channel_types.WHATSAPP_NUMBER, chat)

  const messageData = await AdminShopifyService.GetSummaryMessage(req, getCartData, chat.channel.type, chat.title, agent._id, chat, integration)

  for (const message of messageData) {
    await ChatService.addAgentMessage(req, chat.id, message.message_type, message.message_data, agent._id, undefined, { mark_as_seen_event: true })
    await helpers.sleepFunction(1000)
  }

}