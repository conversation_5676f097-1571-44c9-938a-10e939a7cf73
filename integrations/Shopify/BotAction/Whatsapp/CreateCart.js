const enums = require('../../../../libs/enums')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

const SendAddressMessageForAddress = require('../../../../integrations/Shopify/BotAction/SendAddressMessageForAddress')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if (!botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.SHOPIFY_BOT_MESSAGE_ACTIONS.CREATE_CART) {
    return
  }

  // Agentın gönderdiği mesajda buton id si ile müşterinin butona bastığı id ile aynı mı kontrol ediliyor
  const selectedButton = botData.agent_message.vContent.buttons.find(item => item.reply.id === botData.customer_message.vContent.interactive[`${botData.message_type}`].id)

  if (!selectedButton) {
    return
  }

  const agent = botData.agent

  // Soket üzerinden Kart oluşturuldu bilgisi gönderiliyor.
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CREATED_CART,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
    }
  }, req.i18n.language)

  // Adressler bizm tarafımızda kayıtlı. adresleri alıyoruz
  const messageData = await SendAddressMessageForAddress(req, chat, chatIntegration, integration)
  if (messageData === false) {
    return
  }

  // Müşteriye addreslerini seçmesi için bot mesajı gönderiliyor
  return ChatService.addAgentMessage(req, chat.id, enums.message_types.WHATSAPP_INTERACTIVE, {
    text: req.t('App.success.integration.customer_address_message'),
    next_action: enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS,
    agent_id: agent.id,
    language: req.language,
    bb_code: true,
    buttons: [{
      title: req.t('Global.chat_message.select_address'),
      rows: messageData.whatsapp_message_data
    }],
    sub_type: enums.message_types.LIST
  }, agent.id, undefined, { mark_as_seen_event: true })
}