const enums = require('./../../../../libs/enums')

const AdminShopifyService = require('../../AdminShopifyService')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const MultiProductsShare = require('../../../../modules/AgentApp/MultiProductsShare')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

module.exports = async (req, chat, integration, chatIntegration) => {

  const botData = await GetInteractiveData(chat)

  if (!botData) {
    return
  }

  const agent = botData.agent

  if (botData.agent_message.vContent.next_action !== enums.SHOPIFY_BOT_MESSAGE_ACTIONS.ADD_TO_CART) {
    return
  }

  // Agentın gönderdiği mesajda buton id si ile müşterinin butona bastığı id ile aynı mı kontrol ediliyor
  const selectedButton = botData.agent_message.vContent.buttons.find(item => item.reply.id === botData.customer_message.vContent.interactive[`${botData.message_type}`].id)

  if (!selectedButton) {
    return
  }

  // Ürün ID si alındı
  const productId = botData.agent_message.content.bot_data.product_id

  // Çoklu Ürün Paylaşımı var mı kontrolü yapılır.
  if (botData.agent_message.vBotData.getMultiShare()) {
    return MultiProductsShare(req, chat, integration, chatIntegration, botData.agent_message, agent)
  }

  // Seçilen Ürünün ilk variant seçenekleri alınır
  const getVariant = await AdminShopifyService.GetSelectVariant1(req, integration, { id: productId })

  if (getVariant.variant_count === 1) {
    const messageData = await AdminShopifyService.AddToCart(req, chat, chatIntegration, integration, {
      product_id: productId,
      variant_id: getVariant.values[0].variant_id,
      quantity: 1
    })

    await ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
      text: req.t('App.success.integration.add_to_cart', {
        item_name: messageData.item_name,
        interpolation: { escapeValue: false },
        lng: chat.vData.getChatLangCode()
      })
    }, agent.id, undefined, { mark_as_seen_event: true })

    // Soket üzerinden sepete ürün eklendiğine dair bilgi gönderiliyor
    await QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.ITEM_ADDED_TO_CART,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_id: chat.id,
        integration_id: integration.id,
        item_name: messageData.product_title,
      }
    }, req.language)

    // Soket üzerinden müşteri durumu hakkında bilgi gönderiliyor
    await QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_id: chat.id,
        integration_id: integration.id,
        stage: enums.ORDER_STAGES.STAGE_ADD_TO_CART
      }
    }, req.language)

    return
  }

  for (let index = 0; index < getVariant.values.length; index += 10) {

    let values = getVariant.values.slice(index, index + 10)

    await ChatService.addAgentMessage(req, chat.id, enums.message_types.WHATSAPP_INTERACTIVE, {
      text: req.t('App.success.integration.whatsapp_add_to_cart_variant', {
        variant_title: getVariant.name,
        interpolation: { escapeValue: false }
      }),
      language: req.language,
      agent_id: agent._id,
      next_action: enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_VARIANT1,
      bb_code: true,
      bot_data: {
        product_id: productId,
        variant_count: getVariant.variant_count,
        product_name: getVariant.product_name,
        variants: values
      },
      buttons: [{
        title: getVariant.name,
        rows: values.map((item, index) => {
          return {
            id: index.toString(),
            title: item.title.substring(0, 24),
            description: item.price ? `${item.price} ${item.currency_code}`.substring(0, 71) : ''
          }
        })
      }],
      sub_type: enums.message_types.LIST
    }, agent.id, undefined, { mark_as_seen_event: true })
  }

}