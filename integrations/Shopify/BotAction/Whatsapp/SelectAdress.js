const enums = require('../../../../libs/enums')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const AdminShopifyService = require('../../AdminShopifyService')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if (!botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS) {
    return
  }

  const agent = botData.agent

  // Bot mesajı içirisindeki data alınıyor
  const address = botData.agent_message.vContent.buttons[0].rows.find(item => item.id == botData.customer_message.vContent.interactive[`${botData.message_type}`].id)
  if (!address) {
    return
  }

  const seletedCustomerAddress = await AdminShopifyService.GetSelectedCustomerAddress(req, chatIntegration, integration, address.id)

  delete seletedCustomerAddress.id

  await AdminShopifyService.SelectAddressForDraftOrder(req, chatIntegration, integration, seletedCustomerAddress)

  const chatIntegrationData = chatIntegration.vData
  chatIntegrationData.setDeliveryAddressId(address.id)
  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')
  await chatIntegration.save()

  // Addres değiştiğine dair bilgiler soket tarafından veriliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.DELIVERY_ADDRESS_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id
    }
  }, req.language)

  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.INVOICE_ADDRESS_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id
    }
  }, req.language)

  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_APPROVE_CART
    }
  }, req.language)

  const messageData = await AdminShopifyService.GetCargoOptionsForMessage(req, integration, chat.channel.type)

  const botMessageData = {
    message_type: enums.message_types.WHATSAPP_INTERACTIVE,
    message_data: {
      text: req.t('App.success.integration.customer_address_message'),
      next_action: enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_CARGO,
      agent_id: agent.id,
      language: req.language,
      buttons: [{
        title: req.t('Global.chat_message.select_address'),
        rows: messageData.cargoNamesForWhatsapp
      }],
      bb_code: true,
      sub_type: enums.message_types.LIST
    }
  }

  // Müşteriye addreslerini seçmesi için bot mesajı gönderiliyor
  await ChatService.addAgentMessage(req, chat.id, botMessageData.message_type, botMessageData.message_data, agent.id, undefined, { mark_as_seen_event: true })

}