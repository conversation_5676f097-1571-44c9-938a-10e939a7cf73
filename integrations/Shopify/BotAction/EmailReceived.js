const enums = require('../../../libs/enums')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')
const AdminShopifyService = require('../AdminShopifyService')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Email Konrol Regexi
  const regex = new RegExp(/^([a-z0-9_\.\-])+\@(([a-z0-9\-])+\.)+([a-z0-9]{2,4})+$/)

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  const agent = botData.agent

  //Email uygun mu diye Kontrol Edildi. Yoksa tekrar emailini almak için mesaj gönderdik
  if (!regex.test(botData.customer_message.content.text.toLowerCase())) {

    return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
      text: req.t('App.success.ask_form_questions.email'),
      next_action: enums.SHOPIFY_BOT_MESSAGE_ACTIONS.EMAIL_RECEIVED,
      agent_id: agent.id,
      language: req.language,
    }, agent.id, undefined, { mark_as_seen_event: true })

  }

  // Müşterinini bize göndermiş olduğu email i kendi tarafımıza kaydediyoruz
  chat.email = botData.customer_message.content.text.toLowerCase()
  await chat.save()

  if (chatIntegration.ext_id) {
    return
  }

  // otomatik shopify tarafından müşteriyi bağlıyoruz.
  const customers = await AdminShopifyService.CustomerFilterByQuery(req, integration, chat.email)
  if (customers.length !== 1) {
    return
  }

  chatIntegration.ext_id = customers[0].id
  await chatIntegration.save()

  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CUSTOMER_PAIRED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id
    }
  }, req.language)
}