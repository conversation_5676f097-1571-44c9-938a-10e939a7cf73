const enums = require('../../../libs/enums')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const CreateCartForWhatapp = require('./Whatsapp/CreateCart')
const CreateCartForInstagram = require('./Instagram/CreateCart')

const SendAddressMessageShopifyForAddress = require('./SendAddressMessageForAddress')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return CreateCartForWhatapp(req, chat, integration, chatIntegration)
  }

  // Instagram için burası kullanılacak
  if (chat.channel.type === enums.channel_types.INSTAGRAM_ACCOUNT) {
    return CreateCartForInstagram(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  // Sepeti onaylamak istiyorsa 1 e basması gerekmekte
  if (parseInt(botData.customer_message.content.text) !== 1) {
    return
  }

  const agent = botData.agent

  // Soket üzerinden Kart oluşturuldu bilgisi gönderiliyor.
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CREATED_CART,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
    }
  }, req.language)

  // Adresler alınıyor
  const messageData = await SendAddressMessageShopifyForAddress(req, chat, chatIntegration, integration)
  if (messageData === false) {
    return
  }

  // Müşteriye addreslerini seçmesi için bot mesajı gönderiliyor
  return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
    text: req.t('App.success.integration.customer_address_message', {
      interpolation: { escapeValue: false }
    }) + messageData.message_data,
    next_action: enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS,
    agent_id: agent.id,
    language: req.language,
    bot_data: messageData.address_dto,
    bb_code: true,
  }, agent.id, undefined, { mark_as_seen_event: true })
}