const enums = require('../../../libs/enums')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const SelectAddressForWhatapp = require('./Whatsapp/SelectAdress')

const AdminShopifyService = require('../AdminShopifyService')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return SelectAddressForWhatapp(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  const agent = botData.agent

  // Bot mesajı içirisindeki data alınıyor
  const address = botData.bot_data.addresses.find(item => item.address_index === parseInt(botData.customer_message.vContentText))
  if (!address) {
    return
  }

  const seletedCustomerAddress = await AdminShopifyService.GetSelectedCustomerAddress(req, chatIntegration, integration, address.address_id)

  delete seletedCustomerAddress.id

  await AdminShopifyService.SelectAddressForDraftOrder(req, chatIntegration, integration, seletedCustomerAddress)

  const chatIntegrationData = chatIntegration.vData
  chatIntegrationData.setDeliveryAddressId(address.address_id)
  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')
  await chatIntegration.save()

  // Addres değiştiğine dair bilgiler soket tarafından veriliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.DELIVERY_ADDRESS_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id
    }
  }, req.language)

  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.INVOICE_ADDRESS_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id
    }
  }, req.language)

  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_APPROVE_CART
    }
  }, req.language)

  const messageData = await AdminShopifyService.GetCargoOptionsForMessage(req, integration, chat.channel.type)

  const botMessageData = {
    message_type: enums.message_types.TEXT,
    message_data: {
      text: req.t('App.integration.send_cargo_message', {
        cargo_options: messageData.cargoNamesForText.join('[BR][/BR]'),
        interpolation: { escapeValue: false }
      }),
      next_action: enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_CARGO,
      agent_id: agent.id,
      language: req.language,
      bot_data: messageData.cargos,
      bb_code: true,
    }
  }

  // Kargo seçenekleri mesaj olarak gönderiliyor
  await ChatService.addAgentMessage(req, chat.id, botMessageData.message_type, botMessageData.message_data, agent.id, undefined, { mark_as_seen_event: true })

}