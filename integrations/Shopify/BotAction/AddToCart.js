const enums = require('./../../../libs/enums')
const utils = require('./../../../libs/utils')

const AdminShopifyService = require('../AdminShopifyService')

const AddToCartForWhatapp = require('./Whatsapp/AddToCart')
const AddToCartForInstagram = require('./Instagram/AddToCart')

const MultiProductsShare = require('../../../modules/AgentApp/MultiProductsShare')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')

const getVariantData = (variants, channelType) => {

  let index = 1
  let botData = []

  const variantMessage = variants.values.map(item => {

    let variantName = `${utils.getMessageEmoji(index, channelType)} ${item.title}`

    botData.push({
      index: index,
      title: item.title,
      variant_id: item.variant_id ? item.variant_id : undefined
    })

    index++

    if (variants.variant_count == 1) {
      return variantName + ` (${item.price} ${item.currency_code})`
    } else {
      return variantName
    }

  }).join('[BR][/BR]')

  return {
    message: variantMessage,
    bot_data: botData
  }

}

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return AddToCartForWhatapp(req, chat, integration, chatIntegration)
  }

  // Instgram için burası kullanılacak
  if (chat.channel.type === enums.channel_types.INSTAGRAM_ACCOUNT) {
    return AddToCartForInstagram(req, chat, integration, chatIntegration)
  }

  const botData = await GetBotData(chat, 50)

  if (!botData) {
    return
  }

  const agent = botData.agent

  const customerSelectedProductIndex = parseInt(botData.customer_message.vContentText)

  if (customerSelectedProductIndex < 0) {
    return false
  }

  // Çoklu Ürün Paylaşımı var mı kontrolü yapılır.
  if (customerSelectedProductIndex === 0 && botData.agent_message.vBotData.getMultiShare()) {
    return MultiProductsShare(req, chat, integration, chatIntegration, botData.agent_message, agent)
  }

  // Ürün mesajı bulunuyor
  const productShareMessage = botData.messages.find(message => {

    if (message.from_type !== enums.message_from_types.AGENT) {
      return false
    }

    if (message.content && message.content.bot_data && message.content.bot_data.product_share_counter) {

      if (message.content.bot_data.product_share_counter === customerSelectedProductIndex) {
        return true
      }

    }

    return false

  })

  if (!productShareMessage) {
    return false
  }

  if (!productShareMessage.content.bot_data) {
    return false
  }

  // agent mesajı içerisinden product id bilgisi alındı
  const productId = productShareMessage.content.bot_data.product_id

  // Seçilen Ürünün ilk variant seçenekleri alınır
  const getVariant = await AdminShopifyService.GetSelectVariant1(req, integration, { id: productId })

  if (getVariant.variant_count === 1) {
    const messageData = await AdminShopifyService.AddToCart(req, chat, chatIntegration, integration, {
      product_id: productId,
      variant_id: getVariant.values[0].variant_id,
      quantity: 1
    })

    await ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
      text: req.t('App.success.integration.add_to_cart', {
        item_name: messageData.item_name,
        interpolation: { escapeValue: false },
        lng: chat.vData.getChatLangCode()
      })
    }, agent.id, undefined, { mark_as_seen_event: true })

    // Soket üzerinden sepete ürün eklendiğine dair bilgi gönderiliyor
    await QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.ITEM_ADDED_TO_CART,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_id: chat.id,
        integration_id: integration.id,
        item_name: messageData.product_title,
      }
    }, req.language)

    // Soket üzerinden müşteri durumu hakkında bilgi gönderiliyor
    await QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_id: chat.id,
        integration_id: integration.id,
        stage: enums.ORDER_STAGES.STAGE_ADD_TO_CART
      }
    }, req.language)

    return
  }

  // Mesaj gönderimi için data hazırlandı
  const getData = getVariantData(getVariant, chat.channel.type)

  return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
    text: req.t('App.success.integration.add_to_cart_variant', {
      variant_title: getVariant.name,
      variant: getData.message,
      interpolation: { escapeValue: false }
    }),
    language: req.language,
    agent_id: agent._id,
    next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_VARIANT1,
    bb_code: true,
    bot_data: {
      product_id: productId,
      variant_count: getVariant.variant_count,
      product_name: getVariant.product_name,
      variants: getData.bot_data
    }
  }, agent.id, undefined, { mark_as_seen_event: true })

}
