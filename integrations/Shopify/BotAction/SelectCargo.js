const pino = require('pino')()

const enums = require('../../../libs/enums')

const AdminShopifyService = require('../AdminShopifyService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')

const SelectCargoForWhatapp = require('./Whatsapp/SelectCargo')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return SelectCargoForWhatapp(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  const agent = botData.agent

  const cargoOptions = botData.agent_message.content.bot_data

  // Müşterinin hangi mesajı seçtiğini anlıyoruz
  const cargo = cargoOptions.find(a => a.cargo_index === Number(botData.customer_message.vContentText))
  if (!cargo) {
    return
  }

  const cargoData = await AdminShopifyService.GetSelectedCargo(req, integration, cargo.cargo_option_id)
  if (!cargoData) {
    return
  }

  // DraftOrder içerisine kargo ekleniyor
  await AdminShopifyService.SetCargoOption(req, integration, chatIntegration, {
    title: cargoData.name,
    price: cargoData.rateProvider.price.amount
  })

  const chatIntegrationData = chatIntegration.vData
  chatIntegrationData.setCargoOptionId(cargo.cargo_option_id)
  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')
  await chatIntegration.save()

  // Soket üzerinden agent tarafına sepete kargo eklendiğine dair bilgi gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CARGO_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      cargo_option_id: cargo.cargo_option_id
    }
  }, req.language)

  // Soket üzerinden agenta müşterinin stage bilgisi değiştiğine dair bilgi gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_SELECT_CARGO
    }
  }, req.language)

  const messageData = await AdminShopifyService.GetPaymentOptionsForMessage(req, integration, chat.channel.type)

  const botMessageData = {
    message_type: enums.message_types.TEXT,
    message_data: {
      text: req.t('App.success.integration.payment_type', {
        payment_options: messageData.paymentOptionsTexts.join('[BR][/BR]'),
        interpolation: { escapeValue: false }
      }),
      bb_code: true,
      next_action: enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_PAYMENT_OPTION,
      agent_id: agent.id,
      language: req.language,
      bot_data: messageData.payments
    }
  }
  // Ödeme yöntemleri mesaj olarak gönderiliyor.
  await ChatService.addAgentMessage(req, chat.id, botMessageData.message_type, botMessageData.message_data, agent.id, undefined, { mark_as_seen_event: true })

  pino.info({
    trace_id: req.trace_id,
    integration_id: integration.id,
    chat_id: chat.id,
    channel_id: chat.channel.id,
    bot_message_data: JSON.stringify(botMessageData),
    stage: enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_PAYMENT_OPTION
  })

}