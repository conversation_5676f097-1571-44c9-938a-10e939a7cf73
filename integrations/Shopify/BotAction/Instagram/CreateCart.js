const enums = require('../../../../libs/enums')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const GetBotData = require('../../../../modules/AgentApp/BotAction/GetBotData')

const SendAddressMessageForAddress = require('../../../../integrations/Shopify/BotAction/SendAddressMessageForAddress')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  if (req.t('Global.chat_message.confirm_cart') !== botData.customer_message.content.text) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.SHOPIFY_BOT_MESSAGE_ACTIONS.CREATE_CART) {
    return
  }

  const agent = botData.agent

  // Soket üzerinden Kart oluşturuldu bilgisi gönderiliyor.
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CREATED_CART,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
    }
  }, req.i18n.language)

  // Adressler bizm tarafımızda kayıtlı. adresleri alıyoruz
  const messageData = await SendAddressMessageForAddress(req, chat, chatIntegration, integration)
  if (messageData === false) {
    return
  }

  if (!messageData.message_data) {
    return
  }
  // Müşteriye addreslerini seçmesi için bot mesajı gönderiliyor
  return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
    text: req.t('App.success.integration.customer_address_message', {
      interpolation: { escapeValue: false }
    }) + messageData.message_data,
    next_action: enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS,
    agent_id: agent.id,
    language: req.language,
    bot_data: messageData.address_dto,
    bb_code: true,
  }, agent.id, undefined, { mark_as_seen_event: true })
}