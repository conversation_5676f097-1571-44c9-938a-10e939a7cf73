const enums = require('../../../libs/enums')

const QueueService = require('../../../services/QueueService')
const ChatService = require('../../../services/ChatService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const CreateOrderForWhatapp = require('./Whatsapp/CreateOrder')

const AdminShopifyService = require('../AdminShopifyService')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return CreateOrderForWhatapp(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  // Sepeti onaylamak istiyorsa alttakilerden birisini seçmesi gerekli
  if (!['1', 'Onayla', 'Approve', 'Approuver', 'يوافق'].includes(botData.customer_message.content.text)) {
    return
  }

  const agent = botData.agent

  const isCreditCartId = chatIntegration.vData.getPaymentOptionId() === enums.shopify_credit_cart_id

  const url = await AdminShopifyService.CreateOrder(req, chat, integration, chatIntegration)

  // Soket üzerinden sipariş olduşturulduğuna dair bilgi gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_CREATED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      web_url: url
    }
  })

  // Soket üzerinden müşteri stage bilgsii değiştiğine dair bilgi gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_APPROVE_CART
    }
  }, req.language)

  // Sipariş oluşturulduğna dair mesaj gönderiliyor
  if (isCreditCartId) {
    return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
      text: req.t('App.success.integration.shopify_order_credit_cart_message', { url: url, interpolation: { escapeValue: false } }),
      bb_code: true
    }, agent.id, undefined, { mark_as_seen_event: true })
  }

  return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
    text: req.t('App.success.integration.shopify_order_message', { url: url, interpolation: { escapeValue: false } }),
    bb_code: true
  }, agent.id, undefined, { mark_as_seen_event: true })
}