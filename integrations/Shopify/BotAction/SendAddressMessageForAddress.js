const enums = require('../../../libs/enums')
const utils = require('../../../libs/utils')

const ChatService = require('../../../services/ChatService')

const SendAddressMessageDto = require('../../../dtos/BotAction/SendAddressMessageDto')

const AdminShopifyService = require('../AdminShopifyService')

module.exports = async (req, chat, chatIntegration, integration) => {

  const addresses = []
  const messageCaptions = []
  const messageForWhatsapp = []

  const sendAddressMessageDto = new SendAddressMessageDto()

  const customerAddress = await AdminShopifyService.GetCustomerAddresses(req, chatIntegration, integration)

  //Kullanıcının paylaşılacak adresi yoksa
  if (customerAddress.length === 0) {
    await ChatService.addSystemMessage(req, chat, enums.message_types.TEXT, {
      text: req.t('App.integration.send_empty_address_message')
    }, { mark_as_seen_event: true })

    return false
  }

  //misafir kullanıcın adres bilgilerini geziyoruz
  customerAddress.forEach(item => {

    //Botdata da kullanmak için sakladık.
    addresses.push({
      address_id: item.id,
      address_index: addresses.length + 1
    })

    messageCaptions.push(req.t('App.integration.shopify_send_address_message_caption', {
      emoji: utils.getMessageEmoji(addresses.length, chat.channel.type),
      address: item.address1,
      city: item.city,
      country: item.country,
      province: item.province,
      interpolation: { escapeValue: false }
    }))

    // Whatsapp List Mesaj için data alınıyor
    let title = `${item.city} / ${item.country}`

    messageForWhatsapp.push({
      id: item.id.toString(),
      title: title.substring(0, 23),
      description: item.address1.substring(0, 71),
    })

  })

  sendAddressMessageDto.setBotData(addresses)

  return {
    address_dto: sendAddressMessageDto.getBotData(),
    message_data: messageCaptions.join('[BR][/BR][BR][/BR]'),
    whatsapp_message_data: messageForWhatsapp
  }
}