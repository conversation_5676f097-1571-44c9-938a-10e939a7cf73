const enums = require('../../../libs/enums')
const utils = require('../../../libs/utils')

const AdminShopifyService = require('../AdminShopifyService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')

const SelectVariant2ForWhatapp = require('./Whatsapp/SelectVariant2')

const getVariantData = (variants, variantCount, channelType) => {

  let index = 1
  let botData = []

  const variantMessage = variants.values.map(item => {

    let variantName = `${utils.getMessageEmoji(index, channelType)} ${item.title}`

    botData.push({
      index: index,
      title: item.title,
      variant_id: item.variant_id ? item.variant_id : undefined
    })

    index++

    if (variantCount == 3) {
      return variantName + ` (${item.price} ${item.currency_code})`
    } else {
      return variantName
    }

  }).join('[BR][/BR]')

  return {
    message: variantMessage,
    bot_data: botData
  }

}

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return SelectVariant2ForWhatapp(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_VARIANT2) {
    return
  }

  const agent = botData.agent

  // Ürün ID si alındı
  const productId = botData.agent_message.content.bot_data.product_id
  const variantCount = botData.agent_message.content.bot_data.variant_count
  const variant1 = botData.agent_message.content.bot_data.variant1
  const productName = botData.agent_message.content.bot_data.product_name

  let secondVariantItem = botData.bot_data.variants.find(variant => variant.index == botData.customer_message.vContentText)

  if (!secondVariantItem) {
    return
  }

  // Variant sayısı 2 adet ise doğrudan sepete ekleme işlemi yapılır
  if (variantCount == 2) {

    const variantData = botData.agent_message.content.bot_data.variants.find(item => item.title == secondVariantItem.title)

    // DraftOrder içerisine ürün ekleniyor
    await AdminShopifyService.AddToCart(req, chat, chatIntegration, integration, {
      product_id: productId,
      variant_id: variantData.variant_id,
      quantity: 1
    })

    // Müşteriye sepetine ürün eklendiğine dair mesaj gönderiliyor ve süreç sonlanıyor
    await ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
      text: req.t('App.success.integration.add_to_cart', {
        item_name: `${productName} (${variant1}/${secondVariantItem.title})`,
        interpolation: { escapeValue: false },
      })
    }, agent.id, undefined, { mark_as_seen_event: true })

    // Soket üzerinden agent tarafına sepete ürün eklendiğine dair bilgi gönderiliyor
    QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.ITEM_ADDED_TO_CART,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_id: chat.id,
        integration_id: integration.id,
        item_name: productName,
      }
    }, req.language)

    // Soket üzerinden agenta müşterinin stage bilgisi değiştiğine dair bilgi gönderiliyor
    return QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_id: chat.id,
        integration_id: integration.id,
        stage: enums.ORDER_STAGES.STAGE_ADD_TO_CART
      }
    }, req.language)

  }

  // variant bilgileri ilk seçilene göre alınıyor
  const getVariant3 = await AdminShopifyService.GetSelectVariant3(req, integration, { id: productId, variant1: variant1, variant2: secondVariantItem.title })

  if (getVariant3.values.length === 0) {
    return
  }

  // Mesaj gönderimi için data hazırlandı
  const getData = getVariantData(getVariant3, variantCount, chat.channel.type)

  return ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
    text: req.t('App.success.integration.add_to_cart_variant', {
      variant_title: getVariant3.name,
      variant: getData.message,
      interpolation: { escapeValue: false }
    }),
    language: req.language,
    agent_id: agent._id,
    next_action: enums.SHOPIFY_BOT_MESSAGE_ACTIONS.SELECT_VARIANT3,
    bb_code: true,
    bot_data: {
      product_id: productId,
      variant_count: variantCount,
      variant1: variant1,
      variant2: secondVariantItem.title,
      product_name: productName,
      variants: getData.bot_data
    },
  }, agent.id, undefined, { mark_as_seen_event: true })
}