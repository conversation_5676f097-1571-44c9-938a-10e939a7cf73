const createError = require('http-errors')
const faker = require('faker')
const { default: axios } = require('axios')

const enums = require('../../libs/enums')
const utils = require('../../libs/utils')
const helpers = require('../../libs/helpers')

const User = require('../../models/User')
const Integration = require('../../models/Integration')
const ChatIntegration = require('../../models/ChatIntegration')
const Channel = require('../../models/Channel')
const LivechatScript = require('../../models/LivechatScript')
const CompanyHasPackage = require('../../models/CompanyHasPackage')
const Company = require('../../models/Company')
const Package = require('../../models/Package')
const ShopifyInfo = require('../../models/ShopifyInfo')

const ShopifyIntegrationService = require('./ShopifyIntegrationService')
const ShopifyService = require('./ShopifyService')

const IntegrationService = require('../../modules/AgentApp/IntegrationService')

const ChatService = require('../../services/ChatService')
const OrderService = require('../../services/OrderService')
const UploadService = require('../../services/UploadService')
const QueueService = require('../../services/QueueService')
const ChannelService = require('../../services/ChannelService')
const ThinkerService = require('../../services/ThinkerService')
const HeloBotService = require('../../services/HelobotService')

const WhatsappApiService = require('../../integrations/Whatsapp/WhatsappApiService')

const UrlShorterService = require('../UrlShorter/UrlShorterService')

const AdminShopifyService = {

  getDraftOrderProcess: (req, integration, chatIntegration) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_DRAFT_ORDER, {
      id: chatIntegration.vData.getDraftOrderId()
    })
  },

  isOpenDraftOrder: async (req, chatIntegration, getCartData) => {
    if (getCartData.status !== 'OPEN') {
      const chatIntegrationData = chatIntegration.vData
      chatIntegrationData.deleteDraftOrderId()
      chatIntegration.data = chatIntegrationData.getData()
      chatIntegration.markModified('data')
      await chatIntegration.save()

      throw new createError.BadRequest(req.t('App.errors.integration.cart_isnot_avaliable'))
    }
  },

  GetDraftOrder: async (req, chatIntegration, integration) => {
    const chatIntegrationData = chatIntegration.vData

    let response = {
      currency_code: '',
      payment_option_id: '',
      cargo_option_id: '',
      selected_delivery_address: {},
      selected_invoice_address: {},
      addresses: [],
      cart: {
        id: '',
        additional_cost_fee: 0.00,
        additional_cost_name: '',
        remittance_discount_amount: 0.00,
        remittance_discount_name: '',
        cargo_price: 0.00,
        discount_percentage: 0,
        discount_price: 0.00,
        has_coupon_price: false,
        coupon_price: 0.00,
        has_campaign_price: false,
        campaign_price: 0.00,
        order_note: '',
        items: [],
        campaigns: [],
        sub_total: 0.00,
        total_amount: 0.00,
        name: '',
        invoiceUrl: '',
        tags: '',
      },
      cargo_options: [],
      totalDiscountsSet: {},
      totalShippingPriceSet: {},
      discountCodes: [],
      payment_options: [],
      platformDiscounts: [],
      tax: {},
      shippingLine: {},
      taxLines: []
    }

    // DraftOrderId bilgisi varmı kontrol ediliyor
    if (chatIntegrationData.getDraftOrderId()) {
      const getCartData = await AdminShopifyService.getDraftOrderProcess(req, integration, chatIntegration)
      await AdminShopifyService.isOpenDraftOrder(req, chatIntegration, getCartData)

      if (chatIntegration.ext_id) {
        response.addresses = await AdminShopifyService.GetCustomerAddresses(req, chatIntegration, integration)
      }

      response.selected_delivery_address = Object.keys(getCartData.address).length ? {
        id: getCartData.address.id,
        header: getCartData.address.country + ', ' + getCartData.address.city + ', ' + getCartData.address.address1
      } : {}

      response.selected_invoice_address = Object.keys(getCartData.address).length ? {
        id: getCartData.address.id,
        header: getCartData.address.country + ', ' + getCartData.address.city + ', ' + getCartData.address.address1
      } : {}

      response.cart.items = getCartData.items.map(item => {
        return {
          line_id: item.id,
          product_id: item.product_id,
          title: item.title,
          subtitle: item.subtitle,
          list_price: item.list_price,
          sell_price: item.sell_price,
          count: item.count,
          stock_increment: 1,
          min_order_count: 1,
          image_url: item.image_url,
          stock_unit: item.stock_unit,
          discounted: '',
          variant_id: item.variant_id,
          totalDiscountSet: item.totalDiscountSet,
          discountedTotal: item.discountedTotal,
          originalTotal: item.originalTotal,
          originalTotalSet: item.originalTotalSet,
          discountedUnitPriceSet: item.discountedUnitPriceSet,
          originalUnitPriceSet: item.originalUnitPriceSet,
        }
      })

      response.cart.id = getCartData.id
      response.cart.name = getCartData.name
      response.cart.invoiceUrl = getCartData.invoiceUrl
      response.cart.tags = getCartData.tags
      response.cart.additional_cost_fee = getCartData.tax.presentmentMoney.amount
      response.cart.additional_cost_name = req.t('App.integration.tax')
      response.cart.sub_total = getCartData.sub_total_price
      response.cart.sub_total_price_set = getCartData.sub_total_price_set
      response.cart.total_amount = getCartData.total_price
      response.currency_code = getCartData.currency_code
      response.discountCodes = getCartData.discountCodes
      response.totalDiscountsSet = getCartData.totalDiscountsSet
      response.totalShippingPriceSet = getCartData.totalShippingPriceSet
      response.platformDiscounts = getCartData.platformDiscounts
      response.tax = getCartData.tax
      response.shippingLine = getCartData.shippingLine
      response.taxLines = getCartData.taxLines
      response.payment_option_id = chatIntegrationData.getPaymentOptionId()
      response.cargo_option_id = chatIntegrationData.getCargoOptionId()
    }

    response.payment_options = await AdminShopifyService.GetPaymentOptions(req, integration)
    response.cargo_options = await AdminShopifyService.GetCargoOptions(req, integration)

    return response
  },

  CreateOrder: async (req, chat, integration, chatIntegration) => {
    const isCreditCartId = chatIntegration.vData.getPaymentOptionId() === enums.shopify_credit_cart_id

    let url = null
    if (isCreditCartId) {
      const draftOrder = await AdminShopifyService.getDraftOrderProcess(req, integration, chatIntegration)
      url = draftOrder.invoiceUrl

      // TODO: daha sonra kaldırılabilir
      const chatIntegrationData = chatIntegration.vData
      chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_APPROVE_CART)
      chatIntegrationData.deleteDraftOrderId()
      chatIntegrationData.setNewOrderData({})
      chatIntegrationData.setProductShareCounter(1)
      chatIntegration.data = chatIntegrationData.getData()
      chatIntegration.markModified('data')
      await chatIntegration.save()

      OrderService.AfterOrder(
        req,
        chat,
        integration,
        Number(draftOrder.total_price),
        draftOrder.currency_code,
        draftOrder.id,
        draftOrder.discountCodes,
        draftOrder.items.reduce((a, b) => a + b.count, 0)
      )
    } else {
      const orderId = await AdminShopifyService.OrderCreate(req, chat, integration, chatIntegration)

      const orderData = await AdminShopifyService.GetOrder(req, integration, orderId)

      url = orderData.statusPageUrl
    }

    return UrlShorterService.CreateShortUrl(url, req.trace_id)
  },

  CreateDraftOrder: async (req, chat, chatIntegration, integration, data) => {
    const createdDraftOrder = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_CREATE_DRAFT_ORDER, {
      input: {
        email: chat.email || undefined,
        lineItems: [data],
        purchasingEntity: {
          customerId: chatIntegration.ext_id
        },
        tags: ['HeloRobo']
      }
    })

    const chatIntegrationData = chatIntegration.vData
    chatIntegrationData.setDraftOrderId(createdDraftOrder.data.id)
    chatIntegration.data = chatIntegrationData.getData()
    chatIntegration.markModified('data')
    await chatIntegration.save()
  },

  AddToCart: async (req, chat, chatIntegration, integration, data) => {
    const getProduct = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_PRODUCTS_BY_ID, {
      id: data.product_id
    })

    if (!chatIntegration.vData.getDraftOrderId()) {
      if (!chatIntegration.ext_id) {
        await AdminShopifyService.CreateCustomer(req, chat, chatIntegration, integration, chat.note)
      }

      await AdminShopifyService.CreateDraftOrder(req, chat, chatIntegration, integration, {
        quantity: data.quantity,
        variantId: data.variant_id
      })
    } else {
      const cart = await AdminShopifyService.getDraftOrderProcess(req, integration, chatIntegration)
      await AdminShopifyService.isOpenDraftOrder(req, chatIntegration, cart)

      const newCart = cart.items.map(item => {
        return {
          quantity: item.count,
          variantId: item.variant_id
        }
      })

      const hasSameProductIndex = newCart.findIndex(a => a.variantId === data.variant_id)
      if (hasSameProductIndex !== -1) {
        newCart[hasSameProductIndex].quantity = newCart[hasSameProductIndex].quantity + data.quantity
      } else {
        newCart.push({
          quantity: data.quantity,
          variantId: data.variant_id
        })
      }

      await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_UPDATE_DRAFT_ORDER, {
        id: chatIntegration.vData.getDraftOrderId(),
        input: {
          lineItems: newCart
        }
      })
    }

    const selectedVariant = getProduct.variant_data.variants.find(a => a.id === data.variant_id)

    return {
      item_count_in_cart: data.quantity,
      item_name: `${getProduct.title} ` + (selectedVariant.title === 'Default Title' ? '' : `(${selectedVariant.title})`)
    }
  },

  GetCustomer: async (req, chatIntegration, integration) => {
    return await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_CUSTOMER, {
      id: chatIntegration.ext_id
    })
  },

  CreateCustomer: async (req, chat, chatIntegration, integration, customerNote) => {
    const getName = helpers.getCustomerName(chat.title)

    const createdCustomer = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_CREATE_CUSTOMER, {
      input: {
        email: chat.email || undefined,
        firstName: getName.first_name,
        lastName: getName.last_name,
        note: customerNote,
        tags: ['HeloRobo']
      }
    })

    chatIntegration.ext_id = createdCustomer.data.customer.id
    await chatIntegration.save()

    const agent = await User.findById(chat.owner_user_id)
    if (agent) {
      await QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.CUSTOMER_PAIRED,
        socket_rooms: [agent.vSocketCode],
        data: {
          chat_id: chat.id
        }
      }, req.language)
    }
  },

  UpdateCustomer: async (req, chat, chatIntegration, integration, customerNote) => {
    const getName = helpers.getCustomerName(chat.title)

    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_UPDATE_CUSTOMER, {
      input: {
        id: chatIntegration.ext_id,
        email: chat.email || undefined,
        firstName: getName.first_name,
        lastName: getName.last_name,
        note: customerNote,
        phone: chat.phone_number || undefined
      }
    })
  },

  CreateCustomerAddress: async (req, chatIntegration, integration, addressData) => {
    const customerAddress = await AdminShopifyService.GetCustomerAddresses(req, chatIntegration, integration)

    customerAddress.push(addressData)

    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_UPDATE_CUSTOMER, {
      input: {
        id: chatIntegration.ext_id,
        addresses: customerAddress
      }
    })
  },

  SelectAddressForDraftOrder: async (req, chatIntegration, integration, addressData) => {
    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_UPDATE_DRAFT_ORDER, {
      id: chatIntegration.vData.getDraftOrderId(),
      input: {
        shippingAddress: addressData,
        billingAddress: addressData,
      }
    })
  },

  GetProducts: (req, integration, data = {}) => {
    if (!data.after) {
      delete data.after
    }

    if (data.q) {
      data.query = data.q
    }

    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_PRODUCTS, data)
  },

  GetProductsByIds: (req, integration, ids) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_PRODUCTS_BY_IDS, {
      ids: ids
    })
  },

  GetProductsByCategoryId: (req, integration, data = {}) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_PRODUCTS_FOR_CATALOG_FILTER, data)
  },

  EmptyCart: async (req, integration, chatIntegration) => {
    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_DELETE_DRAFT_ORDER, {
      id: chatIntegration.vData.getDraftOrderId()
    })

    const chatIntegrationData = chatIntegration.vData
    chatIntegrationData.deleteDraftOrderId()
    chatIntegration.data = chatIntegrationData.getData()
    chatIntegration.markModified('data')
    await chatIntegration.save()
  },

  GetCustomerAddresses: async (req, chatIntegration, integration) => {
    const customer = await AdminShopifyService.GetCustomer(req, chatIntegration, integration)

    return customer.data.addresses
  },

  GetSelectedCustomerAddress: async (req, chatIntegration, integration, addressId) => {
    const customer = await AdminShopifyService.GetCustomer(req, chatIntegration, integration)

    return customer.data.addresses.find(a => a.id === addressId)
  },

  UpdateCustomerAddress: async (req, chatIntegration, integration, addressId, data) => {
    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_UPDATE_CUSTOMER, {
      input: {
        id: chatIntegration.ext_id,
        addresses: {
          id: addressId,
          ...data
        }
      }
    })

    // TODO: belki daha sonra draftorder a otomatik olarak atama yapmak gerekebilir
  },

  DeleteAddress: async (req, chatIntegration, integration, addressId) => {
    const customerAddress = await AdminShopifyService.GetSelectedCustomerAddress(req, chatIntegration, integration, addressId)

    delete customerAddress.id

    // aynı adresi idsiz gönderince siliyor. o yüzden adres bilgisini tekrar gönderiyoruz
    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_UPDATE_CUSTOMER, {
      input: {
        id: chatIntegration.ext_id,
        addresses: customerAddress
      }
    })
  },

  getAddCustomerFormFields: async (req, chat) => {
    const { first_name, last_name } = helpers.getCustomerName(chat.title)

    return {
      fields: [
        {
          title: req.t('Global.form_field.account_info'),
          fields: [
            {
              title: req.t('Global.form_field.first_name'),
              field_type: 'text',
              field_key: 'first_name',
              field_name: 'first_name',
              required: true,
              default: first_name
            },
            {
              title: req.t('Global.form_field.last_name'),
              field_type: 'text',
              field_key: 'last_name',
              field_name: 'last_name',
              required: true,
              default: last_name
            },
            {
              title: req.t('Global.form_field.email'),
              field_type: 'text',
              field_key: 'email',
              field_name: 'email',
              required: false,
              default: chat.email || ''
            },
            {
              title: req.t('Global.form_field.phone'),
              field_type: 'text',
              field_key: 'phone',
              field_name: 'phone',
              required: false,
              default: chat.phone_number || ''
            }
          ]
        },
      ]
    }
  },

  getAddAddressFormFields: (req) => {
    return {
      fields: [
        {
          title: req.t('Global.form_field.first_name'),
          field_type: 'text',
          field_key: 'firstName',
          field_name: 'firstName',
          required: true,
          default: ''
        },
        {
          title: req.t('Global.form_field.last_name'),
          field_type: 'text',
          field_key: 'lastName',
          field_name: 'lastName',
          required: true,
          default: ''
        },
        {
          title: req.t('Global.form_field.address1'),
          field_type: 'textarea',
          field_key: 'address1',
          field_name: 'address1',
          required: true,
          default: ''
        },
        {
          title: req.t('Global.form_field.phone'),
          field_type: 'text',
          field_key: 'phone',
          field_name: 'phone',
          required: true,
          default: ''
        },
        {
          title: req.t('Global.form_field.country'),
          field_type: 'select',
          field_key: 'country',
          field_name: 'country',
          required: true,
          default: []
        },
        {
          title: req.t('Global.form_field.city'),
          field_type: 'text',
          field_key: 'city',
          field_name: 'city',
          required: true,
          default: ''
        },
        {
          title: req.t('Global.form_field.province'),
          field_type: 'text',
          field_key: 'province',
          field_name: 'province',
          required: true,
          default: ''
        },
        {
          title: req.t('Global.form_field.zip'),
          field_type: 'text',
          field_key: 'zip',
          field_name: 'zip',
          required: false,
          default: ''
        },
      ],
      countries: require('../../libs/json/countries.json')
    }
  },

  GetAddressFromFields: async (req, chatIntegration, integration, addressId) => {
    const getAddress = await AdminShopifyService.GetSelectedCustomerAddress(req, chatIntegration, integration, addressId)

    const formData = AdminShopifyService.getAddAddressFormFields(req)

    formData.fields.forEach(item => {

      switch (item.field_key) {
        case 'address1':
          item.default = getAddress.address1
          break
        case 'city':
          item.default = getAddress.city
          break
        case 'country':
          item.default = getAddress.country
          break
        case 'firstName':
          item.default = getAddress.firstName
          break
        case 'lastName':
          item.default = getAddress.lastName
          break
        case 'phone':
          item.default = getAddress.phone
          break
        case 'province':
          item.default = getAddress.province
          break
        case 'zip':
          item.default = getAddress.zip
          break

        default:
          break
      }
    })

    return {
      fields: formData.fields,
      countries: formData.countries
    }
  },

  CustomerFilterByQuery: async (req, integration, query) => {
    const users = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_CUSTOMERS, {
      query: query
    })

    return users.data
  },

  GetCustomerOrders: async (req, chatIntegration, integration) => {
    const users = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_CUSTOMER_ORDERS, {
      id: chatIntegration.ext_id
    })

    return users.data
  },

  GetCartCaption: async (req, integration, chatIntegration, isRequiresApprove = true, chat) => {

    if (!chatIntegration.vData.getDraftOrderId()) {
      throw new createError.BadRequest(req.t('App.errors.integration.empty_cart'))
    }

    // Shopify tarafından draftorder bilgileri alınıyor
    const getCartData = await AdminShopifyService.getDraftOrderProcess(req, integration, chatIntegration)

    // sepet bilgileri mesajda gösterilmesi için formatlanıyor
    const cartMessage = getCartData.items.map(item => {
      return req.t('App.integration.send_cart_message.item', {
        emoji: "➡️",
        variant_message: item.subtitle === 'Default Title' ? 'Default' : item.subtitle,
        title: item.title,
        count: item.count,
        price: `${item.list_price} ${getCartData.currency_code}`,
        amount: `${item.sell_price} ${getCartData.currency_code}`,
        interpolation: { escapeValue: false },
        lng: chat.vData.getChatLangCode()
      })
    })

    const seperatedCart = {
      carts: [],
      cart_content: '',
      cart_data: getCartData,
    }
    if (cartMessage.length > 4) {
      for (let index = 0; index < cartMessage.length; index += 4) {
        seperatedCart.carts.push(cartMessage.slice(index, index + 4))
        seperatedCart.cart_content = ShopifyService.__getCartContentData(req, getCartData, isRequiresApprove, 2, true, chat)
      }
    } else {
      seperatedCart.carts.push(cartMessage)
      seperatedCart.cart_content = ShopifyService.__getCartContentData(req, getCartData, isRequiresApprove, 2, false, chat)
    }

    return seperatedCart
  },

  RemoveItemFromCart: async (req, integration, chatIntegration, lineItemId) => {
    const cart = await AdminShopifyService.getDraftOrderProcess(req, integration, chatIntegration)
    await AdminShopifyService.isOpenDraftOrder(req, chatIntegration, cart)

    const itemIndex = cart.items.findIndex(a => a.id === lineItemId)
    if (itemIndex === -1) {
      throw new createError.NotFound(req.t('App.errors.integration.product_not_found'))
    }

    const cartItems = [...cart.items]

    cart.items.splice(itemIndex, 1)

    const newCart = cart.items.map(a => {
      return {
        variantId: a.variant_id,
        quantity: a.count
      }
    })

    // ürün kalmadı ise sepeti siliyoruz.
    if (newCart.length === 0) {
      await AdminShopifyService.EmptyCart(req, integration, chatIntegration)

      return {
        product_name: cartItems[0].title,
        item_count_in_cart: 0
      }
    } else {
      await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_UPDATE_DRAFT_ORDER, {
        id: chatIntegration.vData.getDraftOrderId(),
        input: {
          lineItems: newCart
        }
      })

      return {
        product_name: cart.items[itemIndex].title,
        item_count_in_cart: 0
      }
    }
  },

  UpdateAddressDraftOrder: async (req, chatIntegration, integration, addressData) => {
    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_UPDATE_DRAFT_ORDER, {
      id: chatIntegration.vData.getDraftOrderId(),
      input: {
        billingAddress: addressData,
        shippingAddress: addressData
      }
    })
  },

  UpdateItemFromCart: async (req, chatIntegration, integration, lineItemId, quantity) => {
    const cart = await AdminShopifyService.getDraftOrderProcess(req, integration, chatIntegration)
    await AdminShopifyService.isOpenDraftOrder(req, chatIntegration, cart)

    const itemIndex = cart.items.findIndex(a => a.id === lineItemId)
    if (itemIndex === -1) {
      throw new createError.NotFound(req.t('App.errors.integration.product_not_found'))
    }

    const newCart = cart.items.map(a => {
      let count = a.count
      if (lineItemId === a.id) {
        count = quantity
      }

      return {
        variantId: a.variant_id,
        quantity: count
      }
    })

    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_UPDATE_DRAFT_ORDER, {
      id: chatIntegration.vData.getDraftOrderId(),
      input: {
        lineItems: newCart
      }
    })

    return {
      product_name: cart.items[itemIndex].title,
      item_count_in_cart: quantity
    }
  },

  GetProduct: (req, integration, productId) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_PRODUCTS_BY_ID, {
      id: productId
    })
  },

  ProductShareFromCart: async (req, integration, chatIntegration, lineId) => {
    const cart = await AdminShopifyService.getDraftOrderProcess(req, integration, chatIntegration)

    const cartItem = cart.items.find(a => a.id === lineId)
    if (!cartItem) {
      throw new createError.NotFound(req.t('App.errors.integration.product_not_found'))
    }

    // Mesaj gönderimi için gereli datalar hazırlandı
    return {
      caption: {
        product_title: cartItem.title,
        base_url: cartItem.store_url,
        product_variant: IntegrationService.getVariantForShopify(cartItem.variant_data),
        product_share_counter: chatIntegration.vData.getProductShareCounter(),
        interpolation: { escapeValue: false },
      },
      productItem: cartItem,
      product_id: cartItem.product_id,
      image: cartItem.image_url
    }
  },


  GetCatalog: async (req, integration, data = {}) => {
    const catalogs = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_CATALOG)

    let getProducts
    if (data.category_id) {
      getProducts = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_PRODUCTS_FOR_CATALOG_FILTER, { id: data.category_id })
    } else {
      getProducts = await AdminShopifyService.GetProductsByFilter(req, integration, { query: data.query, after: data.after })
    }

    return {
      filter_options: catalogs.data,
      items: getProducts.items,
      pagination_info: getProducts.pagination_info
    }
  },

  GetProductsByFilter: async (req, integration, data = {}) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_PRODUCTS, {
      query: data.query,
      after: data.after
    })
  },

  WebhookSub: async (integration) => {
    for (const topic of enums.shopify_order_webhook_subscription_topics) {
      const config = {
        url: `https://${integration.vData.getStoreName()}.myshopify.com/admin/api/${integration.vData.getApiVersion() || enums.shopify_admin_api_default_version}/graphql.json`,
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Access-Control-Origin': '*',
          'X-Shopify-Access-Token': integration.vData.getAdminAccessToken(),
          'Content-Type': 'application/json'
        },
        data: JSON.stringify({
          query: `mutation webhookSubscriptionCreate($url: String, $topic: WebhookSubscriptionTopic!) {
          webhookSubscriptionCreate(
            topic: $topic
            webhookSubscription: {callbackUrl: $url, format: JSON}
          ) {
            userErrors {
              field
              message
            }
          }
        }`,
          variables: {
            url: `${process.env.WEBHOOK_URL}/webhook/shopify`,
            topic: topic
          }
        })
      }

      await axios.request(config).catch(() => false)
    }
  },

  GetSummaryMessage: async (req, getCartData, channelType, title, agentId, chat, integration) => {

    // mesaj bilgileri toplanıyor
    const messageData = {
      cart_content: getCartData.carts.join('[BR][/BR]'),
      username: title,
      address: getCartData.cart_data.address.address1 + ', ' + getCartData.cart_data.address.city + ', ' + getCartData.cart_data.address.country,
      total_amount: `${getCartData.cart_data.total_price} ${getCartData.cart_data.currency_code}`,
      tax: getCartData.cart_data.tax.presentmentMoney.amount,
      cargo_option_fee: `0 ${getCartData.cart_data.currency_code}`,
      discounts: '',
      interpolation: { escapeValue: false }
    }

    // kargo fiyatı mesaja ekleniyor
    if (getCartData.cart_data.totalShippingPriceSet.presentmentMoney) {
      if (Number(getCartData.cart_data.totalShippingPriceSet.presentmentMoney.amount) > 0) {
        messageData.cargo_option_fee = `${getCartData.cart_data.totalShippingPriceSet.presentmentMoney.amount} ${getCartData.cart_data.totalShippingPriceSet.presentmentMoney.currencyCode}`
      }
    }

    const discountCodes = []
    if (getCartData.cart_data.platformDiscounts.length > 0) {
      discountCodes.push(`[B]${req.t('App.integration.discounts')} :[/B]`)

      for (const item of getCartData.cart_data.platformDiscounts) {
        discountCodes.push(`- ${item.code}: -${item.totalAmount.amount} ${item.totalAmount.currencyCode}`)
      }

      messageData.discounts = discountCodes.join('[BR][/BR]')
      messageData.discounts += '[BR][/BR][BR][/BR]'
    }

    // kanal tipine göre mesaj gönderme formtı ayarlanıyor
    let messageType
    let messageContent

    switch (channelType) {

      case enums.channel_types.WHATSAPP_NUMBER:

        messageType = enums.message_types.WHATSAPP_INTERACTIVE
        messageContent = {
          caption: req.t('App.integration.shopify_whatsapp_order_summary_message', messageData),
          sub_type: enums.message_types.BUTTON,
          header: {
            type: 'image',
            image: {
              link: integration.vData.getBasketSummaryMessageImageUrl() || enums.basket_summary_message_image_url
            }
          },
          buttons: [{
            type: 'reply',
            reply: {
              id: utils.generateHash(15),
              title: req.t('Global.chat_message.confirm')
            }
          }]
        }
        break

      case enums.channel_types.INSTAGRAM_ACCOUNT:

        messageType = enums.message_types.INSTAGRAM_GENERIC_BUTTON
        messageContent = {
          image_url: integration.vData.getBasketSummaryMessageImageUrl() || enums.basket_summary_message_image_url,
          default_action: {
            type: "web_url",
            url: integration.vData.getBasketSummaryMessageImageUrl() || enums.basket_summary_message_image_url,
          },
          title: req.t('Global.chat_message.cart'),
          subtitle: req.t('App.integration.shopify_whatsapp_order_summary_message', messageData),
          buttons: [
            {
              type: "postback",
              title: req.t('Global.chat_message.confirm'),
              payload: req.t('Global.chat_message.confirm')
            }
          ]
        }
        break

      case enums.channel_types.LIVE_CHAT:
        messageType = enums.message_types.LIVECHAT_BUTTON
        messageContent = {
          url: integration.vData.getBasketSummaryMessageImageUrl() || enums.basket_summary_message_image_url,
          bb_code: true,
          buttons: [
            {
              id: req.t('Global.chat_message.confirm', { lng: chat.vData.getChatLangCode() }),
              text: req.t('Global.chat_message.confirm', { lng: chat.vData.getChatLangCode() })
            }
          ],
          sub_type: enums.message_types.IMAGE_URL,
          caption: helpers.getBbCodeProviderParser(messageData)
        }
        break

      default:

        messageType = enums.message_types.TEXT
        messageContent = {
          text: req.t('App.integration.shopify_order_summary_message', messageData),
          bb_code: true
        }

        break

    }

    // lazım olan ek bilgileri dahil edelim
    messageContent.next_action = enums.SHOPIFY_BOT_MESSAGE_ACTIONS.CREATE_ORDER
    messageContent.agent_id = agentId
    messageContent.language = req.language
    messageContent.bb_code = true

    return [{
      message_data: messageContent,
      message_type: messageType
    }]
  },

  GetDiscountCodes: async (req, integration) => {
    const codesData = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_DISCOUNT_CODES)

    return codesData.data
  },

  SetDiscountCode: async (req, integration, chatIntegration, code) => {
    const cart = await AdminShopifyService.getDraftOrderProcess(req, integration, chatIntegration)
    await AdminShopifyService.isOpenDraftOrder(req, chatIntegration, cart)

    cart.discountCodes.push(code)

    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_UPDATE_DRAFT_ORDER, {
      id: chatIntegration.vData.getDraftOrderId(),
      input: {
        discountCodes: cart.discountCodes
      }
    })
  },

  RemoveDiscountCode: async (req, integration, chatIntegration, code) => {
    const cart = await AdminShopifyService.getDraftOrderProcess(req, integration, chatIntegration)
    await AdminShopifyService.isOpenDraftOrder(req, chatIntegration, cart)

    if (cart.discountCodes.length === 0) {
      return
    }

    const discountIndex = cart.discountCodes.findIndex(a => a === code)
    if (discountIndex === -1) {
      return
    }

    cart.discountCodes.splice(discountIndex, 1)

    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_UPDATE_DRAFT_ORDER, {
      id: chatIntegration.vData.getDraftOrderId(),
      input: {
        discountCodes: cart.discountCodes
      }
    })

    return true
  },

  OrderCreateWebhook: async (req, storeName, customerId, totalPrice, currencyCode, orderId, discountCodes, lineItems) => {
    const integration = await Integration.findOne({ 'data.store_name': storeName.split('.')[0], deleted_at: { $exists: false } })
    if (!integration) {
      return
    }

    const chatIntegration = await ChatIntegration.findOne({
      integration_id: integration._id,
      ext_id: customerId
    }).populate('chat_id')
    if (!chatIntegration) {
      return
    }

    if (chatIntegration.vData.getShopifyLastOrderId() === orderId) {
      return
    }

    const channel = await Channel.findById(chatIntegration.chat.channel_id)

    const sendable = await ChatService.checkSendable(chatIntegration.chat._id, channel.type)
    if (sendable.sendable) {
      const order = await AdminShopifyService.GetOrder(req, integration, orderId)

      const messageData = await AdminShopifyService.OrderCreateMessage(req, channel.type, order.statusPageUrl, integration)

      await ChatService.addSystemMessage(req, chatIntegration.chat._id, messageData.message_type, messageData.message_content, {
        mark_as_seen_event: true,
        tag: sendable.isTag ? enums.message_types.HUMAN_AGENT : false
      })
    }

    const chatIntegrationData = chatIntegration.vData
    chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_APPROVE_CART)
    chatIntegrationData.deleteDraftOrderId()
    chatIntegrationData.setShopifyLastOrderId(orderId)
    chatIntegrationData.setNewOrderData({})
    chatIntegrationData.setProductShareCounter(1)
    chatIntegration.data = chatIntegrationData.getData()
    chatIntegration.markModified('data')
    await chatIntegration.save()

    OrderService.AfterOrder(
      req,
      chatIntegration.chat,
      integration,
      Number(totalPrice),
      currencyCode,
      orderId,
      discountCodes.map(a => a.code),
      lineItems.reduce((a, b) => a + b.quantity, 0)
    )
  },

  SelectDiscountCodes: (platformDiscounts) => {
    let discountCode = undefined

    if (platformDiscounts.length > 0) {
      discountCode = {}

      const allDiscountAmounts = platformDiscounts.filter(a => a.discountClass !== enums.shopify_discount_classes.SHIPPING).reduce((a, b) => a + Number(b.totalAmount.amount), 0)
      const allDiscountCodes = platformDiscounts.map(a => a.code).join(',')

      discountCode.itemFixedDiscountCode = {
        code: allDiscountCodes,
        amountSet: {
          shopMoney: {
            amount: allDiscountAmounts.toString(),
            currencyCode: platformDiscounts[0].totalAmount.currencyCode
          }
        }
      }
    }

    return discountCode
  },

  SelectPaymentOptionForOrder: async (req, integration, chatIntegration, cart) => {
    let transactions = undefined

    if (chatIntegration.vData.getPaymentOptionId() && chatIntegration.vData.getPaymentOptionId() !== enums.shopify_credit_cart_id) {
      const paymentValue = await AdminShopifyService.GetSelectedPaymentOption(req, integration, chatIntegration.vData.getPaymentOptionId())

      transactions = {
        gateway: paymentValue.value,
        amountSet: {
          shopMoney: {
            amount: cart.cart.total_amount,
            currencyCode: cart.currency_code
          }
        },
        status: 'PENDING',
        kind: 'SALE'
      }
    }

    return transactions
  },

  GetOrderShippingLine: (cart) => {
    let amount = cart.shippingLine.originalPrice.amount

    const hasFreeShipping = cart.platformDiscounts.find(a => a.discountClass === enums.shopify_discount_classes.SHIPPING)
    if (hasFreeShipping) {
      amount = '0'
    }

    return {
      priceSet: {
        shopMoney: {
          currencyCode: cart.shippingLine.originalPrice.currencyCode,
          amount: amount
        }
      },
      title: cart.shippingLine.title,
      source: cart.shippingLine.title,
      code: cart.shippingLine.title
    }
  },

  OrderCreate: async (req, chat, integration, chatIntegration) => {
    const cart = await AdminShopifyService.GetDraftOrder(req, chatIntegration, integration)
    if (!cart.shippingLine) {
      throw new createError.BadRequest(req.t('App.errors.integration.once_cargo'))
    }

    const transactions = await AdminShopifyService.SelectPaymentOptionForOrder(req, integration, chatIntegration, cart)

    const selectedAddress = cart.addresses.find(a => a.id === chatIntegration.vData.getDeliveryAddressId())

    delete selectedAddress.id

    const discountCode = AdminShopifyService.SelectDiscountCodes(cart.platformDiscounts)

    const shippingLines = AdminShopifyService.GetOrderShippingLine(cart)

    const order = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_CREATE_ORDER, {
      order: {
        currency: cart.currency_code,
        financialStatus: 'PENDING',
        shippingLines: shippingLines,
        shippingAddress: selectedAddress,
        billingAddress: selectedAddress,
        lineItems: cart.cart.items.map(a => {
          return {
            quantity: a.count,
            productId: a.product_id,
            variantId: a.variant_id,
            requiresShipping: true
          }
        }),
        tags: ['HeloRobo'],
        transactions: transactions,
        customer: {
          toAssociate: {
            id: chatIntegration.ext_id
          }
        },
        discountCode: discountCode
      }
    })

    await AdminShopifyService.EmptyCart(req, integration, chatIntegration)

    const chatIntegrationData = chatIntegration.vData
    chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_APPROVE_CART)
    chatIntegrationData.deleteDraftOrderId()
    chatIntegrationData.setNewOrderData({})
    chatIntegrationData.setShopifyLastOrderId(order.data.id)
    chatIntegrationData.setProductShareCounter(1)
    chatIntegration.data = chatIntegrationData.getData()
    chatIntegration.markModified('data')
    await chatIntegration.save()

    OrderService.AfterOrder(
      req,
      chat,
      integration,
      Number(cart.cart.total_amount),
      cart.currency_code,
      order.data.id,
      cart.discountCodes,
      cart.cart.items.reduce((a, b) => a + b.count, 0)
    )

    return order.data.id
  },

  GetSelectVariant1: (req, integration, data) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_VARIANT1, { id: data.id })
  },

  GetSelectVariant2: (req, integration, data) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_VARIANT2, {
      id: data.id,
      variant1: data.variant1
    })
  },

  GetSelectVariant3: (req, integration, data) => {
    return ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_VARIANT3, {
      id: data.id,
      variant1: data.variant1,
      variant2: data.variant2
    })
  },

  GetCargoOptions: async (req, integration) => {
    const cargos = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_CARGO_OPTIONS)

    return cargos.data.filter(a => a.active)
  },

  GetSelectedCargo: async (req, integration, cargoId) => {
    const cargos = await AdminShopifyService.GetCargoOptions(req, integration)

    return cargos.find(a => a.id === cargoId)
  },

  GetCargoOptionsForMessage: async (req, integration, channelType) => {
    const cargos = []
    const cargoNamesForText = []
    const cargoNamesForWhatsapp = []
    const cargoNamesForLivechat = []

    const cargoOptions = await AdminShopifyService.GetCargoOptions(req, integration)
    if (cargoOptions.length === 0) {
      throw new createError.NotFound(req.t('App.errors.integration.cargos_are_empty'))
    }

    cargoOptions.forEach((cargoOption, index) => {
      cargos.push({
        cargo_option_id: cargoOption.id,
        cargo_index: index + 1
      })

      cargoNamesForText.push(utils.getMessageEmoji(index + 1, channelType) + ' ' + cargoOption.name + ' (' + `${cargoOption.rateProvider.price.amount} ${cargoOption.rateProvider.price.currencyCode}` + ')')

      cargoNamesForWhatsapp.push({
        id: cargoOption.id.toString(),
        title: cargoOption.name.substring(0, 23),
        description: `${cargoOption.rateProvider.price.amount} ${cargoOption.rateProvider.price.currencyCode}`
      })

      cargoNamesForLivechat.push({
        id: cargoOption.id.toString(),
        text: cargoOption.name.substring(0, 23) + " - " + `${cargoOption.rateProvider.price.amount} ${cargoOption.rateProvider.price.currencyCode}`,
      })
    })

    return {
      cargos: cargos,
      cargoNamesForText: cargoNamesForText,
      cargoNamesForWhatsapp: cargoNamesForWhatsapp,
      cargoNamesForLivechat: cargoNamesForLivechat
    }
  },

  SetCargoOption: async (req, integration, chatIntegration, cargoData) => {
    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_UPDATE_DRAFT_ORDER, {
      id: chatIntegration.vData.getDraftOrderId(),
      input: {
        shippingLine: {
          title: cargoData.title,
          price: cargoData.price
        }
      }
    })
  },

  AddTagToDraftOrder: async (req, integration, chatIntegration, tagName) => {
    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_ADD_TAG, {
      id: chatIntegration.vData.getDraftOrderId(),
      tags: [tagName]
    })
  },

  RemoveTagToDraftOrder: async (req, integration, chatIntegration, tagName) => {
    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_REMOVE_TAG, {
      id: chatIntegration.vData.getDraftOrderId(),
      tags: [tagName]
    })
  },

  AddTagToCustomer: async (req, integration, chatIntegration, tagName) => {
    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_ADD_TAG, {
      id: chatIntegration.ext_id,
      tags: [tagName]
    })
  },

  RemoveTagToCustomer: async (req, integration, chatIntegration, tagName) => {
    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_REMOVE_TAG, {
      id: chatIntegration.ext_id,
      tags: [tagName]
    })
  },

  GetPaymentOptions: async (req, integration) => {
    const response = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_PAYMENT_OPTIONS)

    response.data.unshift({
      resourceId: enums.shopify_credit_cart_id,
      translatableContent: [
        {
          value: req.t('App.integration.credit_cart'),
          type: 'SINGLE_LINE_TEXT_FIELD',
          locale: 'tr',
          key: 'name',
          digest: '04cf57cdf98d761e977ca3681604ed536af45d94f65ea8cdef28616e0e0afeb3'
        },
        {
          value: req.t('App.integration.credit_cart'),
          type: 'MULTI_LINE_TEXT_FIELD',
          locale: 'tr',
          key: 'message',
          digest: '55879f5d009a295c106d2eec5dfc921f275f28929c2efbbebacb452a3f1dd9de'
        },
        {
          value: req.t('App.integration.credit_cart'),
          type: 'MULTI_LINE_TEXT_FIELD',
          locale: 'tr',
          key: 'before_payment_instructions',
          digest: '7ae9153b9518f77072c67efc63443bdedfa38db943d2e51b4c096824cb3a7e22'
        }
      ],
      nestedTranslatableResources: {
        nodes: []
      }
    })

    return response.data
  },

  GetPaymentOptionNames: async (req, integration) => {
    const payments = await AdminShopifyService.GetPaymentOptions(req, integration)

    return payments.map(a =>
      a.translatableContent.map(b => {
        if (b.key !== 'name') {
          return false
        }

        return {
          id: a.resourceId,
          name: b.value
        }
      }).filter(b => b !== false)
    ).flat(Infinity)
  },

  GetSelectedPaymentOption: async (req, integration, paymentOption) => {
    const payments = await AdminShopifyService.GetPaymentOptions(req, integration)

    const selectedPayment = payments.find(a => a.resourceId === paymentOption)
    const paymentValue = selectedPayment.translatableContent.find(a => a.key === 'name')
    if (!paymentValue) {
      throw new createError.BadRequest(req.t('App.errors.integration.payment_not_found'))
    }

    return paymentValue
  },

  OrderCreateMessage: async (req, channelType, url, isCreditCartId, integration) => {
    const shortUrl = await UrlShorterService.CreateShortUrl(url, req.trace_id)

    const customerMessage = isCreditCartId ? 'App.success.integration.shopify_order_credit_cart_message' : 'App.success.integration.shopify_order_message'
    let messageType = ''
    let messageContent = {}
    switch (channelType) {

      case enums.channel_types.WHATSAPP_NUMBER:
        messageType = enums.message_types.IMAGE_URL

        messageContent = WhatsappApiService.getSendMessageContent({
          caption: req.t(customerMessage, {
            url: shortUrl,
            interpolation: { escapeValue: false }
          }),
          url: integration.vData.getOrderMessageImageUrl() || enums.order_message_image_url
        })
        break

      default:
        messageType = enums.message_types.TEXT
        messageContent = {
          text: req.t(customerMessage, {
            url: shortUrl,
            interpolation: { escapeValue: false }
          }),
          bb_code: true
        }
        break
    }

    return {
      message_type: messageType,
      message_content: messageContent
    }
  },

  GetOrder: async (req, integration, orderId) => {
    const response = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_GET_ORDER, { id: orderId })

    return response.data
  },

  GetHistoryMessage: async (req, channelType, orderData) => {
    const url = await UrlShorterService.CreateShortUrl(orderData.statusPageUrl, req.trace_id)

    const message = req.t('App.integration.send_history_message.order_url', {
      url: url,
      interpolation: { escapeValue: false },
    })

    if (channelType === enums.channel_types.WHATSAPP_NUMBER) {
      return {
        caption: message,
        url: enums.basket_message_image_url,
        bb_code: true
      }
    } else {
      return {
        text: message,
        bb_code: true
      }
    }
  },

  GetPaymentOptionsForMessage: async (req, integration, channelType) => {
    const paymentOptionsTexts = []
    const paymentsForWhatsapp = []
    const paymentsForLiveChat = []
    const payments = []
    let index = 0

    const shopifyPaymentOptions = await AdminShopifyService.GetPaymentOptionNames(req, integration)
    if (shopifyPaymentOptions.length === 0) {
      throw new createError.NotFound(req.t('App.errors.integration.cargos_are_empty'))
    }

    for (const item of shopifyPaymentOptions) {
      paymentOptionsTexts.push(utils.getMessageEmoji(++index, channelType) + ' [B]' + item.name + '[/B]')

      // // Whatsapp list mesaj için
      paymentsForWhatsapp.push({
        id: item.id.toString(),
        title: item.name.substring(0, 23),
        description: ''
      })

      paymentsForLiveChat.push({
        id: item.id.toString(),
        text: item.name.substring(0, 23),
      })

      payments.push({
        payment_id: item.id,
        value: item.name,
        payment_index: index
      })
    }

    return {
      paymentOptionsTexts: paymentOptionsTexts,
      paymentsForWhatsapp: paymentsForWhatsapp,
      paymentsForLiveChat: paymentsForLiveChat,
      payments: payments
    }
  },

  AddScriptToSite: async (req, integration, channelId, data) => {
    const url = await UploadService.uploadFileFromPresignedUrl(faker.random.uuid(), data, 'application/javascript')

    const response = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_ADD_SCRIPT_TAG, { src: url })

    return new LivechatScript({
      company_id: integration.company_id,
      channel_id: channelId,
      data: data,
      shopify_script_tag_id: response.data.id,
      url: url
    }).save()
  },

  UpdateScriptToSite: async (req, integration, channelId, data, variables = {}) => {
    const hasScript = await LivechatScript.findOne({
      channel_id: channelId,
      company_id: integration.company_id,
      deleted_at: {
        $exists: false
      }
    }).sort({ _id: -1 })
    if (hasScript) {
      const codeUrl = new URL(hasScript.url)

      await UploadService.uploadFileFromPresignedUrl(codeUrl.pathname.substring(1), data, 'application/javascript')

      hasScript.variables = variables
      return hasScript.save()
    } else {
      const url = await UploadService.uploadFileFromPresignedUrl(faker.random.uuid(), data, 'application/javascript')

      const response = await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_ADD_SCRIPT_TAG, { src: url })

      return new LivechatScript({
        company_id: integration.company_id,
        channel_id: channelId,
        data: data,
        shopify_script_tag_id: response.data.id,
        url: url,
        variables: variables
      }).save()
    }
  },

  RemoveScriptToSite: async (req, integration, channelId, id) => {
    const hasScript = await LivechatScript.findOne({
      _id: id,
      channel_id: channelId,
      company_id: integration.company_id,
      deleted_at: {
        $exists: false
      }
    })
    if (!hasScript) {
      throw new createError.BadRequest(req.t('App.errors.integration.livechat_not_added'))
    }

    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_REMOVE_SCRIPT_TAG, { id: hasScript.shopify_script_tag_id })

    hasScript.deleted_at = new Date()
    await hasScript.save()
  },

  RemoveCustomerFromDraftOrder: async (req, integration, chatIntegration) => {
    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_UPDATE_DRAFT_ORDER, {
      id: chatIntegration.vData.getDraftOrderId(),
      input: {
        purchasingEntity: null
      }
    })
  },

  AddCustomerToDraftOrder: async (req, integration, chatIntegration) => {
    await ShopifyIntegrationService.process(req, integration, enums.SHOPIFY_ACTIONS.ADMIN_UPDATE_DRAFT_ORDER, {
      id: chatIntegration.vData.getDraftOrderId(),
      input: {
        purchasingEntity: {
          customerId: chatIntegration.ext_id
        }
      }
    })
  },

  ShopifyAppUninstallWebhook: async (domain, traceId) => {
    const shop = await ShopifyInfo.findOne({ domain: domain, status: 'INSTALLED', deleted_at: { $exists: false } }).sort({ _id: -1 })
    if (!shop) {
      return
    }

    // shop.status = 'UNINSTALLED'
    // await shop.save()

    const isCompany = await Company.findById(shop.helorobo_infos.company)
    if (isCompany) {
      isCompany.is_active = false
      await isCompany.save()

      const hasPackage = await CompanyHasPackage.findOne({ company_id: isCompany._id })
      if (hasPackage) {
        if (enums.shopify_free_package_id !== hasPackage.package_id.toString()) {
          hasPackage.deleted_at = new Date()
          await hasPackage.save()

          const isPackage = await Package.findById(enums.shopify_free_package_id)

          const { package } = helpers.packageSettings(isPackage, true, true)

          await new CompanyHasPackage({
            company_id: isCompany._id,
            package_id: package._id,
            started_date: new Date(),
            name: package.name,
            type: package.type,
            description: package.description,
            is_active: true,
            price: package.price,
            data: package.data,
            package_type: package.package_type,
            currency: package.currency,
          }).save()

          await ChannelService.removeCreditChannels(traceId, isCompany._id.toString())
          await ChannelService.passiveChannelsWithoutLiveChat(isCompany._id)

          if (helpers.isModuleTimeOut(package.data.thinker) === true) {
            process.nextTick(() => {
              ThinkerService.CompanyStopFlows(isCompany._id, traceId)
            })
          }

          if (helpers.isModuleTimeOut(package.data.helobot) === true) {
            process.nextTick(() => {
              HeloBotService.CompanyStopBots(isCompany._id, traceId)
            })
          }
        }
      }
    }
  }

}

module.exports = AdminShopifyService
