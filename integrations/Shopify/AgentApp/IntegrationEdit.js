const createError = require('http-errors')

module.exports = async (req, integration) => {

  const integrationData = integration.vData

  if (req.method === 'POST') {

    const perpage = req.body.perpage

    if (!perpage) {
      throw new createError.BadRequest(req.t('App.errors.integration.perpage_not_found'))
    }

    if (parseInt(perpage) > 10 || parseInt(perpage) <= 0) {
      throw new createError.BadRequest(req.t('App.errors.integration.perpage_wrong_value'))
    }

    integrationData.setPerPage(parseInt(perpage))

    integration.data = integrationData.getData()
    integration.markModified('data')

    await integration.save()
    return { success: true }
  }

  const fields = {
    perpage: integrationData.getPerPage(),
  }

  return { fields: fields }
}