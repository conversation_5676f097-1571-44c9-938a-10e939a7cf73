const axios = require('axios')
const moment = require('moment')
const pino = require('pino')()
const jwt = require('jsonwebtoken')

const enums = require('../libs/enums')
const helpers = require('../libs/helpers')

const Package = require('../models/Package')
const BilltekromPayment = require('../models/BilltekromPayment')
const CompanyHasPackage = require('../models/CompanyHasPackage')

const QueueService = require('../services/QueueService')

const ChannelService = require('./ChannelService')
const WhatsappService = require('../integrations/Whatsapp/WhatsappService')

let token = null
let token_expire = null

const BillTekromService = {

  getIframe: async (email, traceId) => {
    try {

      const tokenData = jwt.sign({
        data: {
          email: email,
          platform: "helorobo"
        }
      }, process.env.BILL_TEKROM_TOKEN, {
        algorithm: 'HS256',
        noTimestamp: true
      })

      const config = {
        url: `${process.env.BILL_TEKROM_BASE_URL}/accounts/login-global`,
        method: 'POST',
        data: {
          jwt: tokenData,
          operation: "package_list",
          operation_params: {
            service_id: 113,
            is_main: true
          }
        }
      }

      return await axios.request(config).then(response => response.data)

    } catch (error) {

      pino.info({
        message: 'BillTekromService hatasi => ' + error.message,
        trace_id: traceId,
        email: email,
        timestamp: new Date(),
      })

      throw new Error('BillTekromService hatasi => ' + error.message)
    }
  },

  getAccountServices: async (accountId) => {
    await BillTekromService.login()

    const config = {
      url: `${process.env.BILL_TEKROM_BASE_URL}/account/service?sort=-id&page=1&per_page=100&include=account,service,site,payment,platform,package,createdBy,updatedBy,payments,renewScenario&filter[account_id]=${accountId}`,
      method: 'GET',
      headers: {
        'authorization': 'Bearer ' + token
      }
    }

    return axios.request(config).then(response => response.data.data)
  },

  getPackages: async () => {
    await BillTekromService.login()

    const config = {
      url: `${process.env.BILL_TEKROM_BASE_URL}/package?filter[platform_id]=19&per_page=-1`,
      method: 'GET',
      headers: {
        'authorization': 'Bearer ' + token
      }
    }

    return axios.request(config).then(response => response.data)
  },

  login: async () => {

    if (token_expire && moment().unix() < token_expire) {
      return
    }

    const config = {
      url: `${process.env.BILL_TEKROM_BASE_URL}/login`,
      method: 'POST',
      data: {
        email: process.env.BILL_TEKROM_API_USER,
        password: process.env.BILL_TEKROM_API_USER_PASSWORD
      }
    }

    const response = await axios.request(config).then(response => response.data)

    token = response.data.token
    token_expire = response.data.token_exp
  },

  calcConversationTotalCost: (companyConversationLimit, total_cost, free_conversation_count, conversation_count, analyticCost, profitMultiplier, startDate, endDate, analyticItems) => {
    if (companyConversationLimit > 1000) {
      const allConversationCount = free_conversation_count + conversation_count

      if (allConversationCount > companyConversationLimit) {
        const conversationPrice = analyticCost / conversation_count || 0

        let monthDiff = endDate.diff(startDate, 'months')
        if (monthDiff === 0) {
          monthDiff = 1
        }

        const freeConversationCountForCompany = companyConversationLimit * monthDiff

        // aşım yapılmamış demektir
        if (freeConversationCountForCompany > allConversationCount || monthDiff === 1) {
          total_cost = Number(((allConversationCount - companyConversationLimit) * conversationPrice).toFixed(4))
        } else {
          for (let i = 0; i < monthDiff; i++) {
            const startOfMonth = startDate.add(1, 'months')

            const items = analyticItems.filter(a =>
              a.start >= startOfMonth.unix() &&
              a.end <= endDate.unix() &&
              a.conversation_type !== enums.whatsapp_business_conversation_types.FREE_TIER
            )

            const conversationCount = items.reduce((a, b) => a + b.conversation, 0)

            if (conversationCount > companyConversationLimit) {
              total_cost += (conversationCount - companyConversationLimit) * conversationPrice
            }
          }
        }
      } else {
        total_cost = 0
      }
    } else {
      total_cost = analyticCost
    }

    return total_cost * profitMultiplier
  },

  calcMessageTotalCost: (companyMessageLimit, total_cost, message_count, analyticCost, profitMultiplier, startDate, endDate, analyticItems) => {
    if (message_count > companyMessageLimit) {
      const messagePrice = analyticCost / message_count || 0

      let monthDiff = endDate.diff(startDate, 'months')
      if (monthDiff === 0) {
        monthDiff = 1
      }

      const freeMessageCountForCompany = companyMessageLimit * monthDiff

      // aşım yapılmamış demektir
      if (freeMessageCountForCompany > message_count || monthDiff === 1) {
        total_cost = Number(((message_count - companyMessageLimit) * messagePrice).toFixed(4))
      } else {
        for (let i = 0; i < monthDiff; i++) {
          const startOfMonth = startDate.add(1, 'months')

          const items = analyticItems.filter(a =>
            a.start >= startOfMonth.unix() &&
            a.end <= endDate.unix() &&
            a.pricing_type !== enums.whatsapp_business_message_types.FREE_CUSTOMER_SERVICE &&
            a.pricing_type !== enums.whatsapp_business_message_types.FREE_ENTRY_POINT
          )

          const messageCount = items.reduce((a, b) => a + b.message, 0)

          if (messageCount > companyMessageLimit) {
            total_cost += (messageCount - companyMessageLimit) * messagePrice
          }
        }
      }
    } else {
      total_cost = 0
    }

    return total_cost * profitMultiplier
  },

  GetMonthlyMessageLimit: async (traceId, company, wabaIdsArray) => {
    let total_cost = 0
    let message_count = 0
    let free_message_count = 0
    let total_payment_cost = 0
    let status = false

    let payments = await BilltekromPayment.find({
      company_id: company._id,
      is_used: {
        $exists: false
      },
      deleted_at: { $exists: false }
    }).sort({ _id: 1 })

    for (const item of payments) {
      total_payment_cost += item.price
    }

    // mesaj sayacı bulunan aya göre hesaplanmalı
    const startDate = moment().startOf('months').utc(false)
    const endDate = moment().startOf('months').add(1, 'months').utc(false)

    const analytic = await WhatsappService.GetMessageAnalytics(wabaIdsArray, startDate.unix(), endDate.unix(), traceId)

    message_count = analytic.message_count
    free_message_count = analytic.free_message_count

    // kredi var ise kredi tarihinden itibaren borç hesaplanmalı
    if (payments.length > 0) {
      let startDateAll = moment(payments[0].created_at).startOf('months').utc(false)
      let endDateAll = moment().startOf('days').add(1, 'days').utc(false)

      let analyticAll = await WhatsappService.GetMessageAnalytics(wabaIdsArray, startDateAll.unix(), endDateAll.unix(), traceId)

      // let allPrice = 0
      // for (const item of payments) {
      //   allPrice += item.price

      //   if (analyticAll.cost >= allPrice) {
      //     await BilltekromPayment.updateOne({
      //       _id: item._id
      //     }, {
      //       $set: {
      //         is_used: new Date()
      //       }
      //     })
      //   }
      // }
      // // --- buraya kadar aslında kredinin tamamı genel toplamı aşanları tüketilmiş olarak kabul ediyoruz.
      // // --- burdan itibaren tekrar hesaplama yapıyoruz.

      // total_payment_cost = 0
      // payments = await BilltekromPayment.find({
      //   company_id: company._id,
      //   is_used: {
      //     $exists: false
      //   },
      //   deleted_at: { $exists: false }
      // }).sort({ _id: 1 })

      // for (const item of payments) {
      //   total_payment_cost += item.price
      // }

      // if (payments.length > 0) {
      //   startDateAll = moment(payments[0].created_at).startOf('months').utc(false)
      //   endDateAll = moment().startOf('days').add(1, 'days').utc(false)

      //   analyticAll = await WhatsappService.GetMessageAnalytics(wabaIdsArray, startDateAll.unix(), endDateAll.unix(), traceId)
      // }

      total_cost = BillTekromService.calcMessageTotalCost(
        company.vData.getMonthlyConverstaionLimit(),
        total_cost,
        analyticAll.message_count,
        analyticAll.cost,
        company.vData.getWhatsappProfitMultiplierForCost(),
        startDateAll,
        endDateAll,
        analyticAll.analytic_items
      )

      status = (total_payment_cost - total_cost) <= 0
      if (status) {
        // eğer kredi yoksa otomatik ödeme yöntemi silinmesi gerek
        process.nextTick(() => {
          ChannelService.removeCreditChannels(traceId, company.id)
        })
      } else {
        // eğer kredi varsa otomatik ödeme yöntemi eklemesi yapılması gerek
        process.nextTick(() => {
          ChannelService.addCreditChannels(traceId, company.id)
        })
      }
    } else {
      total_cost = BillTekromService.calcMessageTotalCost(
        company.vData.getMonthlyConverstaionLimit(),
        total_cost,
        analytic.message_count,
        analytic.cost,
        company.vData.getWhatsappProfitMultiplierForCost(),
        startDate,
        endDate,
        analytic.analytic_items
      )
    }

    return {
      total_cost: total_cost,
      message_count: message_count,
      free_message_count: free_message_count,
      total_payment_cost: total_payment_cost,
      usable: !status
    }
  },

  CheckCreditStatus: async (traceId, company, wabaIdsArray) => {
    let total_cost = 0
    let message_count = 0
    let free_message_count = 0
    let total_payment_cost = 0
    let status = false

    const hasPayments = await BilltekromPayment.find({
      company_id: company._id,
      is_used: { $exists: false },
      deleted_at: { $exists: false }
    }).sort({ _id: 1 })

    if (hasPayments.length > 0) {
      for (const item of hasPayments) {
        total_payment_cost += item.price
      }

      const startDate = moment(hasPayments[0].created_at).startOf('months').utc(false)

      const endDate = moment().startOf('days').add(1, 'days').utc(false)

      const analytic = await WhatsappService.GetMessageAnalytics(wabaIdsArray, startDate.unix(), endDate.unix(), traceId)

      message_count = analytic.message_count
      free_message_count = analytic.free_message_count

      total_cost = BillTekromService.calcMessageTotalCost(
        company.vData.getMonthlyConverstaionLimit(),
        total_cost,
        message_count,
        analytic.cost,
        company.vData.getWhatsappProfitMultiplierForCost(),
        startDate,
        endDate,
        analytic.analytic_items
      )

      status = (total_payment_cost - total_cost) <= 0
      if (status) {
        // eğer kredi yoksa otomatik ödeme yöntemi silinmesi gerek
        process.nextTick(() => {
          ChannelService.removeCreditChannels(traceId, company.id)
        })
      } else {
        for (const item of hasPayments) {
          if (moment(item.created_at).isBefore(moment().startOf('month'))) {
            await BilltekromPayment.updateMany({
              _id: item._id
            }, {
              $set: {
                is_used: new Date()
              }
            })
          }
        }

        // eğer kredi varsa otomatik ödeme yöntemi eklemesi yapılması gerek
        process.nextTick(() => {
          ChannelService.addCreditChannels(traceId, company.id)
        })
      }
    } else {

      const payments = await BilltekromPayment.find({
        company_id: company._id,
        is_used: {
          $gte: moment().startOf('months').utc(false).toDate()
        },
        deleted_at: { $exists: false }
      }).sort({ _id: 1 })

      for (const item of payments) {
        total_payment_cost += item.price
      }

      const startDate = moment().startOf('months').utc(false)
      const endDate = moment().startOf('months').add(1, 'months').utc(false)

      const analytic = await WhatsappService.GetMessageAnalytics(wabaIdsArray, startDate.unix(), endDate.unix(), traceId)

      message_count = analytic.message_count
      free_message_count = analytic.free_message_count

      total_cost = BillTekromService.calcMessageTotalCost(
        company.vData.getMonthlyConverstaionLimit(),
        total_cost,
        message_count,
        analytic.cost,
        company.vData.getWhatsappProfitMultiplierForCost(),
        startDate,
        endDate,
        analytic.analytic_items
      )
    }

    return {
      total_cost: total_cost,
      message_count: message_count,
      free_message_count: free_message_count,
      total_payment_cost: total_payment_cost,
      usable: !status
    }
  },

  getAccountEmail: async (accountId) => {
    await BillTekromService.login()

    const config = {
      url: `${process.env.BILL_TEKROM_BASE_URL}/account?sort=-id&page=1&per_page=15&include=updatedBy,createdBy,mfaMethods,payment,payment.package.service,payment.accountHasService.coupon,payment.site,tags&filter[id]=${accountId}`,
      method: 'GET',
      headers: {
        'authorization': 'Bearer ' + token
      }
    }

    return axios.request(config).then(response => response.data.data)
  },

  SubPackageWebhook: async (body, company, traceId) => {
    if (body.status === 'success' && body.merchant_oid) {
      const hasPayment = await BilltekromPayment.findOne({
        company_id: company._id,
        billtekrom_process_id: body.id,
        merchant_oid: body.merchant_oid
      })
      if (hasPayment) {
        return
      }

      await new BilltekromPayment({
        billtekrom_process_id: body.id,
        merchant_oid: body.merchant_oid,
        company_id: company._id,
        billtekrom_package_id: body.package_id,
        price: helpers.subtractVatFromPrice(Number(body.payment_amount)),
        payment_price: body.payment_amount,
        currency: body.currency === 'USD' ? '$' : '₺'
      }).save()

      process.nextTick(async () => {
        ChannelService.addCreditChannels(traceId, company.id)
      })

      await QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.CONVERSATION_LIMIT_UPDATED,
        socket_rooms: [company.vSocketCode],
        data: {}
      }, 'tr')

      return
    }

    // hizmet silinmiş demektir
    if (body.deleted_at) {
      await BilltekromPayment.findOneAndUpdate({
        billtekrom_process_id: body.id,
        company_id: company._id,
        billtekrom_package_id: body.package_id,
        deleted_at: { $exists: false }
      }, {
        $set: {
          deleted_at: new Date()
        }
      })

      await QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.BILLTEKROM_PACKAGE_UPDATED,
        socket_rooms: [company.vSocketCode],
        data: {}
      }, 'tr')

      return
    }

    // hizmet güncellenmiş demektir
    if (body.updated_by) {
      await BilltekromPayment.findOneAndUpdate({
        billtekrom_process_id: body.id,
        company_id: company._id,
        billtekrom_package_id: body.package_id,
        deleted_at: { $exists: false }
      }, {
        $set: {
          price: body.price,
          currency: body.currency === 'USD' ? '$' : '₺'
        }
      })

      await QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.BILLTEKROM_PACKAGE_UPDATED,
        socket_rooms: [company.vSocketCode],
        data: {}
      }, 'tr')

      return
    }
  },

  PackageWebhook: async (body, company) => {
    const companyHasPackage = await CompanyHasPackage.findOne({
      company_id: company._id,
      deleted_at: {
        $exists: false
      }
    })

    // hizmet silinmiş demektir
    if (body.deleted_at) {
      if (companyHasPackage && companyHasPackage.billtekrom_package_id === body.package_id) {
        companyHasPackage.deleted_at = new Date()
        await companyHasPackage.save()

        await QueueService.publishToAppSocket({
          event: enums.agent_app_socket_events.BILLTEKROM_PACKAGE_UPDATED,
          socket_rooms: [company.vSocketCode],
          data: {}
        }, 'tr')

        return
      }
    } else if (body.status === 'success' && body.merchant_oid) { // ödeme alınmış ve hizmet başlatılmış demektir
      if (companyHasPackage) {
        companyHasPackage.deleted_at = new Date()
        await companyHasPackage.save()
      }

      const isPackage = await Package.findOne({
        billtekrom_package_id: body.package_id,
        deleted_at: {
          $exists: false
        }
      })
      if (!isPackage) {
        return
      }

      const { package, period } = helpers.packageSettings(isPackage)

      await new CompanyHasPackage({
        company_id: company._id,
        package_id: isPackage._id,
        type: package.type,
        name: package.name,
        description: package.description,
        is_active: package.is_active,
        price: package.price,
        data: {
          ...package.data,
          billtekrom_service_id: body.id,
          monthly_conversation_limit: company.data.package.monthly_conversation_limit,
          agent_settings: company.data.package.agent_settings,
          channel_settings: company.data.package.channel_settings,
        },
        package_type: package.package_type,
        expiry_date: moment(period).subtract(1, 'weeks').toDate(),
        currency: package.currency,
        billtekrom_package_id: package.billtekrom_package_id,
        started_date: new Date(),
        is_demo: false,
        premium: companyHasPackage ? companyHasPackage.premium : false
      }).save()

      await QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.BILLTEKROM_PACKAGE_UPDATED,
        socket_rooms: [company.vSocketCode],
        data: {}
      }, 'tr')

      return
    } else if (body.updated_by) { // hizmet güncellenmiş demektir
      if (companyHasPackage) {
        companyHasPackage.is_active = body.is_active
        companyHasPackage.price = body.price
        companyHasPackage.currency = body.currency === 'USD' ? '$' : '₺'
        companyHasPackage.started_date = moment(body.start_time).toDate()
        companyHasPackage.expiry_date = moment(body.end_time).toDate()

        await companyHasPackage.save()

        await QueueService.publishToAppSocket({
          event: enums.agent_app_socket_events.BILLTEKROM_PACKAGE_UPDATED,
          socket_rooms: [company.vSocketCode],
          data: {}
        }, 'tr')

        return
      }
    } else if (body.created_by) { // hizmet eklendi demektir
      if (!companyHasPackage) {
        const now = moment().unix()
        const startTime = moment(body.start_time).unix()
        const endTime = moment(body.end_time).unix()

        if (now > startTime && now < endTime) {
          const isPackage = await Package.findOne({
            billtekrom_package_id: body.package_id,
            deleted_at: {
              $exists: false
            }
          })
          if (!isPackage) {
            return
          }

          const { package, period } = helpers.packageSettings(isPackage)

          await new CompanyHasPackage({
            company_id: company._id,
            package_id: isPackage._id,
            type: package.type,
            name: package.name,
            description: package.description,
            is_active: package.is_active,
            price: package.price,
            data: {
              ...package.data,
              billtekrom_service_id: body.id,
              monthly_conversation_limit: company.data.package.monthly_conversation_limit,
              agent_settings: company.data.package.agent_settings,
              channel_settings: company.data.package.channel_settings,
            },
            package_type: package.package_type,
            expiry_date: moment(period).subtract(1, 'weeks').toDate(),
            currency: package.currency,
            billtekrom_package_id: package.billtekrom_package_id,
            started_date: new Date(),
            is_demo: body.is_demo
          }).save()

          await QueueService.publishToAppSocket({
            event: enums.agent_app_socket_events.BILLTEKROM_PACKAGE_UPDATED,
            socket_rooms: [company.vSocketCode],
            data: {}
          }, 'tr')

          return
        }
      }
    }
  },

  GetActivePackage: async (billTekromAccountId, companyHasPackage) => {
    const accountServices = await BillTekromService.getAccountServices(billTekromAccountId)

    const service = accountServices.data.find(a => a.package.id === companyHasPackage.billtekrom_package_id)

    if (!service) {
      return false
    }

    const nowService = accountServices.data.find(a =>
      moment().unix() > moment(a.start_time).unix()
      &&
      moment().unix() < moment(a.end_time).unix()
      &&
      enums.billtekrom_package_ids.includes(a.package_id)
      &&
      a.is_active === true
    )

    const debtServices = accountServices.data.filter(a => {
      const hasPayment = a.payments.filter(p =>
        !p.payment_time
        &&
        moment().unix() > moment(p.scheduled_payment_date).unix()
      )

      if (hasPayment.length > 0) {
        return true
      }

      return false
    })

    return {
      service: nowService || {},
      services: debtServices
    }
  }
}

module.exports = BillTekromService
