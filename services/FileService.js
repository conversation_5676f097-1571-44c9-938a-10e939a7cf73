const fs = require('fs'); // For File Stream
const path = require('path')  // Dosyanın Path'inden ismini uzantısını çekmek için bir kütüphane
const http = require('https') // For File Download
const Client = require('ssh2-sftp-client');
const FileType = require('file-type')
const multer = require('multer')

const FileCollection = require('../models/File')

const utils = require('./../libs/utils');
const enums = require('../libs/enums');
const axios = require('axios')

const FileService = {

  /**
   * @param url download url
   *
   * @return {Promise<string>}
   */
  download: url => {

    const folder = 'storage/tmp'
    const filename = utils.generateHash()
    const path = folder + '/' + filename

    return new Promise((resolve, reject) => {

      http.get(url, function (response) {

        const settings = {
          flags: 'w',
          encoding: 'utf8',
          fd: null,
          mode: 0o666,
          autoClose: true
        }

        const file = fs.createWriteStream(path, settings)

        response.pipe(file)

        file.on('finish', function () {

          file.end()

          resolve(path)

        })

      }).on('error', function (err) { // Handle errors

        if (fs.existsSync(path)) {
          fs.unlinkSync(path)
        }

        reject(`Error in file downloading: ${err.message}`)

      })

    })

  },

  /**
   * @param sourcePath
   * @param relativeTargetPath
   *
   * @return {Promise<void>}
   */
  upload: (sourcePath, relativeTargetPath) => {

    if (relativeTargetPath[0] !== '/') {
      relativeTargetPath = '/' + relativeTargetPath
    }

    const remotePath = process.env.STATIC_FTP_PATH + relativeTargetPath

    let sftp = new Client();

    return sftp.connect({
      host: process.env.STATIC_FTP_HOST,
      port: process.env.STATIC_FTP_PORT,
      username: process.env.STATIC_FTP_USER,
      password: process.env.STATIC_FTP_PASS,
    }).then(data => {
      return sftp.fastPut(sourcePath, remotePath)
    }).catch(err => {
      console.log('upload file error');
      console.log(err);
    }).finally(() => {
      return sftp.end()
    });

  },

  /**
   *
   * @param {String} name                               Veritabanına kaydedilirkenki isim
   * @param {String} url                                İndirilecek Url
   * @param {enums.file_ext_types} extType             Dosyanın external tipi bknz:{enums.file_ext_types}
   * @param {String} extId                             Dosyanın external id'si ki bu indirildiği yere göre değişiklik gösterebilir.
   * @param {ObjectId} refEntityId                    Bu dosyanın hangi ObjectId'ye ait olduğu: ek bilgi
   * @param {enums.ref_entity_types} refEntityType    Dosyanın hangi entity tipine bağlı olduğunu bnkz:{enums.ref_entity_types}
   *
   * @returns {Promise<File>}            Bir Promise dönderir ona göre kontorlleri sağlanmalı.
   */
  processFile: (name, url, extType, extId, refEntityId, refEntityType) => {

    return FileService.download(url).then(path => {

      return FileService.createNewFileEntry(path, name, extId, extType, refEntityId, refEntityType).then(fileModel => {

        return FileService.upload(path, fileModel.profileImageDirectory).then(() => {

          if (fs.existsSync(path)) {
            fs.unlinkSync(path)
          }

          return fileModel

        })

      })

    })

  },

  createNewFileEntry: (filepath, name, extId, extType, refEntityId, refEntityType) => {

    return FileType.fromFile(filepath).then(fileTypeResult => {

      const fileDataEntry = new FileCollection()

      fileDataEntry.name = name
      fileDataEntry.hash = utils.generateHash()
      fileDataEntry.size = fs.statSync(filepath).size
      fileDataEntry.mime_type = fileTypeResult.mime
      fileDataEntry.extension = fileTypeResult.ext
      fileDataEntry.ext_id = extId
      fileDataEntry.ext_type = extType
      fileDataEntry.ref_entity_id = refEntityId
      fileDataEntry.ref_entity_type = refEntityType

      return fileDataEntry.save()

    })

  },

  /**
   * @tested true
   *
   * @param dir
   * @param fileName
   * @param data
   *
   * @returns {Promise<string>}
   *
   * @constructor
   */
  createFile: (dir, fileName, data = '') => {

    const filePath = dir + '/' + fileName

    return new Promise((resolve, reject) => {

      return fs.writeFile(filePath, data, (err) => {

        if (err) {
          return reject(err)
        }

        return resolve(filePath)

      })

    })

  },

  /**
   * Yolu verilen dosyayı, verilen yeni yere taşımaya yarayan promise fonksiyonu
   *
   * @param {String} from    Dosya ismi ile yolu verilir. Örneğin logs/mylog.txt
   * @param {String} to    Klasör olarak yol. örneğin ./logs/ NOT:!!! ./ eklenmeli ba
   *
   * @returns {Promise<String>} Dosya yolunu dönderir. Örneğin logs/deneme.txt
   */
  moveFile: (from, to) => {

    return Promise.resolve().then(() => {

      const newTo = to + '/' + path.basename(from)

      fs.renameSync(from, newTo)

      return newTo

    })

  },

  uploadPicture: (req, res, next) => {

    const storage = multer.diskStorage({
      destination: function (req, file, cb) {
        cb(null, 'storage/tmp/')
      },
      filename: function (req, file, cb) {
        cb(null, utils.generateHash(5) + file.originalname)
      }
    })

    const fileFilter = (req, file, cb) => {
      if (file.mimetype === 'image/jpeg' || file.mimetype === 'image/png' || file.mimetype === 'image/jpg') {
        cb(null, true)
      } else {
        cb(null, false)
      }
    }

    const upload = multer({ storage: storage, fileFilter: fileFilter }).single('picture')

    upload(req, res, (err) => {
      next();
    })

  },

  uploadFile: (req, res, next) => {

    const storage = multer.diskStorage({
      destination: function (req, file, cb) {
        cb(null, 'storage/tmp/')
      },
      filename: function (req, file, cb) {
        cb(null, utils.generateHash(5) + file.originalname)
      },

    })

    const allowedFileMimetypes = [
      ...enums.allowed_message_types.file,
      ...enums.allowed_message_types.video,
      ...enums.allowed_message_types.image,
      ...enums.allowed_message_types.audio
    ]

    const fileFilter = (req, file, cb) => {
      if (allowedFileMimetypes.includes(file.mimetype)) {
        cb(null, true)
      } else {
        cb(null, false)
      }
    }

    const upload = multer({
      storage: storage,
      fileFilter: fileFilter,
      limits: { fileSize: 1024 * 1024 * 16 } // 16 MB file limits
    }).single('file')

    upload(req, res, (err) => {
      if (err) {
        return res.status(415).json({
          message: err.message
        })
      } else {
        next()
      }
    })
  },

  uploadPictures: (req, res, next) => {

    var Storage = multer.diskStorage({
      destination: function (req, file, cb) {
        cb(null, 'storage/tmp/')
      },
      filename: function (req, file, cb) {
        cb(null, utils.generateHash(5) + file.originalname)
      }
    })

    var upload = multer({
      storage: Storage
    }).fields([{ name: 'logo', maxCount: 1 }, { name: 'favicon', maxCount: 1 }])

    upload(req, res, (err) => {
      next();
    })
  },

  downloadFileWithoutSaving(url){
    return axios.request({
      url: url,
      method: 'GET',
      responseType: 'arraybuffer'
    })
  }

}

module.exports = FileService
