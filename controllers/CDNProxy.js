const { default: axios } = require('axios')

module.exports = async (req, res, next) => {
  try {
    const fileUrl = req.query.url
    if (!fileUrl) {
      throw new Error('File URL is required')
    }

    const response = await axios.get(fileUrl, { responseType: 'arraybuffer' })

    res.setHeader('Content-Type', response.headers['content-type'])
    res.send(Buffer.from(response.data))
  } catch (error) {
    next(error)
  }
}