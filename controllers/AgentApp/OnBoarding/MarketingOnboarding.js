const createError = require('http-errors')
const pino = require('pino')()

const OnBoardingChannels = require('../../../models/OnBoardingChannels')

const FacebookApiService = require('../../../integrations/Facebook/FacebookApiService')

module.exports = async (req, res, next) => {

	try {

		const code = req.body.code
		const redirectUrl = req.body.redirect_url

		if ( ! code) {
			throw new createError.BadRequest(req.t('Onboarding.errors.short_lived_token_not_found'))
		}
		if ( ! redirectUrl) {
			throw new createError.BadRequest(req.t('Onboarding.errors.redirect_url_not_found'))
		}

		const stateDto = await req.getState()

		let onboarding_channel
		onboarding_channel = await OnBoardingChannels.findOne({company_id: stateDto.getCompanyId()})

		if ( ! onboarding_channel) {
			onboarding_channel = await new OnBoardingChannels({
				company_id: stateDto.getCompanyId()
			}).save()
		}

		const longLivedToken = await FacebookApiService.longLivedTokenFromCodeWithRedirectUrl(code, redirectUrl)

		onboarding_channel.marketing_embedded_info = {...onboarding_channel.marketing_embedded_info,
			temporary_long_lived_token: longLivedToken.access_token
		}
		onboarding_channel.markModified('marketing_embedded_info')
		await onboarding_channel.save()

		// promise.all ile iyileştirilebilir.
		const userInfo = await FacebookApiService.getUserInfo(longLivedToken.access_token)
		const debugToken = await FacebookApiService.debugToken(longLivedToken.access_token)

		const requiredScopes = [
			'ads_management',
			'business_management',
			'pages_read_engagement',
			'pages_manage_metadata',
			'pages_read_user_content',
			// 'pages_manage_ads', // token ile giderken gerek kalmıyor. Ayrıca appimizin yetkisi yok.
			'public_profile'
		]

		const missingScopes = requiredScopes.filter(scope => ! debugToken.data.scopes.includes(scope))
		if (missingScopes.length > 0) {
			throw new createError.BadRequest(req.t('Onboarding.errors.missing_scopes', {scopes: missingScopes.join(', ')}))
		}

		const embeddedInfoData = onboarding_channel.vMarketingEmbeddedInfo
		embeddedInfoData.setRedirectUrl(redirectUrl)
		embeddedInfoData.setCode(code)
		embeddedInfoData.setLongLivedToken(longLivedToken.access_token)
		embeddedInfoData.setIssuedUserId(userInfo.id)
		embeddedInfoData.setIssuedUserName(userInfo.name)
		embeddedInfoData.setIssuedAt(new Date())
		embeddedInfoData.setScopes(debugToken.data.scopes)

		onboarding_channel.marketing_embedded_info = embeddedInfoData.getData()
		onboarding_channel.markModified('marketing_embedded_info')
		await onboarding_channel.save()

		return res.modifiedResponse(200, {success: true})

	} catch (error) {
		next(error)
	}
}
