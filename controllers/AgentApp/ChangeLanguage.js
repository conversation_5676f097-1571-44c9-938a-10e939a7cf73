const Joi = require('joi')

const ChatIntegration = require('../../models/ChatIntegration')
const Chat = require('../../models/Chat')
const createError = require("http-errors");

module.exports = async (req, res, next) => {

  try {

    const schema = Joi.object({
      chat_id: Joi.string().optional().allow(null).error(new Error(req.t('App.errors.integration.chat_id_cannot_be_empty'))),
      data: Joi.object({
        app_lang_code: Joi.string().optional(),
        chat_lang_code: Joi.string().optional(),
        integration_lang_code: Joi.string().optional()
      }).required()
    })
    await schema.validateAsync(req.body)

    const chatId = req.body.chat_id
    const languages = req.body.data

    const stateDto = await req.getState()

    // Agent ekranı için gereken dil ayarları set edilecek
    if (languages.app_lang_code) {
      const userData = stateDto.getUser().vData
      userData.setAppLangCode(languages.app_lang_code)
      stateDto.getUser().data = userData.getData()
      stateDto.getUser().markModified('data')
      await stateDto.getUser().save()
    }

    if (chatId) {
      const chat = await Chat.findOne({
        _id: chatId,
        channel_id: {
          $in: stateDto.getPermittedChannelIds()
        }
      })

      if (!chat) {
        throw new createError.BadRequest(req.t('App.errors.conversation.not_found'))
      }

      if (languages.chat_lang_code) {
        const chatData = chat.vData
        chatData.setChatLangCode(languages.chat_lang_code)
        chat.data = chatData.getData()
        chat.markModified('data')
        await chat.save()
      }

      if (languages.integration_lang_code) {
        const chatIntegration = await ChatIntegration.findOne({ chat_id: chatId })
        if (chatIntegration) {
          // Chat var ise chate dair chat dil bilgileri set edilir
          const chatIntegrationData = chatIntegration.vData

          chatIntegrationData.setLanguages({ integration_lang_code: languages.integration_lang_code })
          chatIntegration.data = chatIntegrationData.getData()
          chatIntegration.markModified('data')
          await chatIntegration.save()
        }
      }
    }

    return res.modifiedResponse(200, { success: true })
  } catch (error) {
    return next(error)
  }

}
