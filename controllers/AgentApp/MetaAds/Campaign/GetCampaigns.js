const createError = require('http-errors')

const MetaAdsChildBms = require('../../../../models/MetaAdsChildBms')

const FacebookAdsApiService = require('../../../../integrations/Facebook/FacebookAdsApiService')

module.exports = async (req, res, next) => {
	try {

		const stateDto = await req.getState()

		const childBmId = req.params.childBmId

		const metaAdsChildBm = await MetaAdsChildBms.findOne({
			_id: childBmId,
			company_id: stateDto.getCompanyId(),
			deleted_at: {$exists: false}
		})

		if ( ! metaAdsChildBm) {
			throw new createError.BadRequest(req.t('App.errors.meta_ads.child_bm_not_found'))
		}

		const campaignResponse = await FacebookAdsApiService.getCampaigns(metaAdsChildBm.ad_account_id)


		return res.modifiedResponse(200, {
			success: true,
			campaigns: campaignResponse.data.data
		})
	} catch (error) {
		next(error)
	}
}
