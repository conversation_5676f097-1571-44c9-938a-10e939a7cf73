const MetaAdsChildBms = require('../../../models/MetaAdsChildBms')
const {Types} = require('mongoose')


module.exports = async (req, res, next) => {
	try {

		const childBmId = req.params.childBmId

		if ( ! childBmId) {
			return res.modifiedResponse(400, {success: false, message: 'Child BM parameter is required'})
		}

		const stateDto = await req.getState()
		const childBm = await MetaAdsChildBms.findOne({
			_id: new Types.ObjectId(childBmId),
			company_id: stateDto.getCompanyId()
		})

		if ( ! childBm) {
			return res.modifiedResponse(400, {success: false, message: 'Child BM not found'})
		}






		return res.modifiedResponse(200, {
			success: true,
			data: {
				access_token: childBm.system_user_access_token,
				ad_account_id: childBm.ad_account_id
			}
		})
	} catch (error) {
		next(error)
	}
}
