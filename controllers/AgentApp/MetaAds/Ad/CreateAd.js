const Joi = require('joi')
const createError = require('http-errors')


const MetaAdsChildBms = require('../../../../models/MetaAdsChildBms')
const {Types} = require('mongoose')
const FacebookAdsApiService = require('../../../../integrations/Facebook/FacebookAdsApiService')


module.exports = async (req, res, next) => {
	try {

		const childBmId = req.params.childBmId

		if ( ! childBmId) {
			throw new createError.BadRequest(req.t('App.errors.meta_ads.child_bm_id_not_found'))
		}


		const schema = Joi.object({
			name: Joi.string().required().error(new Error(req.t('Global.errors.name'))),
			status: Joi.string().required().error(new Error(req.t('Global.errors.status'))),
			adset_id: Joi.string().required().error(new Error(req.t('Global.errors.adset_id'))),
			adcreative_id: Joi.string().required().error(new Error(req.t('Global.errors.creative_id')))
		})

		await schema.validateAsync(req.body)

		const name = req.body.name
		const status = req.body.status
		const adsetId = req.body.adset_id
		const adcreativeId = req.body.adcreative_id


		const stateDto = await req.getState()


		const metaAdsChildBm = await MetaAdsChildBms.findOne({
			_id: new Types.ObjectId(childBmId),
			company_id: stateDto.getCompanyId(),
			deleted_at: {$exists: false}
		})

		if ( ! metaAdsChildBm) {
			throw new createError.BadRequest(req.t('App.errors.meta_ads.child_bm_not_found'))
		}

		await FacebookAdsApiService.createAd({
			name,
			status,
			adsetId,
			adcreativeId,
			adAccountId: metaAdsChildBm.ad_account_id,
			childBMAccessToken: metaAdsChildBm.system_user_access_token
		})


		return res.modifiedResponse(200, {
			success: true
		})

	} catch (error) {
		next(error)
	}
}
