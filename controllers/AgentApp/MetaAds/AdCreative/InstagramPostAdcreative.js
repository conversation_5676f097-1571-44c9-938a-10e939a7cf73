const createError = require('http-errors')

class InstagramPostAdcreative {

	name
	objectId
	instagramUserId
	sourceInstagramMediaId

	constructor({name, objectId, source_instagram_media_id}) {
		this.name = name
		this.objectId = objectId
		this.sourceInstagramMediaId = source_instagram_media_id
	}

	setObjectId(pageId) {
		this.objectId = pageId
	}

	setInstagramUserId(instagramUserId) {
		this.instagramUserId = instagramUserId
	}

	validate(t) {
		if ( ! this.name) {
			throw new createError.BadRequest(t('App.errors.meta_ads.name_not_found'))
		}
		if ( ! this.sourceInstagramMediaId) {
			throw new createError.BadRequest(t('App.errors.meta_ads.source_instagram_media_id_not_found'))
		}
	}

	to<PERSON>son() {
		return {
			name: this.name,
			object_id: this.objectId,
			instagram_user_id: this.instagramUserId,
			source_instagram_media_id: this.sourceInstagramMediaId,
			status: 'PAUSED',
			call_to_action: {
				type: 'INSTAGRAM_MESSAGE',
				value: {
					link: 'https://www.helorobo.com'
				}
			}
		}
	}

}


module.exports = InstagramPostAdcreative
