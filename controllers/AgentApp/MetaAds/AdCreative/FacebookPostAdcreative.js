// Bu Facebook postunu isntagram ads olarak kullanmak için.
class FacebookAdcreative {

	name
	objectStoryId
	instagramUserId

	constructor({name, object_story_id}) {
		this.name = name
		this.objectStoryId = object_story_id
	}

	setInstagramUserId(instagramUserId) {
		this.instagramUserId = instagramUserId
	}

	setObjectId() {
	}

	validate(t) {
		if ( ! this.name) {
			throw new createError.BadRequest(t('App.errors.meta_ads.name_not_found'))
		}
		if ( ! this.objectStoryId) {
			throw new createError.BadRequest(t('App.errors.meta_ads.object_story_id_not_found'))
		}
	}

	toJson() {
		return {
			object_story_id: this.objectStoryId,
			instagram_user_id: this.instagramUserId,
			call_to_action: {
				type: 'MESSAGE_PAGE',
				value: {
					app_destination: 'MESSENGER'
				}
			}
		}
	}

}


module.exports = FacebookAdcreative
