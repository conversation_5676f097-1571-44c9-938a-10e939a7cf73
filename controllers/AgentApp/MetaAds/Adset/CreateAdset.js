const Joi = require('joi')
const createError = require('http-errors')


const MetaAdsChildBms = require('../../../../models/MetaAdsChildBms')
const {Types} = require('mongoose')
const FacebookAdsApiService = require('../../../../integrations/Facebook/FacebookAdsApiService')


module.exports = async (req, res, next) => {
	try {

		const childBmId = req.params.childBmId

		if ( ! childBmId) {
			throw new createError.BadRequest(req.t('App.errors.meta_ads.child_bm_id_not_found'))
		}


		const schema = Joi.object({
			name: Joi.string().required().error(new Error(req.t('Global.errors.name'))),
			status: Joi.string().required().error(new Error(req.t('Global.errors.status'))),
			destination_type: Joi.string().required().error(new Error(req.t('Global.errors.destination_type'))),
			bid_strategy: Joi.string().required().error(new Error(req.t('Global.errors.bid_strategy'))),
			billing_event: Joi.string().required().error(new Error(req.t('Global.errors.billing_event'))),
			daily_budget: Joi.number().required().error(new Error(req.t('Global.errors.daily_budget'))),
			start_time: Joi.number().required().error(new Error(req.t('Global.errors.start_time'))),
			countries: Joi.array().required().error(new Error(req.t('Global.errors.countries'))),
			device_platforms: Joi.array().required().error(new Error(req.t('Global.errors.device_platforms'))),
			optimization_goal: Joi.string().required().error(new Error(req.t('Global.errors.optimization_goal'))),
			campaign_id: Joi.string().required().error(new Error(req.t('Global.errors.campaign_id'))),
			bid_amount: Joi.number().optional().error(new Error(req.t('Global.errors.bid_amount'))) // eğerki bid_strategy lowest_cost_with_bid_cap ise verilecek.
		})

		await schema.validateAsync(req.body)

		const name = req.body.name
		const status = req.body.status
		const destinationType = req.body.destination_type
		const bidStrategy = req.body.bid_strategy
		const billingEvent = req.body.billing_event
		const dailyBudget = req.body.daily_budget
		const startTime = req.body.start_time
		const countries = req.body.countries
		const devicePlatforms = req.body.device_platforms
		const optimizationGoal = req.body.optimization_goal
		const campaignId = req.body.campaign_id
		const bidAmount = req.body.bid_amount

		if(bidStrategy === 'LOWEST_COST_WITH_BID_CAP' && !bidAmount) {
			throw new createError.BadRequest(req.t('Global.errors.bid_amount'))
		}

		const stateDto = await req.getState()
		const metaAdsChildBm = await MetaAdsChildBms.findOne({
			_id: new Types.ObjectId(childBmId),
			company_id: stateDto.getCompanyId(),
			deleted_at: {$exists: false}
		})

		if ( ! metaAdsChildBm) {
			throw new createError.BadRequest(req.t('App.errors.meta_ads.child_bm_not_found'))
		}

		await FacebookAdsApiService.createAdSet({
			adAccountId: metaAdsChildBm.ad_account_id,
			campaignId,
			name,
			status,
			bidStrategy,
			billingEvent,
			dailyBudget,
			bidAmount,
			startTime,
			countries,
			devicePlatforms,
			optimizationGoal,
			pageId: metaAdsChildBm.primary_page_id,
			destinationType,
			childBMAccessToken: metaAdsChildBm.system_user_access_token
		})


		return res.modifiedResponse(200, {
			success: true
		})

	} catch (error) {
		next(error)
	}
}
